# YAML Lint Configuration for RSGlider API
# https://yamllint.readthedocs.io/en/stable/configuration.html

extends: default

rules:
  # Line length - allow longer lines for OpenAPI descriptions
  line-length:
    max: 120
    level: warning

  # Indentation - 2 spaces for YAML
  indentation:
    spaces: 2
    indent-sequences: true
    check-multi-line-strings: false

  # Comments
  comments:
    min-spaces-from-content: 1
    require-starting-space: true

  # Document formatting
  document-start:
    present: false  # Don't require --- at start
  document-end:
    present: false  # Don't require ... at end

  # Empty lines
  empty-lines:
    max: 2
    max-start: 0
    max-end: 1

  # Brackets and braces
  brackets:
    min-spaces-inside: 0
    max-spaces-inside: 1
    min-spaces-inside-empty: 0
    max-spaces-inside-empty: 0

  braces:
    min-spaces-inside: 0
    max-spaces-inside: 1
    min-spaces-inside-empty: 0
    max-spaces-inside-empty: 0

  # Colons
  colons:
    max-spaces-before: 0
    min-spaces-after: 1
    max-spaces-after: 1

  # Commas
  commas:
    max-spaces-before: 0
    min-spaces-after: 1
    max-spaces-after: 1

  # Hyphens
  hyphens:
    max-spaces-after: 1

  # Key duplicates
  key-duplicates: enable

  # Octal values
  octal-values:
    forbid-implicit-octal: true
    forbid-explicit-octal: true

  # Quoted strings
  quoted-strings:
    quote-type: any
    required: only-when-needed

  # Trailing spaces
  trailing-spaces: enable

  # Truthy values
  truthy:
    allowed-values: ['true', 'false', 'yes', 'no']
    check-keys: false

# Ignore patterns
ignore: |
  node_modules/
  dist/
  build/
  .git/
  *.min.yaml
  *.min.yml
