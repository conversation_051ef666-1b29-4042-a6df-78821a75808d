# YAML Validation Configuration for RSGlider API
# Used by yaml-validator and similar tools

# Files to validate
files:
  - "api-docs/openapi.yaml"
  - "api-docs/examples/*.yaml"
  - "api-docs/examples/*.yml"
  - ".github/workflows/*.yml"
  - ".github/workflows/*.yaml"
  - "docker-compose*.yml"
  - "docker-compose*.yaml"

# Validation rules
rules:
  # OpenAPI specific validation
  openapi:
    enabled: true
    version: "3.0.3"
    strict: true

  # YAML syntax validation
  syntax:
    enabled: true
    strict: true

  # Disable problematic path parameter validation
  path-parameters-required: off
  openapi-path-param-required: off
  path-params: off
  operation-parameters: off

  # Schema validation
  schema:
    enabled: true

  # Security validation
  security:
    enabled: true
    check_secrets: true

# Ignore patterns
ignore:
  - "node_modules/**"
  - "dist/**"
  - "build/**"
  - ".git/**"
  - "*.min.yaml"
  - "*.min.yml"
  - "temp/**"
  - "tmp/**"

# Output format
output:
  format: "detailed"
  colors: true

# Error handling
errors:
  fail_on_error: true
  fail_on_warning: false
