# RSGlider API Development Environment
# This file contains safe development defaults

# Application
NODE_ENV=development
PORT=3000

# Database Configuration (Docker)
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_NAME=rsglider
DATABASE_USER=rsglider
DATABASE_PASSWORD=rsglider_dev_password

# Redis Configuration (Docker)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=rsglider_redis_password

# JWT Configuration (Development only - change in production!)
JWT_SECRET=dev_jwt_secret_change_in_production_this_is_not_secure
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# BTCPay Server Configuration (Development)
BTCPAY_SERVER_URL=http://btcpay:49392
BTCPAY_STORE_ID=dev_store_id
BTCPAY_API_KEY=dev_api_key
BTCPAY_WEBHOOK_SECRET=dev_webhook_secret

# Gitea Integration (Development)
GITEA_BASE_URL=https://git.rsglider.com
GITEA_ADMIN_TOKEN=dev_admin_token
GITEA_OIDC_CLIENT_ID=dev_client_id
GITEA_OIDC_CLIENT_SECRET=dev_client_secret

# Email Configuration (Development - use Ethereal or MailHog)
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=dev_password

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=./uploads

# Rate Limiting (Relaxed for development)
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=1000

# Logging
LOG_LEVEL=debug
LOG_FORMAT=dev

# CORS Configuration (Allow all origins in development)
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# Session Configuration
SESSION_SECRET=dev_session_secret_not_secure
SESSION_MAX_AGE=86400000  # 24 hours

# Two-Factor Authentication
TOTP_ISSUER=RSGlider-Dev
TOTP_WINDOW=2

# Payout Configuration
DEFAULT_PAYOUT_FREQUENCY=weekly
MINIMUM_PAYOUT_AMOUNT=1  # Lower for testing
PAYOUT_CURRENCY=USD

# Feature Flags (All enabled for development)
ENABLE_REGISTRATION=true
ENABLE_2FA=true
ENABLE_DEVELOPER_SIGNUP=true
ENABLE_MARKETPLACE=true

# Development Only
DEBUG_SQL=true
ENABLE_SWAGGER=true
SWAGGER_PATH=/api/docs
