{"all": true, "check-coverage": true, "reporter": ["text", "text-summary", "html", "json", "lcov", "clover"], "reports-dir": "coverage", "temp-directory": ".c8_output", "exclude": ["**/*.spec.ts", "**/*.integration.spec.ts", "**/node_modules/**", "**/dist/**", "**/coverage/**", "**/*.d.ts", "**/main.ts", "**/test/**", "**/*.config.ts", "**/*.config.js"], "include": ["src/**/*.ts"], "extension": [".ts"], "cache": true, "clean": true, "skip-full": false, "branches": 60, "lines": 60, "functions": 60, "statements": 60, "per-file": true, "watermarks": {"lines": [60, 80], "functions": [60, 80], "branches": [60, 80], "statements": [60, 80]}}