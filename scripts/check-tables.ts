#!/usr/bin/env tsx

/**
 * Check what tables exist in the database
 */

import { sql } from 'drizzle-orm';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

async function checkTables() {
    console.log('🔍 Checking database tables...');

    // Use same connection as the app
    const connectionString = `postgresql://${process.env.DATABASE_USER || 'rsglider'}:${process.env.DATABASE_PASSWORD || 'rsglider_dev_password'}@${process.env.DATABASE_HOST || 'postgres'}:${process.env.DATABASE_PORT || '5432'}/${process.env.DATABASE_NAME || 'rsglider'}`;

    const client = postgres(connectionString);
    const db = drizzle(client);

    try {
        // Check for application tables
        const tablesResult = await db.execute(sql`
      SELECT table_name, table_type 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);

        console.log('\n📊 Found tables in public schema:');
        if (tablesResult.length === 0) {
            console.log('❌ No application tables found!');
        } else {
            tablesResult.forEach((table: any) => {
                console.log(`  ✅ ${table.table_name}`);
            });
        }

        // Check specifically for email management tables
        const emailTables = ['email_templates', 'email_campaigns', 'email_logs'];
        console.log('\n📧 Email management tables:');
        for (const tableName of emailTables) {
            const exists = tablesResult.find((t: any) => t.table_name === tableName);
            console.log(`  ${exists ? '✅' : '❌'} ${tableName}`);
        }

        // Check for migrations table
        const migrationsResult = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'drizzle' 
      AND table_name = '__drizzle_migrations'
    `);

        console.log('\n🔄 Migration status:');
        if (migrationsResult.length > 0) {
            console.log('  ✅ Drizzle migrations table exists');

            // Check migration history
            const migrationHistory = await db.execute(sql`
        SELECT hash, created_at 
        FROM drizzle.__drizzle_migrations 
        ORDER BY created_at DESC 
        LIMIT 5
      `);

            console.log(`  📜 ${migrationHistory.length} migrations applied`);
            migrationHistory.forEach((migration: any, index: number) => {
                console.log(`    ${index + 1}. ${migration.hash} (${migration.created_at})`);
            });
        } else {
            console.log('  ❌ No migrations applied yet');
        }

    } catch (error) {
        console.error('❌ Database check failed:', error.message);
    } finally {
        await client.end();
    }
}

checkTables().catch(console.error); 