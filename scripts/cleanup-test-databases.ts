#!/usr/bin/env tsx

/**
 * Cleanup script to remove all orphaned test databases
 * Run with: npm run cleanup:test-dbs
 */

import { cleanupOrphanedTestDatabases } from '../test/database-setup.js';

async function main() {
    console.log('🧹 Cleaning up orphaned test databases...');

    try {
        await cleanupOrphanedTestDatabases();
        console.log('✅ Cleanup completed successfully');
    } catch (error) {
        console.error('❌ Cleanup failed:', error.message);
        process.exit(1);
    }
}

main(); 