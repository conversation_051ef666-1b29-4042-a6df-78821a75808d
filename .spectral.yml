# Spectral OpenAPI Linting Configuration for RSGlider API
extends: ["spectral:oas"]

rules:
  # Disable problematic path parameter validation
  path-params: off
  operation-parameters: off

  # Disable operationId warnings (not critical for functionality)
  operation-operationId: off
  operation-operationId-unique: off
  operation-operationId-valid-in-url: off

  # Keep important rules enabled
  operation-description: warn
  operation-tags: error
  tag-description: error
  operation-success-response: error
