/**
 * Jest Test Setup
 * Global test configuration and utilities
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.DATABASE_URL = 'sqlite://test.db';
process.env.REDIS_URL = 'redis://localhost:6379';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods and NestJS Logger in tests to reduce noise
const originalConsole = { ...console };

// Check if debug mode is enabled
const isDebugMode = process.env.TEST_DEBUG === 'true' || process.argv.includes('--debug');

// Create a conditional Logger class
class ConditionalLogger {
  log = isDebugMode ? originalConsole.log : jest.fn();
  error = isDebugMode ? originalConsole.error : jest.fn();
  warn = isDebugMode ? originalConsole.warn : jest.fn();
  debug = isDebugMode ? originalConsole.debug : jest.fn();
  verbose = isDebugMode ? originalConsole.log : jest.fn(); // verbose -> log in debug mode
  setContext = jest.fn();
  localInstance = jest.fn();

  // Static methods
  static log = isDebugMode ? originalConsole.log : jest.fn();
  static error = isDebugMode ? originalConsole.error : jest.fn();
  static warn = isDebugMode ? originalConsole.warn : jest.fn();
  static debug = isDebugMode ? originalConsole.debug : jest.fn();
  static verbose = isDebugMode ? originalConsole.log : jest.fn();
  static overrideLogger = jest.fn();
  static flush = jest.fn();
  static isLevelEnabled = jest.fn().mockReturnValue(true);
  static getTimestamp = jest.fn().mockReturnValue(new Date().toISOString());
}

// Only mock Logger if not in debug mode
if (!isDebugMode) {
  jest.mock('@nestjs/common', () => {
    const actual = jest.requireActual('@nestjs/common');
    return {
      ...actual,
      Logger: ConditionalLogger,
    };
  });
}

beforeEach(() => {
  // Only suppress console output if not in debug mode
  if (!isDebugMode) {
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
    console.debug = jest.fn();
    console.info = jest.fn();
  }
});

afterEach(() => {
  // Only restore console if we mocked it
  if (!isDebugMode) {
    Object.assign(console, originalConsole);
  }
});

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    username: 'testuser',
    isEmailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }),

  createMockRequest: (user?: any) => ({
    user,
    headers: {},
    body: {},
    params: {},
    query: {},
  }),

  createMockResponse: () => {
    const res: any = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.cookie = jest.fn().mockReturnValue(res);
    res.clearCookie = jest.fn().mockReturnValue(res);
    return res;
  },
};

// Export to make this a module
export { };

// Declare global types for TypeScript
declare global {
  var testUtils: {
    createMockUser: () => any;
    createMockRequest: (user?: any) => any;
    createMockResponse: () => any;
  };
}
