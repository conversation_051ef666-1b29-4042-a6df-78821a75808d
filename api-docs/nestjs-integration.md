# NestJS Integration Guide

## 🚀 Setting Up Swagger Documentation

### 1. Install Dependencies
```bash
npm install @nestjs/swagger swagger-ui-express
npm install @nestjs/config class-validator class-transformer
```

### 2. Main Application Setup
```typescript
// src/main.ts
import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('RSGlider API')
    .setDescription('Comprehensive API for RSGlider platform with developer marketplace, BTCPay integration, and session management')
    .setVersion('1.0')
    .addBearerAuth({
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
    }, 'JWT')
    .addApiKey({
      type: 'apiKey',
      name: 'X-API-Key',
      in: 'header',
    }, 'ApiKey')
    .addServer('https://api.rsglider.com', 'Production')
    .addServer('https://api-staging.rsglider.com', 'Staging')
    .addServer('http://localhost:3000', 'Development')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  await app.listen(3000);
}
bootstrap();
```

### 3. DTO Examples with Swagger Decorators

#### Authentication DTOs
```typescript
// src/auth/dto/login.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsString, MinLength, Matches, IsOptional, IsBoolean } from 'class-validator';

export class LoginDto {
  @ApiProperty({
    format: 'email',
    example: '<EMAIL>'
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    minLength: 8,
    example: 'SecurePass123!'
  })
  @IsString()
  @MinLength(8)
  password: string;

  @ApiPropertyOptional({
    pattern: '^[0-9]{6}$',
    example: '123456',
    description: '6-digit TOTP code from authenticator app'
  })
  @IsOptional()
  @Matches(/^[0-9]{6}$/)
  twoFactorCode?: string;

  @ApiPropertyOptional({
    default: false,
    description: 'Remember login for extended session'
  })
  @IsOptional()
  @IsBoolean()
  rememberMe?: boolean = false;
}
```

#### Developer Integration DTOs
```typescript
// src/developers/dto/publish-repository.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsArray, IsEnum, IsOptional, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

export class RepositoryPricingDto {
  @ApiProperty({
    enum: ['free', 'one_time', 'subscription', 'pay_what_you_want'],
    example: 'one_time'
  })
  @IsEnum(['free', 'one_time', 'subscription', 'pay_what_you_want'])
  type: string;

  @ApiPropertyOptional({
    minimum: 0,
    example: 9.99
  })
  @IsOptional()
  basePrice?: number;

  @ApiPropertyOptional({
    enum: ['USD', 'EUR', 'GBP'],
    default: 'USD'
  })
  @IsOptional()
  @IsEnum(['USD', 'EUR', 'GBP'])
  currency?: string = 'USD';
}

export class PublishRepositoryDto {
  @ApiProperty({
    example: 'automation',
    description: 'Marketplace category for the item'
  })
  @IsString()
  category: string;

  @ApiPropertyOptional({
    type: [String],
    example: ['productivity', 'scripts'],
    description: 'Tags for better discoverability'
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    type: RepositoryPricingDto
  })
  @ValidateNested()
  @Type(() => RepositoryPricingDto)
  pricing: RepositoryPricingDto;

  @ApiPropertyOptional({
    enum: ['public', 'private'],
    default: 'public'
  })
  @IsOptional()
  @IsEnum(['public', 'private'])
  visibility?: string = 'public';

  @ApiPropertyOptional({
    default: false,
    description: 'Request featured placement (admin approval required)'
  })
  @IsOptional()
  @IsBoolean()
  featured?: boolean = false;
}
```

### 4. Controller Examples

#### Authentication Controller
```typescript
// src/auth/auth.controller.ts
import { Controller, Post, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiSecurity } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { AuthResponseDto } from './dto/auth-response.dto';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Login user',
    description: 'Authenticate user with email/password and optional 2FA code'
  })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    type: AuthResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid credentials or 2FA code required',
  })
  @ApiResponse({
    status: 423,
    description: 'Account locked due to failed attempts',
  })
  async login(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
    return this.authService.login(loginDto);
  }
}
```

#### Developer Controller
```typescript
// src/developers/developers.controller.ts
import { Controller, Get, Post, Param, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { DevelopersService } from './developers.service';
import { PublishRepositoryDto } from './dto/publish-repository.dto';

@ApiTags('Developer Integration')
@Controller('developers')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT')
export class DevelopersController {
  constructor(private readonly developersService: DevelopersService) {}

  @Get('gitea/profile')
  @Roles('Developer')
  @ApiOperation({
    summary: 'Get Gitea integration status',
    description: 'Get current Gitea integration status and profile information'
  })
  @ApiResponse({
    status: 200,
    description: 'Gitea integration status retrieved',
  })
  @ApiResponse({
    status: 403,
    description: 'User does not have Developer role',
  })
  async getGiteaProfile() {
    return this.developersService.getGiteaProfile();
  }

  @Post('repositories/:repoId/publish')
  @Roles('Developer')
  @ApiOperation({
    summary: 'Publish repository to marketplace',
    description: 'Publish a Gitea repository as a marketplace item with pricing and metadata'
  })
  @ApiParam({
    name: 'repoId',
    description: 'Repository ID from Gitea',
    example: '123'
  })
  @ApiResponse({
    status: 201,
    description: 'Repository published successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Repository already published or invalid metadata',
  })
  async publishRepository(
    @Param('repoId') repoId: string,
    @Body() publishDto: PublishRepositoryDto,
  ) {
    return this.developersService.publishRepository(repoId, publishDto);
  }
}
```

### 5. Response DTOs

```typescript
// src/common/dto/pagination.dto.ts
import { ApiProperty } from '@nestjs/swagger';

export class PaginationDto {
  @ApiProperty({ example: 1 })
  page: number;

  @ApiProperty({ example: 20 })
  limit: number;

  @ApiProperty({ example: 100 })
  total: number;

  @ApiProperty({ example: 5 })
  totalPages: number;

  @ApiProperty({ example: true })
  hasNext: boolean;

  @ApiProperty({ example: false })
  hasPrev: boolean;
}

// src/developers/dto/developer-analytics.dto.ts
import { ApiProperty } from '@nestjs/swagger';

export class DeveloperAnalyticsDto {
  @ApiProperty({
    enum: ['7d', '30d', '90d', '1y', 'all'],
    example: '30d'
  })
  period: string;

  @ApiProperty({ example: 1250.50 })
  totalRevenue: number;

  @ApiProperty({ example: 342 })
  totalDownloads: number;

  @ApiProperty({ example: 1205 })
  totalViews: number;

  @ApiProperty({ example: 5 })
  activeItems: number;
}
```

### 6. Guards and Decorators

```typescript
// src/auth/guards/roles.guard.ts
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    
    if (!requiredRoles) {
      return true;
    }
    
    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.roles?.includes(role));
  }
}

// src/auth/decorators/roles.decorator.ts
import { SetMetadata } from '@nestjs/common';

export const ROLES_KEY = 'roles';
export const Roles = (...roles: string[]) => SetMetadata(ROLES_KEY, roles);
```

### 7. Environment Configuration

```typescript
// src/config/configuration.ts
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  database: {
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || '15m',
  },
  gitea: {
    baseUrl: process.env.GITEA_BASE_URL,
    adminToken: process.env.GITEA_ADMIN_TOKEN,
  },
  btcpay: {
    serverUrl: process.env.BTCPAY_SERVER_URL,
    storeId: process.env.BTCPAY_STORE_ID,
    apiKey: process.env.BTCPAY_API_KEY,
  },
});
```

This setup provides a complete NestJS integration that matches the OpenAPI specification exactly, with proper validation, documentation, and type safety.
