# RSGlider API - cURL Examples

## 🔐 Authentication

### Register New User
```bash
curl -X POST https://api.rsglider.com/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "firstName": "<PERSON>",
    "lastName": "Developer",
    "acceptTerms": true
  }'
```

### Login User
```bash
curl -X POST https://api.rsglider.com/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'
```

### Refresh Token
```bash
curl -X POST https://api.rsglider.com/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "your_refresh_token_here"
  }'
```

## 👤 User Profile Management

### Get Current User Profile
```bash
curl -X GET https://api.rsglider.com/users/me \
  -H "Authorization: Bearer your_access_token_here"
```

### Setup Two-Factor Authentication
```bash
curl -X POST https://api.rsglider.com/users/me/2fa/setup \
  -H "Authorization: Bearer your_access_token_here"
```

### Get Active Sessions
```bash
curl -X GET https://api.rsglider.com/users/me/sessions \
  -H "Authorization: Bearer your_access_token_here"
```

### Register Desktop App
```bash
curl -X POST https://api.rsglider.com/users/me/desktop/register \
  -H "Authorization: Bearer your_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "deviceFingerprint": "unique_device_id_123",
    "deviceName": "Johns MacBook Pro",
    "appVersion": "1.0.0",
    "platform": "macos",
    "hardwareId": "hardware_uuid_456"
  }'
```

## 🛍️ Marketplace & Shopping

### Browse Store Items
```bash
curl -X GET "https://api.rsglider.com/store/items?category=scripts&page=1&limit=20" \
  -H "Authorization: Bearer your_access_token_here"
```

### Add Item to Cart
```bash
curl -X POST https://api.rsglider.com/cart/items \
  -H "Authorization: Bearer your_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "itemId": "item_uuid_here",
    "quantity": 1
  }'
```

### Checkout with BTCPay Server
```bash
curl -X POST https://api.rsglider.com/cart/checkout \
  -H "Authorization: Bearer your_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "paymentMethod": "btcpay_bitcoin",
    "returnUrl": "https://rsglider.com/payment/success",
    "cancelUrl": "https://rsglider.com/payment/cancel"
  }'
```

### Check Order Status
```bash
curl -X GET https://api.rsglider.com/orders/order_uuid_here/status \
  -H "Authorization: Bearer your_access_token_here"
```

## 🧑‍💻 Developer Integration

### Get Gitea Integration Status
```bash
curl -X GET https://api.rsglider.com/developers/gitea/profile \
  -H "Authorization: Bearer your_access_token_here"
```

### List Developer Repositories
```bash
curl -X GET https://api.rsglider.com/developers/repositories \
  -H "Authorization: Bearer your_access_token_here"
```

### Publish Repository to Marketplace
```bash
curl -X POST https://api.rsglider.com/developers/repositories/repo_id_123/publish \
  -H "Authorization: Bearer your_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "category": "automation",
    "tags": ["productivity", "scripts"],
    "pricing": {
      "type": "one_time",
      "basePrice": 9.99,
      "currency": "USD"
    },
    "visibility": "public"
  }'
```

### Get Developer Analytics
```bash
curl -X GET "https://api.rsglider.com/developers/analytics/overview?period=30d" \
  -H "Authorization: Bearer your_access_token_here"
```

### Check Available Payout Amount
```bash
curl -X GET https://api.rsglider.com/developers/payouts/available \
  -H "Authorization: Bearer your_access_token_here"
```

### Request Manual Payout
```bash
curl -X POST https://api.rsglider.com/developers/payouts/request \
  -H "Authorization: Bearer your_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "btcAddress": "******************************************",
    "reason": "Monthly payout request"
  }'
```

### Update Payout Settings
```bash
curl -X PUT https://api.rsglider.com/developers/payouts/settings \
  -H "Authorization: Bearer your_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "btcAddress": "******************************************",
    "autoPayoutEnabled": true
  }'
```

## 🤖 Bot Session Management

### Get Active Bot Sessions
```bash
curl -X GET https://api.rsglider.com/users/me/bots/sessions \
  -H "Authorization: Bearer your_access_token_here"
```

### Start New Bot Session
```bash
curl -X POST https://api.rsglider.com/users/me/bots/sessions \
  -H "Authorization: Bearer your_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "botType": "trading_bot",
    "sessionName": "My Trading Bot",
    "configuration": {
      "strategy": "dca",
      "interval": "1h"
    }
  }'
```

### Stop Bot Session
```bash
curl -X DELETE "https://api.rsglider.com/users/me/bots/sessions/bot_session_uuid?force=false" \
  -H "Authorization: Bearer your_access_token_here"
```

## 🔧 Admin Operations

### List All Users (Admin)
```bash
curl -X GET "https://api.rsglider.com/admin/users?page=1&limit=20" \
  -H "Authorization: Bearer admin_access_token_here"
```

### Promote User to Developer (Admin)
```bash
curl -X POST https://api.rsglider.com/admin/developers/user_uuid_here/promote \
  -H "Authorization: Bearer admin_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Approved developer application",
    "notifyUser": true,
    "giteaUsername": "developer_username"
  }'
```

### Update Global Payout Settings (Admin)
```bash
curl -X PUT https://api.rsglider.com/admin/payout-settings \
  -H "Authorization: Bearer admin_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "minimumAmount": 50,
    "frequency": "weekly",
    "autoPayoutEnabled": true,
    "holdingPeriod": 7
  }'
```

### Set User-Specific Payout Settings (Admin)
```bash
curl -X PUT https://api.rsglider.com/admin/developers/user_uuid_here/payout-settings \
  -H "Authorization: Bearer admin_access_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "minimumAmount": 25,
    "frequency": "daily",
    "autoPayoutEnabled": true
  }'
```

## 🔗 Webhook Examples

### BTCPay Server Payment Webhook
```bash
# This is sent by BTCPay Server to your webhook endpoint
curl -X POST https://api.rsglider.com/webhooks/btcpay/payment \
  -H "Content-Type: application/json" \
  -H "BTCPay-Sig: webhook_signature_here" \
  -d '{
    "invoiceId": "invoice_id_123",
    "status": "settled",
    "type": "InvoiceSettled"
  }'
```

### Gitea Repository Webhook
```bash
# This is sent by Gitea when repositories are updated
curl -X POST https://api.rsglider.com/webhooks/gitea/repository \
  -H "Content-Type: application/json" \
  -H "X-Gitea-Signature": "webhook_signature_here" \
  -d '{
    "action": "pushed",
    "repository": {
      "id": 123,
      "name": "my-script",
      "full_name": "developer/my-script"
    }
  }'
```

## 📝 Notes

- Replace `your_access_token_here` with actual JWT tokens
- Replace UUID placeholders with actual resource IDs
- All timestamps are in ISO 8601 format
- Bitcoin addresses must be valid mainnet addresses
- Webhook signatures must be verified for security
