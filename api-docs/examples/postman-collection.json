{"info": {"name": "RSGlider API", "description": "Complete API collection for RSGlider platform testing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "https://api.rsglider.com", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Developer\",\n  \"acceptTerms\": true\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('access_token', response.accessToken);", "    pm.collectionVariables.set('refresh_token', response.refreshToken);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}}]}, {"name": "User Profile", "item": [{"name": "Get Profile", "request": {"method": "GET", "url": {"raw": "{{base_url}}/users/me", "host": ["{{base_url}}"], "path": ["users", "me"]}}}, {"name": "Get Sessions", "request": {"method": "GET", "url": {"raw": "{{base_url}}/users/me/sessions", "host": ["{{base_url}}"], "path": ["users", "me", "sessions"]}}}, {"name": "Setup 2FA", "request": {"method": "POST", "url": {"raw": "{{base_url}}/users/me/2fa/setup", "host": ["{{base_url}}"], "path": ["users", "me", "2fa", "setup"]}}}]}, {"name": "Developer Integration", "item": [{"name": "Get Gitea Profile", "request": {"method": "GET", "url": {"raw": "{{base_url}}/developers/gitea/profile", "host": ["{{base_url}}"], "path": ["developers", "g<PERSON>a", "profile"]}}}, {"name": "List Repositories", "request": {"method": "GET", "url": {"raw": "{{base_url}}/developers/repositories", "host": ["{{base_url}}"], "path": ["developers", "repositories"]}}}, {"name": "Get Analytics Overview", "request": {"method": "GET", "url": {"raw": "{{base_url}}/developers/analytics/overview?period=30d", "host": ["{{base_url}}"], "path": ["developers", "analytics", "overview"], "query": [{"key": "period", "value": "30d"}]}}}, {"name": "Request Payout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"btcAddress\": \"******************************************\",\n  \"reason\": \"Monthly payout request\"\n}"}, "url": {"raw": "{{base_url}}/developers/payouts/request", "host": ["{{base_url}}"], "path": ["developers", "payouts", "request"]}}}]}, {"name": "Marketplace", "item": [{"name": "Browse Store Items", "request": {"method": "GET", "url": {"raw": "{{base_url}}/store/items?category=scripts&page=1&limit=20", "host": ["{{base_url}}"], "path": ["store", "items"], "query": [{"key": "category", "value": "scripts"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Add to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"itemId\": \"{{item_id}}\",\n  \"quantity\": 1\n}"}, "url": {"raw": "{{base_url}}/cart/items", "host": ["{{base_url}}"], "path": ["cart", "items"]}}}, {"name": "Checkout with BTCPay", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paymentMethod\": \"btcpay_bitcoin\",\n  \"returnUrl\": \"https://rsglider.com/payment/success\",\n  \"cancelUrl\": \"https://rsglider.com/payment/cancel\"\n}"}, "url": {"raw": "{{base_url}}/cart/checkout", "host": ["{{base_url}}"], "path": ["cart", "checkout"]}}}]}, {"name": "Admin", "item": [{"name": "List Users", "request": {"method": "GET", "url": {"raw": "{{base_url}}/admin/users?page=1&limit=20", "host": ["{{base_url}}"], "path": ["admin", "users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}}, {"name": "Promote to Dev<PERSON>per", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Approved developer application\",\n  \"notifyUser\": true\n}"}, "url": {"raw": "{{base_url}}/admin/developers/{{user_id}}/promote", "host": ["{{base_url}}"], "path": ["admin", "developers", "{{user_id}}", "promote"]}}}, {"name": "Update Payout Settings", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"minimumAmount\": 50,\n  \"frequency\": \"weekly\",\n  \"autoPayoutEnabled\": true,\n  \"holdingPeriod\": 7\n}"}, "url": {"raw": "{{base_url}}/admin/payout-settings", "host": ["{{base_url}}"], "path": ["admin", "payout-settings"]}}}]}]}