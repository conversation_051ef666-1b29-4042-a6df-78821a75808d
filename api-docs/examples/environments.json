{"environments": [{"name": "Development", "values": [{"key": "base_url", "value": "http://localhost:3000", "enabled": true}, {"key": "access_token", "value": "", "enabled": true}, {"key": "refresh_token", "value": "", "enabled": true}, {"key": "user_id", "value": "", "enabled": true}, {"key": "item_id", "value": "", "enabled": true}]}, {"name": "Staging", "values": [{"key": "base_url", "value": "https://api-staging.rsglider.com", "enabled": true}, {"key": "access_token", "value": "", "enabled": true}, {"key": "refresh_token", "value": "", "enabled": true}, {"key": "user_id", "value": "", "enabled": true}, {"key": "item_id", "value": "", "enabled": true}]}, {"name": "Production", "values": [{"key": "base_url", "value": "https://api.rsglider.com", "enabled": true}, {"key": "access_token", "value": "", "enabled": true}, {"key": "refresh_token", "value": "", "enabled": true}, {"key": "user_id", "value": "", "enabled": true}, {"key": "item_id", "value": "", "enabled": true}]}]}