# RSGlider API Documentation

## 📋 Overview
This directory contains the complete API specification and documentation for the RSGlider platform.

## 📁 Structure
```
api-docs/
├── openapi.yaml          # Complete OpenAPI 3.0.3 specification
├── README.md            # This file
├── examples/            # API usage examples and collections
└── schemas/             # Individual schema files (if split)
```

## 🚀 Quick Start

### View API Documentation
1. **Swagger UI**: Import `openapi.yaml` into [Swagger Editor](https://editor.swagger.io/)
2. **Local Development**: NestJS will auto-generate docs at `/api/docs`
3. **Postman**: Import the OpenAPI spec for testing

### Key Endpoints

#### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login with 2FA support
- `POST /auth/refresh` - Token refresh with rotation

#### User Management
- `GET /users/me` - Get current user profile
- `GET /users/me/sessions` - Manage sessions across platforms
- `POST /users/me/2fa/setup` - Setup two-factor authentication

#### Developer Integration
- `GET /developers/gitea/profile` - Gitea integration status
- `POST /developers/repositories/{repoId}/publish` - Publish to marketplace
- `GET /developers/analytics/overview` - Developer analytics

#### Admin Controls
- `POST /admin/developers/{userId}/promote` - Promote user to developer
- `PUT /admin/payout-settings` - Configure payout settings
- `GET /admin/users` - User management

#### Marketplace & Payments
- `GET /store/items` - Browse marketplace
- `POST /cart/checkout` - BTCPay Server checkout
- `GET /orders/{orderId}/status` - Payment status

#### Session Addons
- `GET /store/addons` - Browse session addons (desktop/bot)
- `POST /cart/addons` - Add session addons to cart
- `GET /users/me/addons` - View active session addons

## 🔐 Authentication

### Bearer Token (JWT)
```bash
Authorization: Bearer <jwt_token>
```

### API Key (Alternative)
```bash
X-API-Key: <api_key>
```

## 🏗️ NestJS Integration

### Swagger Module Setup
```typescript
// main.ts
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

const config = new DocumentBuilder()
  .setTitle('RSGlider API')
  .setDescription('Comprehensive API for RSGlider platform')
  .setVersion('1.0')
  .addBearerAuth()
  .addApiKey({ type: 'apiKey', name: 'X-API-Key', in: 'header' })
  .build();

const document = SwaggerModule.createDocument(app, config);
SwaggerModule.setup('api/docs', app, document);
```

### DTO Decorators
```typescript
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({ format: 'email' })
  email: string;

  @ApiProperty({ minLength: 8 })
  password: string;

  @ApiPropertyOptional({ pattern: '^[0-9]{6}$' })
  twoFactorCode?: string;
}
```

## 🔄 Development Workflow

### 1. Code-First Approach
- Define DTOs with Swagger decorators
- NestJS auto-generates OpenAPI spec
- Keep `openapi.yaml` as the source of truth

### 2. Validation
- Use `class-validator` decorators
- Matches OpenAPI validation rules
- Consistent client/server validation

### 3. Testing
- Import OpenAPI spec into Postman
- Generate test collections
- Automated API testing

## 📊 Key Features Covered

### Platform Management
- ✅ User authentication with 2FA
- ✅ Role-based access control (RBAC)
- ✅ Session management (web vs desktop)
- ✅ Admin user management

### Developer Marketplace
- ✅ Admin-controlled Gitea integration
- ✅ Repository publishing to marketplace
- ✅ Analytics and revenue tracking
- ✅ Flexible BTCPay Server payouts

### Payment System
- ✅ Direct Bitcoin payments via BTCPay
- ✅ No stored funds (security-first)
- ✅ Real-time payment tracking
- ✅ Webhook integrations

### Session & Device Control
- ✅ Desktop app device limits (1 Free, 2 Pro + addons)
- ✅ Bot session management (1 Free, 3 Pro + addons)
- ✅ Web device verification (3 sessions for all tiers)
- ✅ Addon-based scaling for desktop/bot sessions

## 🛠️ Implementation Status

| Feature | Status | Notes |
|---------|--------|-------|
| Authentication | ✅ Complete | JWT + 2FA + Session management |
| User Management | ✅ Complete | RBAC + Admin controls |
| Developer Integration | ✅ Complete | Gitea + Marketplace + Analytics |
| Payment System | ✅ Complete | BTCPay Server integration |
| Session Management | ✅ Complete | Multi-platform session control |
| Admin Configuration | ✅ Complete | Flexible payout settings |

## 📝 Related Documentation

- [BTCPay Integration Guide](../docs/BTCPAY_INTEGRATION.md)
- [Gitea Integration Plan](../docs/GITEA_INTEGRATION_PLAN.md)
- [Session Management](../docs/SESSION_MANAGEMENT.md)

## 🔗 External Resources

- [OpenAPI 3.0.3 Specification](https://swagger.io/specification/)
- [NestJS Swagger Module](https://docs.nestjs.com/openapi/introduction)
- [BTCPay Server API](https://docs.btcpayserver.org/API/Greenfield/v1/)
- [Gitea API Documentation](https://docs.gitea.io/en-us/api-usage/)

This API specification is production-ready and covers all RSGlider platform requirements with comprehensive security, payment processing, and developer marketplace functionality.
