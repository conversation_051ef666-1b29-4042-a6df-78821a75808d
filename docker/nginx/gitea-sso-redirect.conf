server {
    listen 80;
    server_name localhost;

    # Redirect login page to SSO
    location /user/login {
        return 302 /user/oauth2/RSGlider;
    }

    # Redirect sign-in links to SSO
    location /user/sign_in {
        return 302 /user/oauth2/RSGlider;
    }

    # Pass all other requests to Gitea
    location / {
        proxy_pass http://rsglider-gitea:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle WebSocket connections
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Increase timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Handle large uploads
        client_max_body_size 100M;
    }
}
