#!/bin/bash

# Helper script to start Gitea development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
SHARED_DIR="$PROJECT_ROOT/shared"
TOKEN_FILE="$SHARED_DIR/gitea_admin_token.txt"
COMPOSE_FILE="$SCRIPT_DIR/docker-compose.gitea.yml"

echo -e "${BLUE}🚀 Starting RSGlider Gitea Development Environment${NC}"

# Ensure shared directory exists
mkdir -p "$SHARED_DIR"

# Function to check if services are running
check_service_health() {
    local service_name="$1"
    docker-compose -f "$COMPOSE_FILE" ps -q "$service_name" | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "unhealthy"
}

# Function to wait for service health
wait_for_service() {
    local service_name="$1"
    local max_attempts=30
    local attempt=0
    
    echo -e "${YELLOW}⏳ Waiting for $service_name to be healthy...${NC}"
    
    while [ $attempt -lt $max_attempts ]; do
        local health=$(check_service_health "$service_name")
        if [ "$health" = "healthy" ]; then
            echo -e "${GREEN}✅ $service_name is healthy!${NC}"
            return 0
        fi
        echo "   $service_name health: $health (attempt $((attempt + 1))/$max_attempts)"
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ $service_name failed to become healthy after $max_attempts attempts${NC}"
    return 1
}

# Start the services
echo -e "${BLUE}🐳 Starting Docker services...${NC}"
docker-compose -f "$COMPOSE_FILE" up -d postgres gitea

# Wait for postgres to be healthy
if ! wait_for_service "postgres"; then
    echo -e "${RED}❌ PostgreSQL failed to start properly${NC}"
    exit 1
fi

# Wait for gitea to be healthy
if ! wait_for_service "gitea"; then
    echo -e "${RED}❌ Gitea failed to start properly${NC}"
    exit 1
fi

# Run the initialization script
echo -e "${BLUE}🔧 Running Gitea initialization...${NC}"
docker-compose -f "$COMPOSE_FILE" up gitea-init

# Check if initialization was successful
if [ -f "$TOKEN_FILE" ]; then
    TOKEN=$(cat "$TOKEN_FILE" 2>/dev/null || echo "")
    if [ -n "$TOKEN" ]; then
        echo -e "${GREEN}✅ Gitea initialization completed successfully!${NC}"
        echo ""
        echo -e "${BLUE}📋 Gitea Access Information:${NC}"
        echo -e "   🌐 Web UI:    ${GREEN}http://localhost:3001${NC}"
        echo -e "   👤 Username:  ${GREEN}admin${NC}"
        echo -e "   🔐 Password:  ${GREEN}rsglider_admin_password_change_in_production${NC}"
        echo -e "   🔑 API Token: ${GREEN}$TOKEN${NC}"
        echo -e "   📁 Token File: ${GREEN}$TOKEN_FILE${NC}"
        echo ""
        echo -e "${BLUE}🔗 API Example:${NC}"
        echo -e "   curl -H \"Authorization: token $TOKEN\" http://localhost:3001/api/v1/user"
        echo ""
        echo -e "${GREEN}🎉 Gitea is ready for development!${NC}"
    else
        echo -e "${RED}❌ Token file exists but is empty${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ Token file not found. Initialization may have failed.${NC}"
    echo -e "${YELLOW}📝 Check logs with: docker-compose -f $COMPOSE_FILE logs gitea-init${NC}"
    exit 1
fi

# Show running services
echo ""
echo -e "${BLUE}🔍 Running Services:${NC}"
docker-compose -f "$COMPOSE_FILE" ps 