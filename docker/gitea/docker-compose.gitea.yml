version: '3.8'

services:
  gitea:
    image: gitea/gitea:1.21
    container_name: rsglider_gitea
    environment:
      - USER_UID=1000
      - USER_GID=1000
      - GITEA__database__DB_TYPE=postgres
      - GITEA__database__HOST=postgres:5432
      - GITEA__database__NAME=rsglider_gitea
      - GITEA__database__USER=rsglider
      - GITEA__database__PASSWD=rsglider_dev_password
      - GITEA__database__SSL_MODE=disable
      - GITEA__server__DOMAIN=localhost
      - GITEA__server__HTTP_PORT=3000
      - GITEA__server__ROOT_URL=http://localhost:3001/
      - GIT<PERSON>__server__DISABLE_SSH=false
      - GITEA__server__SSH_PORT=22
      - GITEA__server__SSH_LISTEN_PORT=22
      - GITEA__security__INSTALL_LOCK=false
    restart: unless-stopped
    networks:
      - rsglider_network
    volumes:
      - gitea_data:/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - ./shared:/shared
    ports:
      - "3001:3000"
      - "2222:22"
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/healthz"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  gitea-init:
    image: curlimages/curl:latest
    container_name: rsglider_gitea_init
    depends_on:
      gitea:
        condition: service_healthy
    networks:
      - rsglider_network
    volumes:
      - ./docker/gitea/init-admin.sh:/init-admin.sh:ro
      - ./shared:/shared
    command: ["/bin/sh", "/init-admin.sh"]
    restart: "no"

  postgres:
    image: postgres:15
    container_name: rsglider_postgres
    environment:
      - POSTGRES_USER=rsglider
      - POSTGRES_PASSWORD=rsglider_dev_password
      - POSTGRES_DB=rsglider_gitea
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - rsglider_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rsglider -d rsglider_gitea"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  gitea_data:
    driver: local
  postgres_data:
    driver: local

networks:
  rsglider_network:
    driver: bridge 