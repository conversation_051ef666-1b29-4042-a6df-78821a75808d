# Gitea Development Environment

This directory contains the setup for a local Gitea instance with automatic admin user creation and API token generation for the RSGlider project.

## Features

- 🚀 Automated Gitea installation and configuration
- 👤 Automatic admin user creation
- 🔑 API token generation for seamless integration
- 🐘 PostgreSQL database backend
- 🔄 Health checks and proper service dependencies
- 💾 Persistent data storage

## Quick Start

### Option 1: Using the Helper Script (Recommended)

```bash
# Start Gitea with automatic setup
./docker/gitea/start-gitea.sh
```

This script will:
1. Start PostgreSQL and Gitea services
2. Wait for services to be healthy
3. Run the admin initialization script
4. Display access credentials and API token

### Option 2: Using Docker Compose Directly

```bash
# Start the services
docker-compose -f docker/gitea/docker-compose.gitea.yml up -d

# Check service status
docker-compose -f docker/gitea/docker-compose.gitea.yml ps

# View initialization logs
docker-compose -f docker/gitea/docker-compose.gitea.yml logs gitea-init
```

## Access Information

After successful setup:

- **Web UI**: http://localhost:3001
- **Username**: `admin`
- **Password**: `rsglider_admin_password_change_in_production`
- **API Token**: Stored in `shared/gitea_admin_token.txt`

## API Usage

The generated token has full access (`all` scopes). Example usage:

```bash
# Get current user info
curl -H "Authorization: token YOUR_TOKEN_HERE" http://localhost:3001/api/v1/user

# List repositories
curl -H "Authorization: token YOUR_TOKEN_HERE" http://localhost:3001/api/v1/user/repos

# Create a repository
curl -X POST -H "Authorization: token YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{"name":"test-repo","description":"Test repository"}' \
  http://localhost:3001/api/v1/user/repos
```

## File Structure

```
docker/gitea/
├── README.md                    # This documentation
├── docker-compose.gitea.yml     # Docker Compose configuration
├── init-admin.sh               # Admin user and token generation script
└── start-gitea.sh              # Helper script for easy startup
```

## Configuration

### Admin Credentials

The default admin credentials are defined in `init-admin.sh`:

```bash
ADMIN_USERNAME="admin"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="rsglider_admin_password_change_in_production"
```

**⚠️ Security Note**: Change the default password for production use!

### Database Configuration

The setup uses PostgreSQL with these credentials:
- **Host**: `postgres:5432`
- **Database**: `rsglider_gitea`
- **Username**: `rsglider`
- **Password**: `rsglider_dev_password`

### Environment Variables

Key Gitea configuration via environment variables:

```yaml
GITEA__database__DB_TYPE: postgres
GITEA__server__DOMAIN: localhost
GITEA__server__ROOT_URL: http://localhost:3001/
GITEA__security__INSTALL_LOCK: false  # Allows reinstallation
```

## Troubleshooting

### Services Not Starting

Check service health:
```bash
docker-compose -f docker/gitea/docker-compose.gitea.yml ps
```

View service logs:
```bash
# Gitea logs
docker-compose -f docker/gitea/docker-compose.gitea.yml logs gitea

# PostgreSQL logs
docker-compose -f docker/gitea/docker-compose.gitea.yml logs postgres

# Initialization logs
docker-compose -f docker/gitea/docker-compose.gitea.yml logs gitea-init
```

### Token Generation Failed

1. Check if Gitea is accessible:
   ```bash
   curl http://localhost:3001/api/healthz
   ```

2. Verify admin credentials by trying manual login:
   ```bash
   curl -u "admin:rsglider_admin_password_change_in_production" \
     http://localhost:3001/api/v1/users/admin/tokens
   ```

3. Re-run initialization:
   ```bash
   docker-compose -f docker/gitea/docker-compose.gitea.yml up gitea-init
   ```

### Reset Everything

To start fresh:
```bash
# Stop and remove containers
docker-compose -f docker/gitea/docker-compose.gitea.yml down

# Remove volumes (⚠️ This deletes all data!)
docker-compose -f docker/gitea/docker-compose.gitea.yml down -v

# Remove token file
rm -f shared/gitea_admin_token.txt

# Start fresh
./docker/gitea/start-gitea.sh
```

## Integration with RSGlider

The generated API token can be used in your RSGlider application for:

- Automated repository creation
- Code deployment and versioning
- Issue tracking integration
- CI/CD pipeline triggers
- Webhook notifications

Example integration in your application:

```python
import requests

# Read the token
with open('shared/gitea_admin_token.txt', 'r') as f:
    token = f.read().strip()

# Use the Gitea API
headers = {'Authorization': f'token {token}'}
response = requests.get('http://localhost:3001/api/v1/user', headers=headers)
user_info = response.json()
```

## Security Considerations

1. **Change default passwords** in production
2. **Secure token storage** - don't commit tokens to version control
3. **Use environment variables** for sensitive configuration
4. **Enable SSL/TLS** for production deployments
5. **Regular backups** of PostgreSQL data
6. **Network security** - restrict access to necessary ports only

## Contributing

When modifying the setup:

1. Test changes with a fresh installation
2. Update this documentation
3. Ensure backwards compatibility
4. Validate token generation and API access 