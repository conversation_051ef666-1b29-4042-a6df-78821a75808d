/* RSGlider SSO-Only Login Styling */

/* Hide local login form on signin page */
.page-content.user.signin .ui.form:has(input[name="user_name"]) {
    display: none !important;
}

/* Hide the "or" divider between local and SSO */
.page-content.user.signin .ui.horizontal.divider {
    display: none !important;
}

/* Add custom header for SSO */
.page-content.user.signin .ui.container {
    position: relative;
}

.page-content.user.signin .ui.container::before {
    content: "🚀 RSGlider Development Environment";
    display: block;
    width: 100%;
    padding: 2rem;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 12px;
    color: white;
    text-align: center;
    font-size: 1.8rem;
    font-weight: bold;
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
}

.page-content.user.signin .ui.container::after {
    content: "Sign in with your RSGlider account to access your development tools";
    display: block;
    text-align: center;
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    margin-top: -1rem;
}

/* Style the OAuth buttons */
.page-content.user.signin .oauth-login .ui.button {
    padding: 1rem 2rem !important;
    font-size: 1.1rem !important;
    border-radius: 8px !important;
    width: 100% !important;
    margin-bottom: 1rem !important;
}

/* Style the RSGlider button specifically */
.page-content.user.signin .oauth-login .ui.RSGlider.button {
    background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
    color: white !important;
    border: none !important;
}

.page-content.user.signin .oauth-login .ui.RSGlider.button:hover {
    background: linear-gradient(135deg, #e55a2b, #e0821a) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4) !important;
}

/* Hide passkey option if present */
.page-content.user.signin .ui.form:has(#sign-in-with-passkey) {
    display: none !important;
}

/* Add footer message */
.page-content.user.signin .ui.container {
    padding-bottom: 3rem;
}

.page-content.user.signin .ui.container::after {
    content: "Need help? Contact <NAME_EMAIL>";
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    color: #999;
    font-size: 0.9rem;
    text-align: center;
}
