#!/bin/bash

# Setup OAuth2 provider for RSGlider SSO in Gitea
echo "🔧 Setting up RSGlider OAuth2 provider in Gitea..."

# Wait for <PERSON><PERSON><PERSON> to be ready
echo "⏳ Waiting for <PERSON>ite<PERSON> to be ready..."
until curl -f http://localhost:3000/api/healthz >/dev/null 2>&1; do
    echo "Waiting for Gitea..."
    sleep 2
done
echo "✅ Gitea is ready"

# Wait for RSGlider API to be ready
echo "⏳ Waiting for RSGlider API to be ready..."
until curl -f http://rsglider-api:3000/health >/dev/null 2>&1; do
    echo "Waiting for RSGlider API..."
    sleep 2
done
echo "✅ RSGlider API is ready"

# Test OIDC discovery endpoint
echo "🔍 Testing OIDC discovery endpoint..."
if curl -f http://rsglider-api:3000/api/auth/oidc/.well-known/openid-configuration >/dev/null 2>&1; then
    echo "✅ OIDC discovery endpoint is accessible"
else
    echo "❌ OIDC discovery endpoint is not accessible"
    exit 1
fi

# Use Gitea CLI to add OAuth2 provider
echo "🔧 Adding RSGlider OAuth2 provider..."

# The gitea admin auth add-oauth command
gitea admin auth add-oauth \
    --name "RSGlider" \
    --provider "openidConnect" \
    --key "gitea" \
    --secret "rsglider_gitea_oauth_secret_change_in_production" \
    --auto-discover-url "http://rsglider-api:3000/api/auth/oidc/.well-known/openid-configuration" \
    --icon-url "http://rsglider-api:3000/favicon.ico" \
    --scopes "openid,profile,email"

if [ $? -eq 0 ]; then
    echo "✅ RSGlider OAuth2 provider added successfully!"
    echo "🎉 SSO is now configured!"
    echo ""
    echo "🧪 To test:"
    echo "1. Go to http://localhost:3001"
    echo "2. Look for 'Sign in with RSGlider' button"
    echo "3. Click it to test SSO flow"
else
    echo "❌ Failed to add OAuth2 provider"
    exit 1
fi
