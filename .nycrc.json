{"extends": "@istanbuljs/nyc-config-typescript", "all": true, "check-coverage": true, "reporter": ["text", "text-summary", "html", "json", "lcov", "clover"], "report-dir": "coverage", "temp-dir": ".nyc_output", "exclude": ["**/*.spec.ts", "**/*.integration.spec.ts", "**/node_modules/**", "**/dist/**", "**/coverage/**", "**/*.d.ts", "**/main.ts", "**/test/**", "**/*.config.ts", "**/*.config.js"], "include": ["src/**/*.ts"], "extension": [".ts"], "cache": true, "instrument": true, "sourceMap": true, "produce-source-map": true, "branches": 60, "lines": 60, "functions": 60, "statements": 60, "per-file": true, "skip-full": false, "watermarks": {"lines": [60, 80], "functions": [60, 80], "branches": [60, 80], "statements": [60, 80]}}