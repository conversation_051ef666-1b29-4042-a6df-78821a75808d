services:
  # PostgreSQL Database
  postgres:
    image: postgres:17-alpine
    container_name: rsglider-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: rsglider
      POSTGRES_USER: rsglider
      POSTGRES_PASSWORD: rsglider_dev_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rsglider -d rsglider"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Sessions
  redis:
    image: redis:7-alpine
    container_name: rsglider-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass rsglider_redis_password
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "rsglider_redis_password", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # RSGlider API Application
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rsglider-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      TZ: America/New_York
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: rsglider
      DATABASE_USER: rsglider
      DATABASE_PASSWORD: rsglider_dev_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: rsglider_redis_password
      JWT_SECRET: dev_jwt_secret_change_in_production
      JWT_EXPIRES_IN: 15m
      REFRESH_TOKEN_EXPIRES_IN: 7d
      BTCPAY_SERVER_URL: https://btcpay.rsglider.com
      BTCPAY_STORE_ID: dev_store_id
      BTCPAY_API_KEY: dev_api_key
      BTCPAY_WEBHOOK_SECRET: dev_webhook_secret
      S3_ENDPOINT: http://minio:9000
      S3_ACCESS_KEY: rsglider_minio_user
      S3_SECRET_KEY: rsglider_minio_password
      S3_BUCKET_NAME: rsglider-uploads
      S3_REGION: us-east-1
      S3_FORCE_PATH_STYLE: true
      GITEA_BASE_URL: http://gitea:3000
      GITEA_EXTERNAL_URL: http://localhost:3001
      GITEA_ADMIN_TOKEN: 66d86aeab9802929c9744a236ba0b7092b832337
      GITEA_WEBHOOK_SECRET: rsglider_gitea_webhook_secret_change_in_production
      # Email Configuration (Inbucket for development)
      SMTP_HOST: inbucket
      SMTP_PORT: 2500
      SMTP_SECURE: false
      SMTP_USER: ""
      SMTP_PASSWORD: ""
      SMTP_FROM: <EMAIL>
      EMAIL_FROM_NAME: RSGlider
      EMAIL_BASE_URL: http://localhost:3000
      # Admin user configuration (synced with Gitea)
      ADMIN_PASSWORD: ${ADMIN_PASSWORD:-rsglider_admin_password_change_in_production}
    volumes:
      # Mount source code for hot reload, but exclude node_modules
      - ./src:/app/src
      - ./package.json:/app/package.json
      - ./pnpm-lock.yaml:/app/pnpm-lock.yaml
      - ./tsconfig.json:/app/tsconfig.json
      - ./nest-cli.json:/app/nest-cli.json
      - ./drizzle.config.ts:/app/drizzle.config.ts
      - ./scripts:/app/scripts
      - api_uploads:/app/uploads
      - gitea_shared:/app/shared:ro
      # Use named volume for node_modules to avoid architecture conflicts
      - api_node_modules:/app/node_modules
      # Timezone configuration
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "3000:3000"
      - "9229:9229" # Debug port
    networks:
      - rsglider-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      gitea:
        condition: service_healthy
      inbucket:
        condition: service_healthy
    command: /app/scripts/startup.sh

  # MinIO Object Storage (S3 Compatible)
  minio:
    image: minio/minio:latest
    container_name: rsglider-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: rsglider_minio_user
      MINIO_ROOT_PASSWORD: rsglider_minio_password
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9002
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"  # MinIO API
      - "9002:9001"  # MinIO Console
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Gitea Admin Initialization
  gitea-init:
    image: docker.gitea.com/gitea:1.23.8-rootless
    container_name: rsglider-gitea-init
    restart: "no"
    environment:
      - USER_UID=1000
      - USER_GID=1000
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-rsglider_admin_password_change_in_production}
      - GITEA__database__DB_TYPE=postgres
      - GITEA__database__HOST=postgres:5432
      - GITEA__database__NAME=rsglider_gitea
      - GITEA__database__USER=rsglider
      - GITEA__database__PASSWD=rsglider_dev_password
      - GITEA__database__SCHEMA=public
      - GITEA__database__SSL_MODE=disable
      - GITEA__database__CHARSET=utf8mb4
      - GITEA__security__INSTALL_LOCK=true
      - GITEA__server__DOMAIN=localhost
      - GITEA__server__HTTP_PORT=3000
      - GITEA__server__ROOT_URL=http://localhost:3001/
      - GITEA__security__SECRET_KEY=rsglider_gitea_secret_key_change_in_production
      - GITEA__security__INTERNAL_TOKEN=rsglider_gitea_internal_token_change_in_production
    volumes:
      - gitea_data:/var/lib/gitea
      - gitea_config:/etc/gitea
      - gitea_shared:/shared
      - ./docker/gitea/official-init.sh:/official-init.sh:ro
    networks:
      - rsglider-network
    depends_on:
      gitea:
        condition: service_healthy
    command: ["/bin/bash", "/official-init.sh"]

  # Gitea Git Service
  gitea:
    image: docker.gitea.com/gitea:1.23.8-rootless
    container_name: rsglider-gitea
    restart: always
    environment:
      - USER_UID=1000
      - USER_GID=1000
      - GITEA__database__DB_TYPE=postgres
      - GITEA__database__HOST=postgres:5432
      - GITEA__database__NAME=rsglider_gitea
      - GITEA__database__USER=rsglider
      - GITEA__database__PASSWD=rsglider_dev_password
      - GITEA__database__SCHEMA=public
      - GITEA__database__SSL_MODE=disable
      - GITEA__database__CHARSET=utf8mb4
      # Auto-install configuration
      - GITEA__security__INSTALL_LOCK=true
      - GITEA__server__DOMAIN=localhost
      - GITEA__server__HTTP_PORT=3000
      - GITEA__server__ROOT_URL=http://localhost:3001/
      - GITEA__server__DISABLE_SSH=false
      - GITEA__server__SSH_PORT=22
      - GITEA__server__SSH_LISTEN_PORT=22
      # Security tokens
      - GITEA__security__SECRET_KEY=rsglider_gitea_secret_key_change_in_production
      - GITEA__security__INTERNAL_TOKEN=rsglider_gitea_internal_token_change_in_production
      # Repository settings
      - GITEA__repository__DEFAULT_BRANCH=main
      - GITEA__repository__DEFAULT_PRIVATE=true
      # Service settings
      - GITEA__service__DISABLE_REGISTRATION=true
      - GITEA__service__REQUIRE_SIGNIN_VIEW=false
      - GITEA__service__DEFAULT_KEEP_EMAIL_PRIVATE=true
      # API settings
      - GITEA__api__ENABLE_SWAGGER=true
    volumes:
      - gitea_data:/var/lib/gitea
      - gitea_config:/etc/gitea
      - gitea_shared:/shared
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "3001:3000"  # Gitea Web UI
      - "2222:22"    # Gitea SSH
    networks:
      - rsglider-network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/healthz"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Inbucket Email Testing
  inbucket:
    image: inbucket/inbucket:latest
    container_name: rsglider-inbucket
    restart: unless-stopped
    ports:
      - "9025:9000"  # Web Interface
      - "2500:2500"  # SMTP Port
      - "1100:1100"  # POP3 Port
    networks:
      - rsglider-network
    environment:
      INBUCKET_WEB_ADDR: 0.0.0.0:9000
      INBUCKET_SMTP_ADDR: 0.0.0.0:2500
      INBUCKET_POP3_ADDR: 0.0.0.0:1100
      INBUCKET_STORAGE_TYPE: memory
      INBUCKET_WEB_COOKIEAUTHKEY: rsglider_inbucket_cookie_key_change_in_production
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9000/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  gitea_data:
    driver: local
  gitea_config:
    driver: local
  gitea_shared:
    driver: local
  api_uploads:
    driver: local
  api_node_modules:
    driver: local

networks:
  rsglider-network:
    driver: bridge
