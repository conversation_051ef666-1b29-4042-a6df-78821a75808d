{"name": "react-ts-vite-mui-boilerplate", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development --host", "build": "tsc -b && vite build", "lint": "eslint ."}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/roboto": "^5.1.0", "@mui/icons-material": "^6.1.1", "@mui/material": "^6.1.1", "@mui/styled-engine-sc": "^6.1.1", "@mui/styles": "^6.1.1", "@react-three/drei": "^9.114.0", "@react-three/fiber": "^8.17.8", "appwrite": "^15.0.0", "debug": "^4.3.7", "framer-motion": "^11.9.0", "react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0", "react-icons": "^5.3.0", "react-router-dom": "^6.26.2", "react-use-measure": "^2.1.1", "styled-components": "^6.1.13", "three": "^0.169.0", "vite": "^5.4.8"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/debug": "^4.1.12", "@types/node": "^22.7.4", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/three": "^0.169.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.12.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "^5.6.2", "typescript-eslint": "^8.8.0", "vite": "^5.4.8", "vite-tsconfig-paths": "^5.0.1"}}