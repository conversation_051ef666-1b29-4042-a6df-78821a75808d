{"name": "react-ts-vite-mui-boilerplate", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development --host", "build": "tsc -b && vite build", "lint": "eslint ."}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/styled-engine-sc": "^7.1.0", "@mui/system": "^7.1.0", "@mui/x-charts": "^8.5.0", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "debug": "^4.4.1", "framer-motion": "^12.15.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-router-dom": "^7.6.1", "react-use-measure": "^2.1.7", "styled-components": "^6.1.18", "three": "^0.177.0", "three-mesh-bvh": "^0.9.0", "vite": "^6.3.5"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/debug": "^4.1.12", "@types/node": "^22.15.29", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/three": "^0.176.0", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0", "vite": "^5.4.8", "vite-tsconfig-paths": "^5.1.4"}}