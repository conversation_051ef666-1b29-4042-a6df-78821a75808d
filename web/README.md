# React + TypeScript + Vite + MUI + Appwrite

## Setup Instructions

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/GliderTechnologies/rsglider_web.git
cd rsglider_web
```

### 2. Install Dependencies

```bash
yarn install
```

### 3. Create Environment Files

Create the following `.env` files in the root directory:

#### `.env.development`

```env
VITE_BASE_URL=http://localhost:5173
VITE_APPWRITE_ENDPOINT=https://dev.rsglider.org/
VITE_APPWRITE_PROJECT_ID=665b64e40008939553a9
VITE_CURRENT_BRANCH=dev
VITE_DEBUG=true
```

### 4. Run the Development Server

To start the development server, run:

```bash
yarn dev
```

For production build:

```bash
yarn build
```

### 5. Access the Application

Open your browser and navigate to the VITE_BASE_URL specified in your `.env` file.

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default tseslint.config({
  languageOptions: {
    // other options...
    parserOptions: {
      project: ["./tsconfig.node.json", "./tsconfig.app.json"],
      tsconfigRootDir: import.meta.dirname,
    },
  },
});
```

- Replace `tseslint.configs.recommended` to `tseslint.configs.recommendedTypeChecked` or `tseslint.configs.strictTypeChecked`
- Optionally add `...tseslint.configs.stylisticTypeChecked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and update the config:

```bash
npm install eslint eslint-plugin-react --save-dev
# or for yarn
yarn add eslint eslint-plugin-react --dev
```

## Troubleshooting

### Common Errors

- **Invalid project selected**: Ensure that `VITE_CURRENT_PROJECT` in your `.env` file is set to `dev`, `staging`, or `production`.
- **API configuration is missing**: Ensure that the environment variables are correctly set and loaded.
- **CORS policy errors**: Configure CORS settings in your Appwrite instance to allow requests from `http://localhost:5173`.

For production deployment, these variables are set in the CI/CD pipeline.

## Debugging

To enable debug logs, run the application with the DEBUG environment variable set to true:

```bash
DEBUG=true yarn dev
```

This will output detailed logs to the console.
