name: CI/CD Pipeline - Dynamic Environments

on:
  push:
    branches:
      - master
      - staging

jobs:
  build-and-push:
    # Use self-hosted runner instead of ubuntu-latest
    runs-on: self-hosted

    # Dynamically assign environment based on branch
    environment: ${{ github.ref == 'refs/heads/master' && 'production' || 'staging' }}

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: docker fix for lowercase owner and repo name
        run: |
          echo "OWNER_LC=${OWNER,,}" >> ${GITHUB_ENV}
          echo "REPO_LC=${REPO,,}" >> ${GITHUB_ENV}
        env:
          OWNER: "${{ github.repository_owner }}"
          REPO: "${{ github.event.repository.name }}"

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: |
            ghcr.io/${{ env.OWNER_LC }}/${{ env.REPO_LC }}:${{ github.sha }}
            ghcr.io/${{ env.OWNER_LC }}/${{ env.REPO_LC }}:latest
          build-args: |
            VITE_APPWRITE_ENDPOINT=${{ env.VITE_APPWRITE_ENDPOINT }}
            VITE_BASE_URL=${{ env.VITE_BASE_URL }}
            VITE_APPWRITE_PROJECT_ID=${{ env.VITE_APPWRITE_PROJECT_ID }}
            VITE_CURRENT_PROJECT=${{ env.VITE_CURRENT_BRANCH }}
            VITE_DEBUG=${{ env.VITE_DEBUG }}

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_KEY }}

      - name: Deploy to remote server
        env:
          SERVER: ${{ secrets.SSH_SERVER }}
          USERNAME: ${{ secrets.SSH_USERNAME }}
          PORT: ${{ secrets.SSH_PORT }}
        run: |
          ssh -o StrictHostKeyChecking=no -p $PORT $USERNAME@$SERVER << 'EOF'
            docker stop rsglider_web_${{ env.VITE_CURRENT_BRANCH }} || true
            docker rm rsglider_web_${{ env.VITE_CURRENT_BRANCH }} || true
            docker run -d --name rsglider_web_${{ env.VITE_CURRENT_BRANCH }} --net=nginx-network \
              ghcr.io/${{ env.OWNER_LC }}/${{ env.REPO_LC }}:latest
          EOF