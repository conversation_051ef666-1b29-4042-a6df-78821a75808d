{"compilerOptions": {"target": "ES2022", "useDefineForClassFields": true, "lib": ["ES2023", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "composite": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@theme/*": ["src/features/Theme/*"], "@pages/*": ["src/pages/*"], "@components/*": ["src/components/*"], "@global/*": ["src/pages/_Global/*"], "@config/*": ["src/backend/*"], "@features/*": ["src/features/*"], "@appwrite": ["src/appwrite"], "@utils/*": ["src/utils/*"], "@assets/*": ["src/assets/*"], "@styles/*": ["src/styles/*"], "@backend/*": ["src/backend/*"], "@Navbar/*": ["src/features/_global/Navbar/*"], "@types/*": ["src/backend/types/*"]}, "types": ["node"]}, "include": ["src", "vite.config.ts"]}