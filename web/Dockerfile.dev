# Development Dockerfile for RSGlider Web UI
FROM node:18-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and yarn.lock to the working directory
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install --frozen-lockfile

# Copy the rest of the application code to the working directory
COPY . .

# Expose port 5173 for Vite dev server
EXPOSE 5173

# Start the development server with host binding
CMD ["yarn", "dev", "--host", "0.0.0.0"]
