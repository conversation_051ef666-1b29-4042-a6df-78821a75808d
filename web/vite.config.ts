import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";
import tsconfigPaths from "vite-tsconfig-paths";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [react(), tsconfigPaths()],
    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer],
      },
    },
    resolve: {
      alias: {
        "@": resolve(__dirname, "./src"),
        "@features": resolve(__dirname, "./src/features"),
        "@theme": resolve(__dirname, "./src/features/theme"),
        "@global": resolve(__dirname, "./src/pages/_Global"),
        "@pages": resolve(__dirname, "./src/pages"),
        "@config": resolve(__dirname, "./src/config"),
        "@assets": resolve(__dirname, "./src/assets"),
        "@backend": resolve(__dirname, "./src/backend"),
        "@mui/styled-engine": "@mui/styled-engine-sc",
        "@Navbar": resolve(__dirname, "./src/features/_global/Navbar"),
        "@types": resolve(__dirname, "./src/backend/types"),
      },
    },
    optimizeDeps: {
      include: [
        "three",
        "react-icons",
        "@react-three/drei",
        "@react-three/fiber",
      ],
    },
    build: {
      commonjsOptions: {
        include: [/three/, /node_modules/],
      },
    },
    define: {
      "import.meta.env": JSON.stringify(env), // Ensure environment variables are available in the app
    },
  };
});
