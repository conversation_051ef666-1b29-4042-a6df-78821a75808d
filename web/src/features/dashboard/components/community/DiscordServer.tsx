import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
} from '@mui/material';
import DiscordIcon from '@components/icons/DiscordIcon';
import GroupIcon from '@mui/icons-material/Group';
import CircleIcon from '@mui/icons-material/Circle';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import { dashboardStyles } from '@styles/components/dashboard';

interface DiscordStats {
  totalMembers: number;
  onlineMembers: number;
  staffOnline: number;
  connected: boolean;
  discordUsername?: string;
}

const DiscordServer: React.FC = () => {
  const discordStats: DiscordStats = {
    totalMembers: 1250,
    onlineMembers: 423,
    staffOnline: 3,
    connected: false,
  };

  const handleConnect = () => {
    const clientId = import.meta.env.VITE_DISCORD_CLIENT_ID;
    const redirectUri = import.meta.env.VITE_DISCORD_REDIRECT_URI;
    const scope = 'identify guilds.join';
    
    window.location.href = `https://discord.com/api/oauth2/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${scope}`;
  };

  return (
    <Box>
      <Typography
        variant="h6"
        component="div"
        sx={dashboardStyles.table.header.wrapper}
      >
        Discord Integration
      </Typography>

      <Paper elevation={0} sx={dashboardStyles.contentPaper}>
        {!discordStats.connected ? (
          <Box sx={dashboardStyles.discord.notConnected}>
            <DiscordIcon sx={dashboardStyles.discord.icon} />
            <Typography variant="h6" sx={dashboardStyles.typography.header}>
              Connect your Discord Account
            </Typography>
            <Typography variant="body1" sx={dashboardStyles.typography.body}>
              Link your Discord account to access our community server and exclusive features.
            </Typography>
            <Button
              variant="contained"
              onClick={handleConnect}
              startIcon={<DiscordIcon />}
              sx={dashboardStyles.discord.button}
            >
              Connect Discord
            </Button>
          </Box>
        ) : (
          <Box>
            <Box sx={dashboardStyles.discord.header}>
              <DiscordIcon sx={dashboardStyles.discord.icon} />
              <Box>
                <Typography variant="h6" sx={dashboardStyles.typography.header}>
                  {discordStats.discordUsername}
                </Typography>
                <Typography variant="body2" sx={dashboardStyles.typography.body}>
                  Connected to Discord
                </Typography>
              </Box>
            </Box>

            <Grid container spacing={3}>
              <Grid size={{ xs: 12, sm: 4 }}>
                <Paper
                  elevation={0}
                  sx={dashboardStyles.discord.statCard}
                >
                  <GroupIcon sx={dashboardStyles.discord.icon} />
                  <Typography variant="h4" sx={dashboardStyles.typography.header}>
                    {discordStats.totalMembers}
                  </Typography>
                  <Typography variant="body2" sx={dashboardStyles.typography.body}>
                    Total Members
                  </Typography>
                </Paper>
              </Grid>
              <Grid size={{ xs: 12, sm: 4 }}>
                <Paper
                  elevation={0}
                  sx={dashboardStyles.discord.statCard}
                >
                  <CircleIcon sx={{ ...dashboardStyles.discord.icon, color: '#3ba55c' }} />
                  <Typography variant="h4" sx={dashboardStyles.typography.header}>
                    {discordStats.onlineMembers}
                  </Typography>
                  <Typography variant="body2" sx={dashboardStyles.typography.body}>
                    Members Online
                  </Typography>
                </Paper>
              </Grid>
              <Grid size={{ xs: 12, sm: 4 }}>
                <Paper
                  elevation={0}
                  sx={dashboardStyles.discord.statCard}
                >
                  <AdminPanelSettingsIcon sx={{ ...dashboardStyles.discord.icon, color: '#faa61a' }} />
                  <Typography variant="h4" sx={dashboardStyles.typography.header}>
                    {discordStats.staffOnline}
                  </Typography>
                  <Typography variant="body2" sx={dashboardStyles.typography.body}>
                    Staff Online
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default DiscordServer; 