import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
} from '@mui/material';
import DiscordIcon from '@components/icons/DiscordIcon';
import GroupIcon from '@mui/icons-material/Group';
import CircleIcon from '@mui/icons-material/Circle';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import { Grid } from '@mui/material';
import { dashboardStyles } from '@styles/components/dashboard';

interface DiscordStats {
  totalMembers: number;
  onlineMembers: number;
  staffOnline: number;
  connected: boolean;
  discordUsername?: string;
  loading?: boolean;
}

const Discord: React.FC = () => {
  const [discordStats] = useState<DiscordStats>({
    totalMembers: 0,
    onlineMembers: 0,
    staffOnline: 0,
    connected: false,
    loading: true
  });

  const handleConnect = () => {
    const applicationId = import.meta.env.VITE_DISCORD_APPLICATION_ID;
    const redirectUri = import.meta.env.VITE_DISCORD_REDIRECT_URI;
    const scope = encodeURIComponent('identify guilds.join guilds');
    const state = Math.random().toString(36).substring(7);
    localStorage.setItem('discord_oauth_state', state);
    
    const discordUrl = new URL('https://discord.com/api/oauth2/authorize');
    discordUrl.searchParams.append('client_id', applicationId);
    discordUrl.searchParams.append('redirect_uri', redirectUri);
    discordUrl.searchParams.append('response_type', 'code');
    discordUrl.searchParams.append('scope', scope);
    discordUrl.searchParams.append('state', state);
    
    window.location.href = discordUrl.toString();
  };

  return (
    <Box>
      <Typography
        variant="h6"
        component="div"
        sx={dashboardStyles.table.header.wrapper}
      >
        Discord Community
      </Typography>

      <Paper elevation={0} sx={dashboardStyles.contentPaper}>
        {!discordStats.connected ? (
          <Box>
            <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <DiscordIcon sx={{ color: '#5865F2', fontSize: 32 }} />
                <Box>
                  <Typography variant="h6" sx={dashboardStyles.typography.header}>
                    Join Our Community
                  </Typography>
                </Box>
              </Box>
              <Button
                variant="contained"
                onClick={handleConnect}
                startIcon={<DiscordIcon />}
                sx={dashboardStyles.discord.button}
              >
                Join Discord
              </Button>
            </Box>
          </Box>
        ) : (
          <Box>
            <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
              <DiscordIcon sx={{ color: '#5865F2', fontSize: 32 }} />
              <Box>
                <Typography variant="h6" sx={dashboardStyles.typography.header}>
                  {discordStats.discordUsername}
                </Typography>
                <Typography variant="body2" sx={dashboardStyles.typography.body}>
                  Connected to Discord
                </Typography>
              </Box>
            </Box>

            <Grid container spacing={3}>
              <Grid size={{ xs: 12, sm: 4 }}>
                <Paper
                  elevation={0}
                  sx={dashboardStyles.discord.statCard}
                >
                  <GroupIcon sx={dashboardStyles.discord.icon} />
                  <Typography variant="h4" sx={dashboardStyles.typography.header}>
                    {discordStats.totalMembers.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" sx={dashboardStyles.typography.body}>
                    Total Members
                  </Typography>
                </Paper>
              </Grid>
              <Grid size={{ xs: 12, sm: 4 }}>
                <Paper
                  elevation={0}
                  sx={dashboardStyles.discord.statCard}
                >
                  <CircleIcon sx={{ ...dashboardStyles.discord.icon, color: '#3ba55c' }} />
                  <Typography variant="h4" sx={dashboardStyles.typography.header}>
                    {discordStats.onlineMembers.toLocaleString()}
                  </Typography>
                  <Typography variant="body2" sx={dashboardStyles.typography.body}>
                    Members Online
                  </Typography>
                </Paper>
              </Grid>
              <Grid size={{ xs: 12, sm: 4 }}>
                <Paper
                  elevation={0}
                  sx={dashboardStyles.discord.statCard}
                >
                  <AdminPanelSettingsIcon sx={{ ...dashboardStyles.discord.icon, color: '#faa61a' }} />
                  <Typography variant="h4" sx={dashboardStyles.typography.header}>
                    {discordStats.staffOnline}
                  </Typography>
                  <Typography variant="body2" sx={dashboardStyles.typography.body}>
                    Staff Online
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default Discord; 