import React, { useEffect, useState } from 'react';
import {
    Box,
    Paper,
    Typography,
    LinearProgress,
    CircularProgress,
} from '@mui/material';
import { Grid } from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import { dashboardStyles } from '@styles/components/dashboard';
import { dashboard } from '@backend/backend';
import { ScripterMetric } from '@backend/types';

const getTrendCompareText = (compareType: 'day' | 'daybefore' | 'week' | 'month'): string => {
    switch (compareType) {
        case 'day':
            return 'vs yesterday';
        case 'daybefore':
            return 'vs two days ago';
        case 'week':
            return 'vs last week';
        case 'month':
            return 'vs last month';
    }
};

const Overview: React.FC = () => {
    const [metrics, setMetrics] = useState<ScripterMetric[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        loadOverviewData();
    }, []);

    const loadOverviewData = async () => {
        try {
            const data = await dashboard.getScripterOverview();
            setMetrics(data.metrics);
        } catch (error) {
            console.error('Failed to load overview data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    if (isLoading) {
        return (
            <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '400px'
            }}>
                <CircularProgress />
            </Box>
        );
    }

    return (
        <Box>
            <Grid container spacing={3}>
                {metrics.map((metric) => (
                    <Grid size={{ xs: 12, sm: 12, md: 5, lg: 4 }} key={metric.label}>
                        <Paper
                            elevation={0}
                            sx={dashboardStyles.contentPaper}
                        >
                            <Box sx={dashboardStyles.layout.notouch}>
                                <Typography variant="body2" sx={dashboardStyles.typography.body}>
                                    {metric.label}
                                </Typography>
                            </Box>
                            <Typography variant="h4" sx={dashboardStyles.typography.header}>
                                {metric.value}
                            </Typography>
                            <Box sx={dashboardStyles.scripter.trendBox(metric.trend >= 0)}>
                                {metric.trend >= 0 ? <TrendingUpIcon /> : <TrendingDownIcon />}
                                <Typography variant="body2">
                                    {Math.abs(metric.trend)}% {getTrendCompareText(metric.compareType)}
                                </Typography>
                            </Box>
                            <LinearProgress
                                variant="determinate"
                                value={Math.min(100, Math.max(0, 50 + (metric.trend)))}
                                sx={dashboardStyles.scripter.progressBar(metric.trend)}
                            />
                        </Paper>
                    </Grid>
                ))}
            </Grid>
        </Box>
    );
};

export default Overview;
