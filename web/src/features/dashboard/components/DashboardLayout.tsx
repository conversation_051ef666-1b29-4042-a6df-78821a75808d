import React from 'react';
import { Box, Grid, List, ListItem, ListItemIcon, ListItemText, Paper, Typography } from '@mui/material';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import DashboardIcon from '@mui/icons-material/Dashboard';
import HistoryIcon from '@mui/icons-material/History';
import SecurityIcon from '@mui/icons-material/Security';
import DevicesIcon from '@mui/icons-material/Devices';
import StorefrontIcon from '@mui/icons-material/Storefront';
import BarChartIcon from '@mui/icons-material/BarChart';
import DescriptionIcon from '@mui/icons-material/Description';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
import DiscordIcon from '@components/icons/DiscordIcon';
import CodeIcon from '@mui/icons-material/Code';
import TableChartIcon from '@mui/icons-material/TableChart';
import ReceiptIcon from '@mui/icons-material/Receipt';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import PeopleIcon from '@mui/icons-material/People';
import { dashboardStyles } from '@styles/components/dashboard';
import { useAuth } from '@features/auth/hooks/useAuth';

interface MenuItem {
  text: string;
  icon: JSX.Element;
  path: string;
  endIcon?: JSX.Element;
}

interface MenuSection {
  title: string;
  items: MenuItem[];
}

const menuSections: MenuSection[] = [
  {
    title: 'User Account',
    items: [
      { text: 'Overview', icon: <DashboardIcon />, path: '/dashboard' },
      { text: 'Order History', icon: <HistoryIcon />, path: '/dashboard/orders' },
      { text: 'Account Security', icon: <SecurityIcon />, path: '/dashboard/security' },
      { text: 'Session Manager', icon: <DevicesIcon />, path: '/dashboard/sessions' },
    ]
  },
  {
    title: 'Community',
    items: [
      { text: 'Discord Server', icon: <DiscordIcon />, path: '/dashboard/community/discord' },
    ]
  },
  {
    title: 'Developer',
    items: [
      { text: 'Dashboard', icon: <StorefrontIcon />, path: '/dashboard/scripter/dashboard' },
      { text: 'Chart View', icon: <BarChartIcon />, path: '/dashboard/scripter/chart' },
      { text: 'Table View', icon: <TableChartIcon />, path: '/dashboard/scripter/table' },
      { text: 'Sales History', icon: <ReceiptIcon />, path: '/dashboard/scripter/sales' },
      { text: 'Projects', icon: <CodeIcon />, path: '/dashboard/scripter/projects' },
      { text: "Documentation", icon: <DescriptionIcon />, path: '/docs', endIcon: <OpenInNewIcon fontSize="small" /> },
    ]
  },
  {
    title: 'Administration',
    items: [
      { text: 'Users', icon: <PeopleIcon />, path: '/dashboard/admin/users' },
      { text: 'Settings', icon: <AdminPanelSettingsIcon />, path: '/dashboard/admin/settings' },
    ]
  }
];

const DashboardLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  // Filter sections based on user role
  const filteredSections = menuSections.filter(section => {
    if (section.title === 'Administration') {
      return user?.roles.includes('admin') || 1 === 1;
    }
    if (section.title === 'Developer') {
      return user?.roles.includes('scripter') || user?.roles.includes('admin') || 1 === 1;
    }
    return true;
  });

  return (
    <Box sx={dashboardStyles.layout.wrapper}>
      <Grid container spacing={12}>
        <Grid size={{ xs: 12, md: 3 }} sx={dashboardStyles.layout.notouch}>
          <Paper elevation={0} sx={dashboardStyles.layout.sidebar}>
            {filteredSections.map((section) => (
              <Box
                key={section.title}
                sx={dashboardStyles.menuSection.wrapper}
              >
                <Typography
                  variant="h6"
                  component="div"
                  sx={dashboardStyles.menuSection.header}
                >
                  {section.title}
                </Typography>

                <List sx={dashboardStyles.menuSection.list}>
                  {section.items.map((item) => {
                    const isActive = location.pathname === item.path;
                    return (
                      <ListItem
                        key={item.text}
                        onClick={() => navigate(item.path)}
                        sx={dashboardStyles.menuItem(isActive)}
                      >
                        <ListItemIcon sx={dashboardStyles.menuItemIcon}>
                          {item.icon}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              {item.text}
                              {item.endIcon}
                            </Box>
                          }
                          primaryTypographyProps={{
                            fontWeight: isActive ? 600 : 400,
                          }}
                        />
                      </ListItem>
                    );
                  })}
                </List>
              </Box>
            ))}
          </Paper>
        </Grid>
        <Grid size={{ xs: 12, md: 9 }}>
          <Outlet />
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardLayout;