import React from 'react';
import { Box, Paper } from '@mui/material';
import PasswordChange from './PasswordChange';
import TwoFactorSettings from './TwoFactorSettings';
import { dashboardStyles } from '@styles/components/dashboard';

const AccountSecurity: React.FC = () => {
  return (
    <Box sx={dashboardStyles.layout.notouch}>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        <Paper elevation={0} sx={dashboardStyles.contentPaper}>
          <PasswordChange textFieldSx={dashboardStyles.textField} />
        </Paper>
        <Paper elevation={0} sx={dashboardStyles.contentPaper}>
          <TwoFactorSettings
            switchSx={dashboardStyles.security.twoFactor.switch}
            switchLabelSx={dashboardStyles.security.twoFactor.label}
          />
        </Paper>
      </Box>
    </Box>
  );
};

export default AccountSecurity;