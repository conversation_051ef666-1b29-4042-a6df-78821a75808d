import React, { useState, useEffect } from 'react';
import {
  Box,
  Switch,
  FormControlLabel,
  Button,
  Typography,
  SxProps,
  Theme,
  Alert,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Chip,
} from '@mui/material';
import { dashboardStyles } from '@styles/components/dashboard';
import { twoFactor, account } from '@backend/backend';
import { useAuth } from '@features/auth/hooks/useAuth';
import { logger } from '@utils/logger';

// Update the interface to include the props
interface TwoFactorSettingsProps {
  switchSx?: SxProps<Theme>;
}

// Simple interfaces that match the backend DTOs
interface SetupData {
  secret?: string;
  qrCodeUrl?: string;
  manualEntryKey?: string;
  issuer?: string;
}

const TwoFactorSettings: React.FC<TwoFactorSettingsProps> = ({ switchSx }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Setup flow states
  const [showSetupDialog, setShowSetupDialog] = useState(false);
  const [setupData, setSetupData] = useState<SetupData | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);

  // Disable flow states
  const [showDisableDialog, setShowDisableDialog] = useState(false);
  const [password, setPassword] = useState('');
  const [disableCode, setDisableCode] = useState('');

  // For now, we'll fetch the user's 2FA status directly from the API
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);

  // Fetch current 2FA status
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userData = await account.get();
        setTwoFactorEnabled(userData.twoFactorEnabled);
      } catch (error) {
        logger.error('Failed to fetch user data:', error);
      }
    };

    if (user) {
      fetchUserData();
    }
  }, [user]);

  const clearMessages = () => {
    setError('');
    setSuccess('');
  };

  const downloadBackupCodes = () => {
    const codesText = backupCodes.join('\n');
    const blob = new Blob([`RSGlider Two-Factor Authentication Backup Codes\n\nSave these codes in a safe place. You can use them to access your account if you lose your authenticator device.\n\n${codesText}\n\nGenerated: ${new Date().toLocaleString()}`], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'rsglider_backup_codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const copyBackupCodes = async () => {
    const codesText = backupCodes.join('\n');
    try {
      await navigator.clipboard.writeText(codesText);
      setSuccess('Backup codes copied to clipboard!');
    } catch (error) {
      setError('Failed to copy backup codes');
    }
  };

  const handleCloseSetupDialog = () => {
    setShowSetupDialog(false);
    setVerificationCode('');
    setSetupData(null);
    setBackupCodes([]);
    clearMessages();
  };

  const handleSwitchToggle = () => {
    if (twoFactorEnabled) {
      // If 2FA is enabled, show disable dialog
      setShowDisableDialog(true);
    } else {
      // If 2FA is disabled, start setup process
      handleSetup2FA();
    }
  };

  const handleSetup2FA = async () => {
    try {
      setLoading(true);
      clearMessages();

      const response = await twoFactor.setup();
      setSetupData(response);
      setShowSetupDialog(true);
    } catch (error) {
      logger.error('2FA setup failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to setup 2FA');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifySetup = async () => {
    if (!verificationCode.trim()) {
      setError('Please enter the verification code');
      return;
    }

    try {
      setLoading(true);
      clearMessages();

      const response = await twoFactor.verifySetup(verificationCode);
      setBackupCodes(response.backupCodes || []);
      setSuccess('Two-factor authentication has been successfully enabled!');

      // Update local 2FA status
      setTwoFactorEnabled(true);

      // Don't auto-close dialog - let user save backup codes first
    } catch (error) {
      logger.error('2FA verification failed:', error);
      setError(error instanceof Error ? error.message : 'Invalid verification code');
    } finally {
      setLoading(false);
    }
  };

  const handleDisable2FA = async () => {
    if (!password.trim()) {
      setError('Please enter your password');
      return;
    }

    if (!disableCode.trim()) {
      setError('Please enter your 2FA code');
      return;
    }

    try {
      setLoading(true);
      clearMessages();

      await twoFactor.disable(password, disableCode);
      setSuccess('Two-factor authentication has been successfully disabled');

      // Update local 2FA status
      setTwoFactorEnabled(false);

      // Close dialog and reset form
      setShowDisableDialog(false);
      setPassword('');
      setDisableCode('');
    } catch (error) {
      logger.error('2FA disable failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to disable 2FA');
    } finally {
      setLoading(false);
    }
  };



  const handleCloseDisableDialog = () => {
    setShowDisableDialog(false);
    setPassword('');
    setDisableCode('');
    clearMessages();
  };

  return (
    <Box>
      <Typography variant="h6" sx={dashboardStyles.typography.subheader}>
        Two-Factor Authentication
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Box sx={dashboardStyles.security.twoFactor.wrapper}>
        <Box>
          <Typography variant="body1" sx={dashboardStyles.typography.body}>
            Two-factor authentication adds an additional layer of security to your account by requiring more than just a password to sign in.
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={twoFactorEnabled}
                  disabled={loading}
                  onChange={handleSwitchToggle}
                  sx={switchSx}
                />
              }
              label={
                <span style={{ color: 'white', fontWeight: 500 }}>
                  Two-Factor Authentication
                </span>
              }
              sx={{
                color: 'white !important',
                '& .MuiFormControlLabel-label': {
                  color: 'white !important',
                  fontWeight: 500,
                },
                '& .MuiTypography-root': {
                  color: 'white !important',
                },
                '& span': {
                  color: 'white !important',
                },
              }}
            />
            <Chip
              label={twoFactorEnabled ? 'Enabled' : 'Disabled'}
              color={twoFactorEnabled ? 'success' : 'default'}
              size="small"
              sx={{
                backgroundColor: twoFactorEnabled ? 'success.main' : 'rgba(255, 255, 255, 0.1)',
                color: twoFactorEnabled ? 'success.contrastText' : 'white',
                fontWeight: 500,
              }}
            />
          </Box>
        </Box>
      </Box>

      {/* Setup 2FA Dialog */}
      <Dialog
        open={showSetupDialog}
        onClose={handleCloseSetupDialog}
        maxWidth="sm"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              ...dashboardStyles.contentPaper,
              color: 'white',
            }
          }
        }}
      >
        <DialogTitle sx={{ color: 'white', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
          Setup Two-Factor Authentication
        </DialogTitle>
        <DialogContent sx={{ color: 'white' }}>
          {setupData && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, pt: 1 }}>
              <Typography variant="body2">
                Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.):
              </Typography>

              {setupData.qrCodeUrl && (
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <img
                    src={setupData.qrCodeUrl}
                    alt="2FA QR Code"
                    style={{ maxWidth: '200px', height: 'auto' }}
                  />
                </Box>
              )}

              {setupData.manualEntryKey && (
                <Box sx={{
                  p: 2,
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: 1,
                  border: '1px solid rgba(255, 255, 255, 0.2)'
                }}>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    Or enter this key manually:
                  </Typography>
                  <Typography variant="body2" sx={{
                    fontFamily: 'monospace',
                    fontWeight: 'bold',
                    wordBreak: 'break-all',
                    color: '#ffa726'
                  }}>
                    {setupData.manualEntryKey}
                  </Typography>
                </Box>
              )}

              <TextField
                label="Verification Code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                placeholder="Enter 6-digit code"
                fullWidth
                slotProps={{ htmlInput: { maxLength: 6 } }}
                sx={dashboardStyles.textField}
              />

              {error && (
                <Alert severity="error">{error}</Alert>
              )}

              {success && (
                <Alert severity="success">{success}</Alert>
              )}

              {backupCodes.length > 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Backup Codes
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    Save these backup codes in a safe place. You can use them to access your account if you lose your authenticator device:
                  </Typography>
                  <Box sx={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gap: 1,
                    p: 2,
                    bgcolor: 'rgba(0, 0, 0, 0.1)',
                    borderRadius: 1,
                    mb: 2
                  }}>
                    {backupCodes.map((code, index) => (
                      <Typography key={index} variant="body2" sx={{ fontFamily: 'monospace' }}>
                        {code}
                      </Typography>
                    ))}
                  </Box>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                    <Button
                      onClick={downloadBackupCodes}
                      variant="outlined"
                      size="small"
                      sx={{
                        borderColor: '#00ff88',
                        color: '#00ff88',
                        '&:hover': {
                          borderColor: '#00cc6a',
                          backgroundColor: 'rgba(0, 255, 136, 0.1)'
                        }
                      }}
                    >
                      Download Codes
                    </Button>
                    <Button
                      onClick={copyBackupCodes}
                      variant="outlined"
                      size="small"
                      sx={{
                        borderColor: '#00ff88',
                        color: '#00ff88',
                        '&:hover': {
                          borderColor: '#00cc6a',
                          backgroundColor: 'rgba(0, 255, 136, 0.1)'
                        }
                      }}
                    >
                      Copy to Clipboard
                    </Button>
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
          <Button
            onClick={handleCloseSetupDialog}
            sx={{
              color: 'rgba(255, 255, 255, 0.7)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)'
              }
            }}
          >
            {backupCodes.length > 0 ? 'Close' : 'Cancel'}
          </Button>
          {backupCodes.length === 0 && (
            <Button
              onClick={handleVerifySetup}
              variant="contained"
              disabled={loading || !verificationCode.trim()}
              sx={{
                backgroundColor: '#00ff88',
                color: '#000',
                '&:hover': {
                  backgroundColor: '#00cc6a'
                },
                '&:disabled': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  color: 'rgba(255, 255, 255, 0.3)'
                }
              }}
            >
              {loading ? <CircularProgress size={20} sx={{ color: '#000' }} /> : 'Verify & Enable'}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Disable 2FA Dialog */}
      <Dialog
        open={showDisableDialog}
        onClose={handleCloseDisableDialog}
        maxWidth="sm"
        fullWidth
        slotProps={{
          paper: {
            sx: {
              ...dashboardStyles.contentPaper,
              color: 'white',
            }
          }
        }}
      >
        <DialogTitle sx={{ color: 'white', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
          Disable Two-Factor Authentication
        </DialogTitle>
        <DialogContent sx={{ color: 'white' }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, pt: 1 }}>
            <Typography variant="body2">
              To disable two-factor authentication, please enter your password and current 2FA code:
            </Typography>

            <TextField
              label="Password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              fullWidth
              required
              sx={dashboardStyles.textField}
            />

            <TextField
              label="2FA Code"
              value={disableCode}
              onChange={(e) => setDisableCode(e.target.value)}
              placeholder="Enter 6-digit code"
              fullWidth
              required
              slotProps={{ htmlInput: { maxLength: 6 } }}
              sx={dashboardStyles.textField}
            />

            {error && (
              <Alert severity="error">{error}</Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, borderTop: '1px solid rgba(255, 255, 255, 0.1)' }}>
          <Button
            onClick={handleCloseDisableDialog}
            sx={{
              color: 'rgba(255, 255, 255, 0.7)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)'
              }
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDisable2FA}
            variant="contained"
            disabled={loading || !password.trim() || !disableCode.trim()}
            sx={{
              backgroundColor: '#ff4444',
              color: '#fff',
              '&:hover': {
                backgroundColor: '#cc3333'
              },
              '&:disabled': {
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                color: 'rgba(255, 255, 255, 0.3)'
              }
            }}
          >
            {loading ? <CircularProgress size={20} sx={{ color: '#fff' }} /> : 'Disable 2FA'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TwoFactorSettings;