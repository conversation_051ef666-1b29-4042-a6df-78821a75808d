import React from 'react';
import { Box, Paper, Typography, Button } from '@mui/material';
import { dashboardStyles } from '@styles/components/dashboard';
import { useAuth } from '@features/auth/hooks/useAuth';
import { dashboard } from '@backend/backend';
import type { DashboardCardData, Subscription, TableHeader } from '../../types';
import CustomTable, { TableColumn } from '@features/_global/components/Table';
import { formatDateTime } from '@utils/formatters';

const Overview: React.FC = () => {
  const { user } = useAuth();
  const displayName = user?.name || user?.email || '';
  const { stats, cardData } = dashboard.getDashboardOverview();

  const typedCardData = cardData as unknown as DashboardCardData[];

  const getColumns = (headers: TableHeader[]): TableColumn<Subscription>[] => 
    headers.map(header => {
      const align = header.align === 'inherit' || header.align === 'justify' 
        ? undefined 
        : header.align as 'left' | 'right' | 'center' | undefined;

      return {
        id: header.id,
        label: header.label,
        align,
        format: (value) => {
          if (header.id === 'amount') {
            return `$${(value as number).toFixed(2)}`;
          }
          if (header.id === 'add_date' || header.id === 'expire_date') {
            return formatDateTime(value as string);
          }
          return value;
        },
      };
    });

  return (
    <Box>
      <Paper
        elevation={0}
        sx={{
          ...dashboardStyles.overview.statCard,
          mb: 3,
          display: 'flex',
          alignItems: 'left',
          gap: 2,
          p: 3,
        }}
      >
        <Box sx={dashboardStyles.layout.notouch}>
          <Typography variant="h4" sx={dashboardStyles.typography.subheader}>
            Welcome back, {displayName}!
          </Typography>
          <Typography variant="body1" sx={dashboardStyles.typography.body}>
            Last login: {stats.lastLoginDate} at {stats.lastLoginTime}
          </Typography>
          <Typography variant="body1" sx={dashboardStyles.typography.body}>
            Scripts Running: 
            <span style={{ fontWeight: 700, color: stats.scriptsRunning === stats.scriptsMax ? '#ff4444' : '#4caf50' }}>
              {stats.scriptsRunning} / {stats.scriptsMax === 999 ? 'Unlimited' : stats.scriptsMax}
            </span>
          </Typography>
        </Box>
      </Paper>

      {typedCardData.map((card, index) => (
        card.enabled && (
          <Box key={card.title} sx={{ mb: index === typedCardData.length - 1 ? 0 : 3 }}>
            <Typography
              variant="h6"
              component="div"
              sx={dashboardStyles.table.header.wrapper}
            >
              {card.title}
            </Typography>

            {(!card.activeSubscription || card.activeSubscription.length === 0) ? (
              <Paper
                elevation={0}
                sx={{
                  ...dashboardStyles.overview.statCard,
                  textAlign: 'center',
                  py: 4,
                }}
              >
                <Typography variant="body1" sx={{ mb: 2, ...dashboardStyles.typography.body }}>
                  {card.message}
                </Typography>
                {card.buttonLink && card.buttonText && (
                  <Button
                    variant="outlined"
                    color="primary"
                    size="small"
                    href={card.buttonLink}
                  >
                    {card.buttonText}
                  </Button>
                )}
              </Paper>
            ) : (
              <CustomTable
                columns={getColumns(card.tableHeaders)}
                data={card.activeSubscription}
                defaultRowsPerPage={5}
              />
            )}
          </Box>
        )
      ))}
    </Box>
  );
};

export default Overview;