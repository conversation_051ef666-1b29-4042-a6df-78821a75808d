import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Grid,
  Tabs,
  Tab,
  Chip,
  SxProps,
  Theme,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { AdminUser, UserActivity, UserDetails as UserDetailsType, UserLoginHistory, UserPurchase, UserClient } from '@backend/types';
import { colors } from '@styles/constants';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import StatusChip from '@features/_global/components/StatusChip';
import { getUserDetails } from '@backend/mock/dashboard/admin/mock_user_details';
import { dashboardStyles } from '@styles/components/dashboard';
import DeleteIcon from '@mui/icons-material/Delete';
import CustomTable, { TableColumn } from '@features/_global/components/Table';
import TabPanel from '@features/_global/components/TabPanel';
import { formatDateTime, getTimeAgo } from '@utils/formatters';

interface UserDetailsProps {
  user: AdminUser;
  onClose: () => void;
}

const UserDetails: React.FC<UserDetailsProps> = ({ user, onClose }) => {
  const [tabValue, setTabValue] = useState(0);
  const [userDetails, setUserDetails] = useState<UserDetailsType | null>(null);

  useEffect(() => {
    setUserDetails(getUserDetails(user.id));
  }, [user.id]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getBooleanIcon = (value: boolean) => {
    if (value) {
      return <CheckCircleIcon sx={{ color: colors.success.main }} />;
    }
    return <CancelIcon sx={{ color: colors.error.main }} />;
  };

  const getStatusChipStyles = (isActive: boolean) => {
    const baseStyles = dashboardStyles.table.chip.base as SxProps<Theme>;
    const statusStyles = isActive 
      ? dashboardStyles.table.chip.success as SxProps<Theme>
      : dashboardStyles.table.chip.default as SxProps<Theme>;

    return {
      ...baseStyles,
      ...statusStyles,
    } as SxProps<Theme>;
  };

  const getPurchaseStatusChipStyles = (status: string) => {
    const baseStyles = dashboardStyles.table.chip.base as SxProps<Theme>;
    let statusStyles: SxProps<Theme>;

    switch (status) {
      case 'completed':
        statusStyles = dashboardStyles.table.chip.success as SxProps<Theme>;
        break;
      case 'pending':
        statusStyles = dashboardStyles.table.chip.warning as SxProps<Theme>;
        break;
      default:
        statusStyles = dashboardStyles.table.chip.error as SxProps<Theme>;
    }

    return {
      ...baseStyles,
      ...statusStyles,
    } as SxProps<Theme>;
  };

  const ipHistoryColumns: TableColumn<UserLoginHistory>[] = [
    { 
      id: 'timestamp', 
      label: 'Date & Time',
      format: formatDateTime,
    },
    { id: 'ipAddress', label: 'IP Address' },
    { id: 'userAgent', label: 'Client' },
    {
      id: 'isActiveSession',
      label: 'Status',
      align: 'center',
      renderCell: (row) => (
        <Chip
          label={row.isActiveSession ? 'Active' : 'Inactive'}
          size="small"
          sx={getStatusChipStyles(row.isActiveSession)}
        />
      ),
    },
  ];

  const activityColumns: TableColumn<UserActivity>[] = [
    {
      id: 'timestamp',
      label: 'Date & Time',
      format: formatDateTime,
    },
    {
      id: 'actionType',
      label: 'Action',
      format: (value: string) => value.replace(/_/g, ' ').toUpperCase(),
    },
    { id: 'details', label: 'Details' },
  ];

  const purchaseColumns: TableColumn<UserPurchase>[] = [
    { id: 'transactionId', label: 'Transaction ID' },
    {
      id: 'date',
      label: 'Date',
      format: formatDateTime,
    },
    { id: 'productName', label: 'Product' },
    {
      id: 'amount',
      label: 'Amount',
      align: 'right',
      format: (value: number) => `$${value.toFixed(2)}`,
    },
    {
      id: 'status',
      label: 'Status',
      renderCell: (row) => (
        <Chip
          label={row.status}
          size="small"
          sx={getPurchaseStatusChipStyles(row.status)}
        />
      ),
    },
  ];

  const clientColumns: TableColumn<UserClient>[] = [
    { id: 'operatingSystem', label: 'Operating System' },
    { id: 'ipAddress', label: 'IP Address' },
    { id: 'scriptName', label: 'Script' },
    { id: 'scriptVersion', label: 'Version' },
    { id: 'runTime', label: 'Run Time' },
    {
      id: 'lastHeartbeat',
      label: 'Last Heartbeat',
      format: getTimeAgo,
    },
    {
      id: 'actions',
      label: 'Actions',
      align: 'right',
      renderCell: (row) => (
        <IconButton
          onClick={() => {
            // TODO: Implement client termination
            console.log('Terminate client:', row.id);
          }}
          sx={{
            color: colors.error.main,
            '&:hover': {
              backgroundColor: 'rgba(244, 67, 54, 0.1)',
            },
          }}
        >
          <DeleteIcon />
        </IconButton>
      ),
    },
  ];

  return (
    <Box>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        mb: 2,
        pb: 2,
        borderBottom: `1px solid ${colors.glass.border}`,
      }}>
        <Typography variant="h6" sx={{ color: colors.text.primary }}>
          {user.username}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <StatusChip 
              status={user.role} 
              type="user-role"
            />
          </Box>
        </Typography>
        <IconButton onClick={onClose} sx={{ color: colors.text.primary }}>
          <CloseIcon />
        </IconButton>
      </Box>

      <Box sx={{ borderBottom: `1px solid ${colors.glass.border}` }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange}
          sx={{
            '& .MuiTab-root': {
              color: colors.text.secondary,
              '&.Mui-selected': {
                color: colors.text.primary,
              },
            },
            '& .MuiTabs-indicator': {
              backgroundColor: colors.primary.main,
            },
          }}
        >
          <Tab label="Details" />
          <Tab label="IP History" />
          <Tab label="Activity" />
          <Tab label="Purchase History" />
          <Tab label="Clients" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box>
                <Typography variant="body2" sx={{ color: colors.text.secondary }}>
                  Email
                </Typography>
                <Typography variant="body1" sx={{ color: colors.text.primary }}>
                  {user.email}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box>
                <Typography variant="body2" sx={{ color: colors.text.secondary }}>
                  Two-Factor Authentication
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'center', mt: 0.5 }}>
                  <Typography variant="body1" sx={{ color: colors.text.primary }}>
                    {user.has2FA ? 'Enabled' : 'Disabled'}
                  </Typography>
                  {getBooleanIcon(user.has2FA)}
                </Box>
              </Box>

              <Box>
                <Typography variant="body2" sx={{ color: colors.text.secondary }}>
                  Client Usage
                </Typography>
                <Typography variant="body1" sx={{ color: colors.text.primary }}>
                  {user.clientCount} / {user.maxClients === 999 ? '∞' : user.maxClients}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 4 }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box>
                <Typography variant="body2" sx={{ color: colors.text.secondary }}>
                  Registration Date
                </Typography>
                <Typography variant="body1" sx={{ color: colors.text.primary }}>
                  {formatDateTime(user.registrationDate)}
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" sx={{ color: colors.text.secondary }}>
                  Last Login
                </Typography>
                <Typography variant="body1" sx={{ color: colors.text.primary }}>
                  {formatDateTime(user.lastLoginDate)}
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <CustomTable
          columns={ipHistoryColumns}
          data={userDetails?.loginHistory || []}
          defaultRowsPerPage={5}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <CustomTable
          columns={activityColumns}
          data={userDetails?.activities || []}
          defaultRowsPerPage={5}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={3}>
        <CustomTable
          columns={purchaseColumns}
          data={userDetails?.purchases || []}
          defaultRowsPerPage={5}
        />
      </TabPanel>

      <TabPanel value={tabValue} index={4}>
        <CustomTable
          columns={clientColumns}
          data={userDetails?.clients || []}
          defaultRowsPerPage={5}
        />
      </TabPanel>
    </Box>
  );
};

export default UserDetails; 