import React, { createContext, useState, useEffect, useCallback } from 'react';
import { User } from '@backend/types';
import { account } from '@backend/backend';
import { AppwriteException } from 'appwrite';

export interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  role: string | null;
  setIsAuthenticated: (value: boolean) => void;
  setUser: (user: User | null) => void;
  setRole: (role: string | null) => void;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [role, setRole] = useState<string | null>(null);

  const login = async (email: string, password: string) => {
    try {
      await account.createEmailPasswordSession(email, password);
      await checkAuth(); // This will set the user and isAuthenticated state
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await account.deleteSession('current');
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      setUser(null);
      setRole(null);
      setIsAuthenticated(false);
      setIsLoading(false);
    }
  }, []);

  const checkAuth = useCallback(async () => {
    try {
      setIsLoading(true);
      const user = await account.get();
      console.log('User data:', user);
      setUser(user as User);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Auth check failed:', error);
      if (error instanceof AppwriteException) {
        if (error.code === 401) {
          // User is not authenticated, this is an expected state
          console.log('User is not authenticated');
          setUser(null);
          setIsAuthenticated(false);
        } else {
          console.error('Appwrite error code:', error.code);
          console.error('Appwrite error message:', error.message);
          console.error('Appwrite error type:', error.type);
        }
      }
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      isLoading,
      user,
      role,
      setIsAuthenticated,
      setUser,
      setRole,
      login,
      logout,
      checkAuth
    }}>
      {children}
    </AuthContext.Provider>
  );
};