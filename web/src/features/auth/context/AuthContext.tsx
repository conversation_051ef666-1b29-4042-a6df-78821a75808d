import React, { createContext, useState, useEffect, useCallback } from 'react';
import { User } from '@backend/types';
import { auth, account, api } from '@backend/backend';
import { logger } from '@utils/logger';
import { useNavigate } from 'react-router-dom';

// Add these functions to handle cookies
const getCookie = (name: string): string | null => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
  return null;
};

export interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  role: string | null;
  setIsAuthenticated: (value: boolean) => void;
  setUser: (user: User | null) => void;
  setRole: (role: string | null) => void;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    // Initialize with token check
    return !!getCookie('authToken');
  });
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [role, setRole] = useState<string | null>(null);
  const navigate = useNavigate();

  const login = async (email: string, password: string, rememberMe = false) => {
    try {
      const response = await auth.login(email, password);
      if (!response || !response.accessToken) {
        throw new Error('Invalid response from server');
      }

      // Set cookie with expiration based on rememberMe
      const cookieOptions = rememberMe
        ? `max-age=${30 * 24 * 60 * 60}; ` // 30 days in seconds
        : 'max-age=86400; '; // 1 day in seconds

      document.cookie = `authToken=${response.accessToken}; path=/; secure; samesite=strict; ${cookieOptions}`;
      
      api.setToken(response.accessToken);
      await checkAuth();
    } catch (error) {
      throw error;
    }
  };

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await auth.logout();
    } catch (error) {
      logger.error('Error during logout:', error);
    } finally {
      // Clear the auth cookie
      document.cookie = 'authToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      
      api.clearToken();
      setUser(null);
      setRole(null);
      setIsAuthenticated(false);
      setIsLoading(false);
      
      // Redirect to landing page after logout instead of login
      navigate('/');
    }
  }, []);

  const checkAuth = useCallback(async () => {
    setIsLoading(true);

    try {
      const token = getCookie('authToken');
      
      if (!token) {
        // If no token, clear auth state without API call
        api.clearToken();
        setUser(null);
        setIsAuthenticated(false);
        setRole(null);
        return;
      }

      api.setToken(token);
      const userAccount = await account.get();
      setUser(userAccount.user);
      setIsAuthenticated(true);
      setRole(userAccount.user.roles?.[0]?.name ?? null);
    } catch (error) {
      logger.error('Auth check failed:', error);
      // Clear auth state on error
      api.clearToken();
      setUser(null);
      setIsAuthenticated(false);
      setRole(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        role,
        setIsAuthenticated,
        setUser,
        setRole,
        login,
        logout,
        checkAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
