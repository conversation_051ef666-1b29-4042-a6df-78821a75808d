import React, { useState } from 'react';
import { <PERSON><PERSON>ield, Typography, CircularProgress, Alert, FormControlLabel, Checkbox, Button } from '@mui/material';
import { useNavigate, Link as RouterLink, useLocation } from 'react-router-dom';
import { Box } from '@mui/material';
import { useAuth } from '@features/auth/hooks/useAuth';
import { logger } from '@utils/logger';
import { authStyles } from '@styles/components/auth';

const Login: React.FC = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [twoFactorCode, setTwoFactorCode] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [requires2FA, setRequires2FA] = useState(false);

  const from = (location.state as { from?: Location })?.from?.pathname || '/';

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      await login(email, password, rememberMe, twoFactorCode);
      navigate(from, { replace: true });
    } catch (error) {
      logger.error(error as Error);
      if (error instanceof Error) {
        // Check if error is about 2FA being required
        if (error.message.includes('Two-factor authentication code is required')) {
          setRequires2FA(true);
          setError('Please enter your two-factor authentication code.');
        } else {
          setError(error.message);
        }
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={authStyles.container}>
      <Typography variant="h1" sx={authStyles.header}>
        Login
      </Typography>
      
      {error && (
        <Alert severity="error" sx={authStyles.alert}>
          {error}
        </Alert>
      )}
      
      <form onSubmit={handleLogin} style={{ width: '100%' }}>
        <TextField
          label="Email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          fullWidth
          autoComplete="off"
          sx={authStyles.textField}
        />
        
        <TextField
          label="Password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          fullWidth
          autoComplete="off"
          sx={authStyles.textField}
        />

        {requires2FA && (
          <TextField
            label="Two-Factor Authentication Code"
            type="text"
            value={twoFactorCode}
            onChange={(e) => setTwoFactorCode(e.target.value)}
            placeholder="Enter 6-digit code"
            required
            fullWidth
            autoComplete="off"
            inputProps={{ maxLength: 6 }}
            sx={authStyles.textField}
          />
        )}

        <FormControlLabel
          control={
            <Checkbox
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
              sx={authStyles.checkbox}
            />
          }
          label="Remember me"
          sx={authStyles.checkboxLabel}
        />
        
        <Button 
          type="submit" 
          disabled={loading} 
          sx={authStyles.button}
          fullWidth
        >
          {loading ? <CircularProgress size={24} /> : "Login"}
        </Button>
      </form>
      
      <Box sx={authStyles.links}>
        <RouterLink to="/auth/forgot-password">
          Forgot Password?
        </RouterLink>
        <RouterLink to="/auth/register">
          Register
        </RouterLink>
      </Box>
    </Box>
  );
};

export default Login;
