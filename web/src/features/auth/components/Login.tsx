import React, { useState, useEffect } from 'react';
import { TextField, Typography, CircularProgress, <PERSON>ert, FormControlLabel, Checkbox } from '@mui/material';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { AppwriteException } from 'appwrite';
import styled from '@emotion/styled';
import { useAuth } from '@features/auth/hooks/useAuth';

const StyledHeader = styled(Typography)`
  font-family: 'Poppins', sans-serif;
  font-weight: 400;
  font-size: 1.5rem;
  color: #FFA500;
  margin-bottom: 24px;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
`;

const Login: React.FC = () => {
  const { login, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState(() => localStorage.getItem('rememberedEmail') || '');
  const [password, setPassword] = useState(() => localStorage.getItem('rememberedPassword') || '');
  const [rememberMe, setRememberMe] = useState(!!localStorage.getItem('rememberedEmail'));
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, isLoading, navigate]);

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    try {
      await login(email, password);
      // After successful login, navigation will be handled by the useEffect above
    } catch (error) {
      if (error instanceof AppwriteException) {
        setError(error.message);
      } else {
        setError("An unexpected error occurred. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <FormContainer>
      <StyledHeader variant="h1">Login</StyledHeader>
      {error && (
        <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
          {error}
        </Alert>
      )}
      <form onSubmit={handleLogin} style={{ width: '100%' }}>
        <StyledTextField
          label="Email"
          variant="outlined"
          fullWidth
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          disabled={loading}
        />
        <StyledTextField
          label="Password"
          type="password"
          variant="outlined"
          fullWidth
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          disabled={loading}
        />
        <FormControlLabel
          control={
            <Checkbox
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
              sx={{
                color: 'rgba(255, 165, 0, 0.5)',
                '&.Mui-checked': {
                  color: '#FFA500',
                },
              }}
            />
          }
          label="Remember me"
          sx={{
            color: '#FFA500',
            '& .MuiFormControlLabel-label': {
              fontSize: '0.9rem',
            },
          }}
        />
        <StyledButton
          type="submit"
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : "Login"}
        </StyledButton>
      </form>
      <LinkContainer>
        <StyledRouterLink to="/auth/forgot-password">
          Forgot Password?
        </StyledRouterLink>
        <StyledRouterLink to="/auth/register">
          Have an invite? Register
        </StyledRouterLink>
      </LinkContainer>
    </FormContainer>
  );
};

const FormContainer = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const StyledTextField = styled(TextField)`
  margin-bottom: 20px;
  width: 100%;
  .MuiOutlinedInput-root {
    color: white;
    fieldset {
      border-color: rgba(255, 165, 0, 0.5);
    }
    &:hover fieldset {
      border-color: rgba(255, 165, 0, 0.7);
    }
    &.Mui-focused fieldset {
      border-color: #FFA500;
    }
  }
  .MuiInputLabel-root {
    color: rgba(255, 255, 255, 0.7);
    &.Mui-focused {
      color: #FFA500;
    }
  }
`;

const StyledButton = styled.button`
  background-color: #FFA500;
  color: black;
  border: none;
  border-radius: 8px; // Match border-radius with FloatingNav buttons
  padding: 12px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
  margin-top: 20px;

  &:hover {
    background-color: #FF8C00;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const LinkContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  width: 100%;
`;

const StyledRouterLink = styled(RouterLink)`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-family: 'Arial', sans-serif;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;

  &:hover {
    color: #FFA500;
  }
`;

export default Login;