import { useState } from 'react';
import { <PERSON><PERSON>, TextField, Typography } from '@mui/material';
import { account } from '@backend/backend';

const VerifyEmail = () => {
  const [email, setEmail] = useState('');

  const handleVerifyEmail = async () => {
    try {
      await account.createVerification('http://localhost:3000/verify-email');
      // Notify user to check their email
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <Typography variant="h4" component="h1" gutterBottom>
        Verify Email
      </Typography>
      <TextField
        label="Email"
        variant="outlined"
        fullWidth
        margin="normal"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />
      <Button variant="contained" color="primary" onClick={handleVerifyEmail} sx={{ mt: 2 }}>
        Send Verification Email
      </Button>
    </>
  );
};

export default VerifyEmail;