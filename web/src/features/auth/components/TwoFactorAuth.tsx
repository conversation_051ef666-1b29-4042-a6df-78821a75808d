import { useState } from 'react';
import { But<PERSON>, TextField, Typography } from '@mui/material';
import { account } from '@backend/backend';

const TwoFactorAuth = () => {
  const [code, setCode] = useState('');

  const handle2FA = async () => {
    try {
      await account.updateVerification("1", code)
      // Redirect to dashboard or home page
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      <Typography variant="h4" component="h1" gutterBottom>
        Two-Factor Authentication
      </Typography>
      <TextField
        label="Authentication Code"
        variant="outlined"
        fullWidth
        margin="normal"
        value={code}
        onChange={(e) => setCode(e.target.value)}
      />
      <Button variant="contained" color="primary" onClick={handle2FA} sx={{ mt: 2 }}>
        Verify
      </Button>
    </>
  );
};

export default TwoFactorAuth;