import React, { useState } from 'react';
import { TextField, Typography, CircularProgress, Alert } from '@mui/material';
import styled from '@emotion/styled';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '@features/auth/hooks/useAuth';
import { account } from '@backend/backend';


const StyledHeader = styled(Typography)`
font-family: 'Poppins', sans-serif;
font-weight: 400;
font-size: 1.5rem;
color: #FFA500;
margin-bottom: 24px;
text-align: center;
text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
line-height: 1.2;
`;

const Register: React.FC = () => {
  const { login } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [inviteCode, setInviteCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleRegister = async () => {
    setLoading(true);
    setError('');
    try {
      const isValidInvite = await verifyInviteCode(inviteCode);
      if (!isValidInvite) {
        throw new Error('Invalid invite code');
      }
      await account.create('unique()', email, password, name);
      await login(email, password);
    } catch (error: unknown) {
      console.error(error);
      setError(error instanceof Error ? error.message : 'An error occurred during registration');
    } finally {
      setLoading(false);
    }
  };

  const verifyInviteCode = async (code: string) => {
    console.log(code);
    return true; // Placeholder, replace with actual verification logic
  };

  return (
    <FormContainer>
      <StyledHeader variant="h1">Register</StyledHeader>
      {error && (
        <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
          {error}
        </Alert>
      )}
      <StyledTextField
        label="Username"
        variant="outlined"
        fullWidth
        value={name}
        onChange={(e) => setName(e.target.value)}
        disabled={loading}
      />
      <StyledTextField
        label="Email"
        variant="outlined"
        fullWidth
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        disabled={loading}
      />
      <StyledTextField
        label="Password"
        type="password"
        variant="outlined"
        fullWidth
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        disabled={loading}
      />
      <StyledTextField
        label="Invite Code"
        variant="outlined"
        fullWidth
        value={inviteCode}
        onChange={(e) => setInviteCode(e.target.value)}
        disabled={loading}
      />
      <StyledButton
        onClick={handleRegister}
        disabled={loading}
      >
        {loading ? <CircularProgress size={24} /> : "REGISTER"}
      </StyledButton>
      <LinkContainer>
        <StyledRouterLink to="/auth/login">
          Already have an account? Login
        </StyledRouterLink>
      </LinkContainer>
    </FormContainer>
  );
};

const FormContainer = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const StyledTextField = styled(TextField)`
  margin-bottom: 20px;
  width: 100%;
  .MuiOutlinedInput-root {
    color: white;
    fieldset {
      border-color: rgba(255, 165, 0, 0.5);
    }
    &:hover fieldset {
      border-color: rgba(255, 165, 0, 0.7);
    }
    &.Mui-focused fieldset {
      border-color: #FFA500;
    }
  }
  .MuiInputLabel-root {
    color: rgba(255, 255, 255, 0.7);
    &.Mui-focused {
      color: #FFA500;
    }
  }
`;

const StyledButton = styled.button`
  background-color: #FFA500;
  color: black;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
  margin-top: 20px;

  &:hover {
    background-color: #FF8C00;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const LinkContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  width: 100%;
`;

const StyledRouterLink = styled(RouterLink)`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-family: 'Arial', sans-serif;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;

  &:hover {
    color: #FFA500;
  }
`;

export default Register;