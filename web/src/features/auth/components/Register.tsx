import React, { useState } from 'react';
import { <PERSON><PERSON>ield, Typography, CircularProgress, Alert, Box, Button } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '@features/auth/hooks/useAuth';
import { auth } from '@backend/backend';
import { logger } from '@utils/logger';
import { authStyles } from '@styles/components/auth';

const Register: React.FC = () => {
  const { login } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleRegister = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      await auth.register(email, password, name);
      await login(email, password);
    } catch (error) {
      logger.error(error as Error);
      if (error instanceof Error) {
        setError(error.message);
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={authStyles.container}>
      <Typography variant="h1" sx={authStyles.header}>
        Register
      </Typography>

      {error && (
        <Alert severity="error" sx={authStyles.alert}>
          {error}
        </Alert>
      )}

      <form onSubmit={handleRegister} style={{ width: '100%' }}>
        <TextField
          label="Name"
          variant="outlined"
          fullWidth
          value={name}
          onChange={(e) => setName(e.target.value)}
          disabled={loading}
          autoComplete='off'
          sx={authStyles.textField}
        />

        <TextField
          label="Email"
          variant="outlined"
          fullWidth
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          disabled={loading}
          autoComplete='off'
          sx={authStyles.textField}
        />

        <TextField
          label="Password"
          type="password"
          variant="outlined"
          fullWidth
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          disabled={loading}
          autoComplete='off'
          sx={authStyles.textField}
        />

        <Button 
          type="submit" 
          disabled={loading}
          fullWidth
          sx={authStyles.button}
        >
          {loading ? <CircularProgress size={24} /> : "REGISTER"}
        </Button>
      </form>

      <Box sx={authStyles.links}>
        <RouterLink to="/auth/login">
          Already have an account? Login
        </RouterLink>
      </Box>
    </Box>
  );
};

export default Register;