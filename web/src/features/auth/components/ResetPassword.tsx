import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>ield, Typography, Box } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import { account } from '@backend/backend';
import styled from '@emotion/styled';

const ResetPassword = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');

  const handleResetPassword = async () => {
    setError('');
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    try {
      const searchParams = new URLSearchParams(location.search);
      const userId = searchParams.get('userId');
      const secret = searchParams.get('secret');
      if (userId && secret) {
        await account.updateRecovery(userId, secret, password);
        navigate('/auth/login', { state: { message: 'Password reset successfully. Please login with your new password.' } });
      } else {
        setError('Invalid reset link. Please request a new password reset.');
      }
    } catch (error) {
      console.error(error);
      setError('An error occurred. Please try again.');
    }
  };

  return (
    <ResetPasswordContainer>
      <GlassCard>
        <Typography variant="h4" component="h1" gutterBottom>
          Reset Password
        </Typography>
        {error && (
          <Typography color="error" sx={{ mb: 2 }}>
            {error}
          </Typography>
        )}
        <TextField
          label="New Password"
          type="password"
          variant="outlined"
          fullWidth
          margin="normal"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
        <TextField
          label="Confirm Password"
          type="password"
          variant="outlined"
          fullWidth
          margin="normal"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
        />
        <StyledButton variant="contained" color="primary" onClick={handleResetPassword} sx={{ mt: 2 }}>
          Reset Password
        </StyledButton>
      </GlassCard>
    </ResetPasswordContainer>
  );
};

const ResetPasswordContainer = styled(Box)`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 16px;
`;

const GlassCard = styled(Box)`
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  width: 100%;
  max-width: 400px;
`;

const StyledButton = styled(Button)`
  margin-top: 16px;
  width: 100%;
  background-color: ${props => props.variant === 'contained' ? '#FFA500' : 'transparent'};
  color: ${props => props.variant === 'contained' ? 'black' : 'white'};
  border: ${props => props.variant === 'outlined' ? '1px solid rgba(255, 255, 255, 0.2)' : 'none'};

  &:hover {
    background-color: ${props => props.variant === 'contained' ? '#FF8C00' : 'rgba(255, 255, 255, 0.1)'};
  }
`;

export default ResetPassword;