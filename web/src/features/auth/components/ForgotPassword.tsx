import { useState } from 'react';
import { TextField, Typography, CircularProgress } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { account } from '@backend/backend';
import styled from '@emotion/styled';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  const StyledHeader = styled(Typography)`
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    font-size: 1.5rem;
    color: #FFA500;
    margin-bottom: 24px;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
  `;

  const handleForgotPassword = async () => {
    setLoading(true);
    try {
      await account.createRecovery(email, 'http://localhost:3000/reset-password');
      // Notify user to check their email
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <FormContainer>
      <StyledHeader variant="h1">
        Forgot<br />Password
      </StyledHeader>
      <StyledTextField
        label="Email"
        variant="outlined"
        fullWidth
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        disabled={loading}
      />
      <StyledButton
        onClick={handleForgotPassword}
        disabled={loading}
      >
        {loading ? <CircularProgress size={24} /> : "Send Recovery Email"}
      </StyledButton>
      <LinkContainer>
        <StyledRouterLink to="/auth/login">
          Back to Login
        </StyledRouterLink>
      </LinkContainer>
    </FormContainer>
  );
};

const FormContainer = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const StyledTextField = styled(TextField)`
  margin-bottom: 20px;
  width: 100%;
  .MuiOutlinedInput-root {
    color: white;
    fieldset {
      border-color: rgba(255, 165, 0, 0.5);
    }
    &:hover fieldset {
      border-color: rgba(255, 165, 0, 0.7);
    }
    &.Mui-focused fieldset {
      border-color: #FFA500;
    }
  }
  .MuiInputLabel-root {
    color: rgba(255, 255, 255, 0.7);
    &.Mui-focused {
      color: #FFA500;
    }
  }
`;

const StyledButton = styled.button`
  background-color: #FFA500;
  color: black;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 100%;
  margin-top: 20px;

  &:hover {
    background-color: #FF8C00;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const LinkContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
  width: 100%;
`;

const StyledRouterLink = styled(RouterLink)`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-family: 'Arial', sans-serif;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;

  &:hover {
    color: #FFA500;
  }
`;

export default ForgotPassword;