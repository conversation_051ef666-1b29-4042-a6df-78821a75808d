import React from 'react';
import { Box, Container, Typo<PERSON>, Grid, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import { FiPlus } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { FaBolt, FaShieldAlt, FaStore, FaHeadset, FaCode, FaTachometerAlt } from 'react-icons/fa';
import { colors } from '@styles/constants';

const features = [
  { title: 'Efficiency', description: 'Maximize your gameplay with our advanced automation tools.', icon: FaBolt, color: '#FFD700' },
  { title: 'Security', description: 'Our client prioritizes your account safety above all else.', icon: FaShieldAlt, color: '#4CAF50' },
  { title: 'AppStore', description: 'Access a wide range of plugins and tools in our integrated store.', icon: FaStore, color: '#2196F3' },
  { title: 'Support', description: '24/7 customer support to assist you with any issues.', icon: FaHeadset, color: '#FF5722' },
  { title: 'Scripts', description: 'Enhance your gameplay with our extensive collection of scripts.', icon: FaCode, color: '#9C27B0' },
  { title: 'Performance', description: 'Enjoy smooth gameplay with our optimized client.', icon: FaTachometerAlt, color: '#F44336' },
];

const faqItems = [
  {
    question: "Is rsglider safe to use?",
    answer: `Yes, rsglider prioritizes your account security and operates with cutting-edge techniques to stay ahead of detection methods. Our approach includes:

• Years of experience in reverse engineering and binary analysis
• Careful monitoring of game updates and anti-cheat developments
• Static analysis of every new game release
• Automated updating tools to ensure minimal downtime
• Immediate shutdown of client access if anti-cheat code changes are detected
• Real-time update system for all active clients
• Thorough testing of every update to avoid triggering anti-cheat software`
  },
  {
    question: "How often is rsglider updated?",
    answer: "Our team releases updates twice a month for regular releases, with ad-hoc updates for hotfixes and critical bugs. We follow our development roadmap closely to deliver new features regularly while minimizing bugs. Our public roadmap will be released soon."
  },
  {
    question: "Can I use rsglider on multiple accounts?",
    answer: "Currently, rsglider is in RC-Alpha and supports one account. However, our core application is built to support multiple accounts and character management. This feature will be enabled before the MVP release, after thorough testing of single instance support."
  },
  {
    question: "What kind of support do you offer?",
    answer: "We provide support through chat and in the rsglider dashboard for registered users. More information about our support services will be available soon."
  }
];


const WhyChooseSection: React.FC = () => {
  return (
    <Box id="why-choose-section" sx={{
      position: 'relative',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      color: 'white',
      padding: { xs: '4rem 0', md: '6rem 0' },
    }}>
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Typography variant="h2" gutterBottom align="center" sx={{ mb: 8, color: colors.primary.main, fontSize: '2.6rem' }}>
            What makes our client stand out?
          </Typography>
        </motion.div>
        
        <Grid container spacing={4}>
          {features.map((item, index) => (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                  p: 4,
                  height: '100%',
                  borderRadius: 2,
                  backgroundColor: colors.glass.background,
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${colors.glass.border}`,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.6)',
                    transform: 'translateY(-5px)',
                  }
                }}>
                  <Typography variant="h1" sx={{ mb: 2, color: item.color }}>{<item.icon />}</Typography>
                  <Typography variant="h5" component="h3" gutterBottom>{item.title}</Typography>
                  <Typography variant="body1" sx={{ color: colors.text.secondary }}>{item.description}</Typography>
                </Box>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 8 }}>
          <Typography variant="h4" component="h3" gutterBottom align="center">
            Frequently Asked Questions
          </Typography>
          {faqItems.map((item, index) => (
            <Accordion key={index} sx={{ 
              backgroundColor: colors.glass.background,
              backdropFilter: 'blur(10px)',
              border: `1px solid ${colors.glass.border}`,
              color: 'white',
              my: 2
            }}>
              <AccordionSummary expandIcon={<FiPlus color="white" />}>
                <Typography variant="h6">{item.question}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography sx={{ whiteSpace: 'pre-wrap' }}>{item.answer}</Typography>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>

        <Box sx={{ mt: 8, textAlign: 'center' }}>
        </Box>
      </Container>
    </Box>
  );
};

export default WhyChooseSection;