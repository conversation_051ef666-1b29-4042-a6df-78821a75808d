import React from 'react';
import { Box, Container, Grid, <PERSON>po<PERSON>, IconButton, Divider } from '@mui/material';
import { motion } from 'framer-motion';
import { FiTwitter, FiMail, FiShield, FiFileText, FiUsers, FiCode, FiEye, FiLock } from 'react-icons/fi';
import { useAuth } from '@features/auth/hooks/useAuth';
import { colors } from '@styles/constants';

const Footer: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    community: [
      { label: 'Discord Server', href: '/dashboard/community/discord', icon: <FiUsers />, auth: true },
      { label: 'Developer Docs', href: 'https://docs.rsglider.com', icon: <FiCode />, external: true },
      { label: 'Script Store', href: '/store', icon: <FiFileText /> },
    ],
    legal: [
      { label: 'Privacy Policy', href: '/privacy', icon: <FiEye /> },
      { label: 'Terms of Service', href: '/terms', icon: <FiFileText /> },
      { label: 'Security', href: '/dashboard/security', icon: <FiLock />, auth: true },
    ],
    contact: [
      { label: 'Support Email', href: 'mailto:<EMAIL>', icon: <FiMail /> },
      { label: 'Security Issues', href: 'mailto:<EMAIL>', icon: <FiShield /> },
    ]
  };

  const socialLinks = [
    { 
      label: 'Twitter/X', 
      href: 'https://x.com/rsglider', 
      icon: <FiTwitter />,
      color: '#1DA1F2'
    },
  ];

  const handleLinkClick = (href: string, external?: boolean) => {
    if (external) {
      window.open(href, '_blank', 'noopener,noreferrer');
    } else {
      window.location.href = href;
    }
  };

  return (
    <Box sx={{
      background: 'linear-gradient(180deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.95) 100%)',
      backdropFilter: 'blur(20px)',
      borderTop: `1px solid ${colors.glass.border}`,
      mt: 'auto',
      py: 6,
    }}>
      <Container maxWidth="lg">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Main Footer Content */}
          <Grid container spacing={4} sx={{ mb: 4 }}>
            {/* Brand Section */}
            <Grid size={{ xs: 12, md: 4 }}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h5" sx={{ 
                  color: colors.text.primary,
                  fontWeight: 800,
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}>
                  <Box component="span" sx={{ color: colors.text.primary }}>rs</Box>
                  <Box component="span" sx={{ 
                    background: 'linear-gradient(45deg, #FFA500, #FF8C00)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                  }}>
                    glider
                  </Box>
                </Typography>
                <Typography variant="body2" sx={{ 
                  color: colors.text.secondary,
                  mb: 3,
                  lineHeight: 1.6,
                  fontStyle: 'italic'
                }}>
                  "Underground automation for the elite. Built by the community, for the community."
                </Typography>
                
                {/* Social Links */}
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {socialLinks.map((social) => (
                    <IconButton
                      key={social.label}
                      onClick={() => handleLinkClick(social.href, true)}
                      sx={{
                        color: social.color,
                        background: 'rgba(255,255,255,0.05)',
                        border: `1px solid ${social.color}40`,
                        '&:hover': {
                          background: `${social.color}20`,
                          transform: 'translateY(-2px)',
                        },
                        transition: 'all 0.3s ease',
                      }}
                      aria-label={social.label}
                    >
                      {social.icon}
                    </IconButton>
                  ))}
                </Box>
              </Box>
            </Grid>

            {/* Community Links */}
            <Grid size={{ xs: 12, sm: 6, md: 2.5 }}>
              <Typography variant="h6" sx={{ 
                color: colors.text.primary,
                fontWeight: 600,
                mb: 2,
                fontSize: '1rem'
              }}>
                Community
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                {footerLinks.community.map((link) => {
                  if (link.auth && !isAuthenticated) return null;
                  return (
                    <Box
                      key={link.label}
                      onClick={() => handleLinkClick(link.href, link.external)}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        color: colors.text.secondary,
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          color: colors.text.primary,
                          transform: 'translateX(5px)',
                        }
                      }}
                    >
                      {link.icon}
                      <Typography variant="body2">{link.label}</Typography>
                    </Box>
                  );
                })}
              </Box>
            </Grid>

            {/* Legal Links */}
            <Grid size={{ xs: 12, sm: 6, md: 2.5 }}>
              <Typography variant="h6" sx={{ 
                color: colors.text.primary,
                fontWeight: 600,
                mb: 2,
                fontSize: '1rem'
              }}>
                Legal & Security
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                {footerLinks.legal.map((link) => {
                  if (link.auth && !isAuthenticated) return null;
                  return (
                    <Box
                      key={link.label}
                      onClick={() => handleLinkClick(link.href)}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        color: colors.text.secondary,
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          color: colors.text.primary,
                          transform: 'translateX(5px)',
                        }
                      }}
                    >
                      {link.icon}
                      <Typography variant="body2">{link.label}</Typography>
                    </Box>
                  );
                })}
              </Box>
            </Grid>

            {/* Contact */}
            <Grid size={{ xs: 12, md: 3 }}>
              <Typography variant="h6" sx={{ 
                color: colors.text.primary,
                fontWeight: 600,
                mb: 2,
                fontSize: '1rem'
              }}>
                Contact
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                {footerLinks.contact.map((link) => (
                  <Box
                    key={link.label}
                    onClick={() => handleLinkClick(link.href)}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      color: colors.text.secondary,
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        color: colors.text.primary,
                        transform: 'translateX(5px)',
                      }
                    }}
                  >
                    {link.icon}
                    <Typography variant="body2">{link.label}</Typography>
                  </Box>
                ))}
              </Box>
            </Grid>
          </Grid>

          <Divider sx={{ 
            borderColor: colors.glass.border,
            mb: 3
          }} />

          {/* Bottom Section */}
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 2
          }}>
            <Typography variant="body2" sx={{ 
              color: colors.text.secondary,
              fontSize: '0.875rem'
            }}>
              © {currentYear} RSGlider. Built for the underground.
            </Typography>
            
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 2,
              color: colors.text.secondary,
              fontSize: '0.75rem'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <FiShield size={12} />
                <Typography variant="caption">Secure</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <FiEye size={12} />
                <Typography variant="caption">Private</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <FiUsers size={12} />
                <Typography variant="caption">Community-Driven</Typography>
              </Box>
            </Box>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default Footer;
