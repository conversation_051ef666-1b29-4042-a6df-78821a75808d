import React from 'react';
import { Box, Container, Typography } from '@mui/material';
import Button from '@features/_global/Button';
import { FiArrowRight } from 'react-icons/fi';

const HowItWorksSection: React.FC = () => {
  const steps = [
    {
      title: 'Install RSGlider',
      description: 'Download and install RSGlider on your computer. Our easy setup process ensures you\'re up and running in minutes.'
    },
    {
      title: 'Configure Your Settings',
      description: 'Customize RSGlider scripts through the in-app settings menu. Configure combat settings, in-game preferences, and much more.'
    },
    {
      title: 'Start Automating',
      description: 'Let RSGlider handle repetitive tasks while you focus on strategy. Automate resource gathering, combat rotations, and more.'
    },
    {
      title: 'Monitor Progress',
      description: 'Track your efficiency gains with our built-in analytics. See how RSGlider boosts your XP rates and resource collection.'
    }
  ];

  return (
    <Box sx={{
      position: 'relative',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      color: 'white',
      py: 8,
    }}>
      <Container maxWidth="md" sx={{ position: 'relative', zIndex: 2 }}>
        <Typography 
          variant="h3" 
          component="h2" 
          gutterBottom 
          align="center" 
          sx={{ mb: 6, color: 'orange' }}
          id="features"
        >
          How It Works
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 6, my: 8 }}>
          {steps.map((step, index) => (
            <Box key={index} sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 3,
              backgroundColor: 'rgba(0, 0, 0, 0.6)', // Dark background for each step
              padding: 3,
              borderRadius: 2,
            }}>
              <Typography variant="h1" sx={{ color: 'orange', opacity: 0.7 }}>{index + 1}</Typography>
              <Box>
                <Typography variant="h5" gutterBottom>{step.title}</Typography>
                <Typography variant="body2">{step.description}</Typography>
              </Box>
            </Box>
          ))}
        </Box>

        <Box sx={{ textAlign: 'center', mt: 10 }}>
          <Typography sx={{ color: 'white' }} variant="h3" component="h2" gutterBottom>
            Interested in trying it out?
          </Typography>
          <Button
            href="/invite"
            variantType="primary"
            component="a"
            sx={{
              backgroundColor: 'orange',
              '&:hover': {
                backgroundColor: 'darkorange',
              },
              color: 'black',
              mt: 3,
            }}
          >
            Request an Invite <FiArrowRight style={{ marginLeft: '8px' }} />
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default HowItWorksSection;