import React from 'react';
import { Box, Container, Typography } from '@mui/material';
import MotionButton from '@features/_global/MotionButton';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import DashboardIcon from '@mui/icons-material/Dashboard';
import { landingStyles } from '@styles/components/landing';
import { useAuth } from '@features/auth/hooks/useAuth';

const HowItWorksSection: React.FC = () => {
  const { isAuthenticated } = useAuth();

  // Different content for authenticated vs non-authenticated users
  const guestSteps = [
    {
      title: 'Create Your Account',
      description: 'Sign up for RSGlider and join thousands of players already automating their OSRS experience.'
    },
    {
      title: 'Browse Our Scripts',
      description: 'Explore our marketplace of premium scripts created by experienced developers and the community.'
    },
    {
      title: 'Download & Install',
      description: 'Get the RSGlider client and install your chosen scripts with our easy setup process.'
    },
    {
      title: 'Start Automating',
      description: 'Let RSGlider handle repetitive tasks while you focus on strategy and enjoy the game.'
    }
  ];

  const authenticatedSteps = [
    {
      title: 'Browse Premium Scripts',
      description: 'Explore our marketplace of high-quality automation scripts created by experienced developers.'
    },
    {
      title: 'Purchase & Download',
      description: 'Buy the scripts that match your playstyle and download them instantly to your account.'
    },
    {
      title: 'Install RSGlider Client',
      description: 'Download our secure client application and sync your purchased scripts automatically.'
    },
    {
      title: 'Configure & Automate',
      description: 'Set up your preferences and let RSGlider boost your efficiency while you enjoy the game.'
    }
  ];

  const steps = isAuthenticated ? authenticatedSteps : guestSteps;

  return (
    <Box sx={landingStyles.howItWorks.section}>
      <Container maxWidth="md" sx={{ position: 'relative', zIndex: 2 }}>
        <Typography
          variant="h3"
          component="h2"
          gutterBottom
          sx={landingStyles.howItWorks.title}
          id="features"
        >
          {isAuthenticated ? 'Get Started Today' : 'How It Works'}
        </Typography>

        <Box sx={landingStyles.howItWorks.stepsContainer}>
          {steps.map((step, index) => (
            <Box key={index} sx={landingStyles.howItWorks.step}>
              <Typography variant="h1" sx={landingStyles.howItWorks.stepNumber}>
                {index + 1}
              </Typography>
              <Box>
                <Typography variant="h5" sx={landingStyles.howItWorks.stepTitle}>
                  {step.title}
                </Typography>
                <Typography variant="body2" sx={landingStyles.howItWorks.stepDescription}>
                  {step.description}
                </Typography>
              </Box>
            </Box>
          ))}
        </Box>

        <Box sx={landingStyles.howItWorks.cta}>
          {isAuthenticated ? (
            <>
              <Typography sx={landingStyles.howItWorks.ctaText} variant="h3" component="h2">
                Ready to boost your OSRS experience?
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <MotionButton
                  variant="contained"
                  href="/store"
                  sx={landingStyles.howItWorks.ctaButton}
                  aria-label="Browse Scripts"
                >
                  Browse Scripts <ShoppingCartIcon />
                </MotionButton>
                <MotionButton
                  variant="outlined"
                  href="/dashboard"
                  sx={{
                    ...landingStyles.howItWorks.ctaButton,
                    backgroundColor: 'transparent',
                    border: '2px solid #00ff88',
                    color: '#00ff88',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 255, 136, 0.1)',
                      border: '2px solid #00cc6a',
                    }
                  }}
                  aria-label="Go to Dashboard"
                >
                  Dashboard <DashboardIcon />
                </MotionButton>
              </Box>
            </>
          ) : (
            <>
              <Typography sx={landingStyles.howItWorks.ctaText} variant="h3" component="h2">
                Ready to revolutionize your gameplay?
              </Typography>
              <MotionButton
                variant="contained"
                href="/auth/register"
                sx={landingStyles.howItWorks.ctaButton}
                aria-label="Create Account"
              >
                Create Your Account <ArrowForwardIcon />
              </MotionButton>
            </>
          )}
        </Box>
      </Container>
    </Box>
  );
};

export default HowItWorksSection;