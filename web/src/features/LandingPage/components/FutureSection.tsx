import React from 'react';
import { Box, Container, Typography, Grid, Chip } from '@mui/material';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';
import styled from '@emotion/styled';
import MotionButton from '@features/_global/MotionButton';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

const GradientSpan = styled.span`
  background: linear-gradient(90deg, #FFA500, #FF4500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
`;

const GlassCard = styled(Box)`
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
  &:hover {
    background: rgba(0, 0, 0, 0.6);
    transform: translateY(-5px);
  }
`;

interface FeatureItem {
  title: string;
  description: string;
  tag?: string;
  icon: React.ReactNode;
}

const FeatureCard: React.FC<{ item: FeatureItem; index: number }> = ({ item, index }) => (
  <Grid item xs={12} sm={6} md={4}>
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 * index }}
    >
      <GlassCard sx={{ p: 3, height: '100%' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ mr: 2, color: 'orange' }}>{item.icon}</Box>
          <Typography variant="h6" component="h3" sx={{ color: 'orange', flex: 1 }}>
            {item.title}
          </Typography>
          {item.tag && (
            <Chip
              label={item.tag}
              size="small"
              sx={{ backgroundColor: 'rgba(0, 255, 255, 0.2)', color: 'cyan' }}
            />
          )}
        </Box>
        <Typography variant="body2" sx={{ color: 'white' }}>{item.description}</Typography>
      </GlassCard>
    </motion.div>
  </Grid>
);

const FutureSection: React.FC = () => {
  const features = [
    { title: 'Seamless Integration', description: 'Works flawlessly with the OSRS C++ Client', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Continuous Updates', description: 'Regular updates to stay ahead of the game', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Windows Support', description: 'Optimized for Windows operating systems', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Advanced Pathfinding', description: 'Intelligent navigation through complex game environments', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Lightweight UI', description: 'Beautiful and responsive user interface', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Theme Options', description: 'Dark and Light Mode for personalized experience', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Account Security', description: 'Advanced features to keep your account safe', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Mobile Control', description: 'Remotely control the Desktop Client from your phone', tag: 'Coming Soon!', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'AI-Powered Assistance', description: 'Advanced AI-integrations for your gameplay', tag: 'Coming Soon!', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
  ];

  const devFeatures = [
    { title: 'Scriptable', description: 'Develop custom scripts using JavaScript/TypeScript', icon: <FiArrowRight /> },
    { title: 'Quick Start', description: 'Bootstrap your script development with initial setup and libraries', icon: <FiArrowRight /> },
    { title: 'Advanced Debugging', description: 'Powerful tools for efficient development and troubleshooting', icon: <FiArrowRight /> },
  ];

  return (
    <Box id="future-section" sx={{
      position: 'relative',
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
    }}>
      <Container maxWidth="lg" sx={{ 
        position: 'relative', 
        zIndex: 2, 
        py: 6,
      }}>
        <Typography variant="h2" component="h2" gutterBottom sx={{ color: 'white', fontSize: { xs: '2.5rem', md: '3.5rem' }, textAlign: 'center', mb: 4 }}>
          The Future of Automation
        </Typography>
        <Typography variant="h5" sx={{ mb: 6, color: 'white', textAlign: 'center' }}>
          <strong><span style={{ color: 'white' }}>rs</span><GradientSpan>glider</GradientSpan></strong> is at the forefront of gaming innovation, combining cutting-edge technology with classic gameplay to create an unparalleled experience.
        </Typography>

        <Grid container spacing={3} sx={{ mb: 6 }}>
          {features.map((item, index) => (
            <FeatureCard key={index} item={item} index={index} />
          ))}
        </Grid>

        <Typography variant="h3" component="h3" gutterBottom sx={{ color: 'white', textAlign: 'center', mb: 4 }}>
          For Developers
        </Typography>
        <Grid container spacing={3} sx={{ mb: 6 }}>
          {devFeatures.map((item, index) => (
            <FeatureCard key={index} item={item} index={index} />
          ))}
        </Grid>

        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h4" component="h3" gutterBottom sx={{ color: 'white' }}>
            Ready to Experience the Future?
          </Typography>
          <MotionButton
            variant="contained"
            href="https://example.com"
            sx={{
              backgroundColor: 'orange',
              color: 'black',
              '&:hover': {
                backgroundColor: 'darkorange',
              },
            }}
            aria-label="Join the Waitlist"
          >
            Join the Waitlist <ArrowForwardIcon />
          </MotionButton>
        </Box>
      </Container>
    </Box>
  );
};

export default FutureSection;