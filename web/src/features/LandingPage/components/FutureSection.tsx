import React from 'react';
import { Box, Container, Typography, Grid, Chip } from '@mui/material';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';
import MotionButton from '@features/_global/MotionButton';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CodeIcon from '@mui/icons-material/Code';
import GroupIcon from '@mui/icons-material/Group';
import SecurityIcon from '@mui/icons-material/Security';
import { landingStyles } from '@styles/components/landing';
import { colors } from '@styles/constants';
import { useAuth } from '@features/auth/hooks/useAuth';

interface FeatureItem {
  title: string;
  description: string;
  tag?: string;
  icon: React.ReactNode;
}

const FeatureCard: React.FC<{ item: FeatureItem; index: number }> = ({ item, index }) => (
  <Grid size={{ xs: 12, sm: 6, md: 4 }}>
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.1 * index }}
    >
      <Box sx={landingStyles.future.card}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ mr: 2, color: colors.primary.main }}>{item.icon}</Box>
          <Typography variant="h6" component="h3" sx={{ color: 'orange', flex: 1 }}>
            {item.title}
          </Typography>
          {item.tag && (
            <Chip
              label={item.tag}
              size="small"
              sx={{ 
                backgroundColor: colors.secondary.light,
                color: colors.text.primary 
              }}
            />
          )}
        </Box>
        <Typography variant="body2" sx={{ color: colors.text.secondary }}>{item.description}</Typography>
      </Box>
    </motion.div>
  </Grid>
);

const FutureSection: React.FC = () => {
  const { isAuthenticated } = useAuth();

  const features = [
    { title: 'Seamless Integration', description: 'Works flawlessly with the OSRS C++ Client', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Continuous Updates', description: 'Regular updates to stay ahead of the game', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Windows Support', description: 'Optimized for Windows operating systems', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Advanced Pathfinding', description: 'Intelligent navigation through complex game environments', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Lightweight UI', description: 'Beautiful and responsive user interface', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Theme Options', description: 'Dark and Light Mode for personalized experience', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Account Security', description: 'Advanced features to keep your account safe', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'Mobile Control', description: 'Remotely control the Desktop Client from your phone', tag: 'Coming Soon!', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
    { title: 'AI-Powered Assistance', description: 'Advanced AI-integrations for your gameplay', tag: 'Coming Soon!', icon: <FiArrowRight style={{ verticalAlign: 'middle' }} /> },
  ];

  const devFeatures = [
    { title: 'Scriptable', description: 'Develop custom scripts using JavaScript/TypeScript built around our SDK', icon: <FiArrowRight /> },
    { title: 'Quick Start', description: 'Bootstrap your script development with initial setup and libraries', icon: <FiArrowRight /> },
    { title: 'Advanced Debugging', description: 'Powerful tools for efficient development and troubleshooting', icon: <FiArrowRight /> },
  ];

  return (
    <Box sx={landingStyles.future.section}>
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2, py: 6 }}>
        <Typography variant="h2" component="h2" gutterBottom sx={{ 
          color: 'white', 
          fontSize: { xs: '2.5rem', md: '3.5rem' }, 
          textAlign: 'center', 
          mb: 4 
        }}>
          The Future of Automation
        </Typography>
        
        <Grid container spacing={3} sx={{ mb: 6 }}>
          {features.map((item, index) => (
            <FeatureCard key={index} item={item} index={index} />
          ))}
        </Grid>

        <Typography variant="h3" component="h3" gutterBottom sx={{ color: 'white', textAlign: 'center', mb: 4 }}>
          For Developers
        </Typography>
        
        <Grid container spacing={3} sx={{ mb: 6 }}>
          {devFeatures.map((item, index) => (
            <FeatureCard key={index} item={item} index={index} />
          ))}
        </Grid>

        <Box sx={{ textAlign: 'center' }}>
          {isAuthenticated ? (
            <>
              <Typography variant="h4" component="h3" gutterBottom sx={{ color: colors.text.primary }}>
                Join Our Growing Community
              </Typography>
              <Typography variant="body1" sx={{ color: colors.text.secondary, mb: 4, maxWidth: '600px', mx: 'auto' }}>
                Connect with fellow developers, share scripts, get support, and stay updated on the latest features and security updates.
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <MotionButton
                  variant="contained"
                  href="/dashboard/community/discord"
                  sx={{
                    ...landingStyles.howItWorks.ctaButton,
                    background: 'linear-gradient(45deg, #5865F2, #4752C4)',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #4752C4, #3C45A5)',
                    }
                  }}
                  aria-label="Join Discord Community"
                >
                  Join Discord <GroupIcon />
                </MotionButton>
                <MotionButton
                  variant="outlined"
                  href="/dashboard/security"
                  sx={{
                    ...landingStyles.howItWorks.ctaButton,
                    backgroundColor: 'transparent',
                    border: '2px solid #ff6b6b',
                    color: '#ff6b6b',
                    '&:hover': {
                      backgroundColor: 'rgba(255, 107, 107, 0.1)',
                      border: '2px solid #ff5252',
                    }
                  }}
                  aria-label="Security Settings"
                >
                  Security <SecurityIcon />
                </MotionButton>
                <MotionButton
                  variant="outlined"
                  onClick={() => window.open('https://docs.rsglider.com', '_blank', 'noopener,noreferrer')}
                  sx={{
                    ...landingStyles.howItWorks.ctaButton,
                    backgroundColor: 'transparent',
                    border: '2px solid #4ecdc4',
                    color: '#4ecdc4',
                    '&:hover': {
                      backgroundColor: 'rgba(78, 205, 196, 0.1)',
                      border: '2px solid #26a69a',
                    }
                  }}
                  aria-label="Developer Documentation"
                >
                  Dev Docs <CodeIcon />
                </MotionButton>
              </Box>
            </>
          ) : (
            <>
              <Typography variant="h4" component="h3" gutterBottom sx={{ color: colors.text.primary }}>
                Ready to Experience the Future?
              </Typography>
              <Typography variant="body1" sx={{ color: colors.text.secondary, mb: 4, maxWidth: '600px', mx: 'auto' }}>
                Join thousands of players who are already using RSGlider to revolutionize their OSRS experience with cutting-edge automation technology.
              </Typography>
              <MotionButton
                variant="contained"
                href="/auth/register"
                sx={landingStyles.howItWorks.ctaButton}
                aria-label="Create Account"
              >
                Join the Revolution <ArrowForwardIcon />
              </MotionButton>
            </>
          )}
        </Box>
      </Container>
    </Box>
  );
};

export default FutureSection;