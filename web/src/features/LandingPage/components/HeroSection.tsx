import React, { useEffect, useState } from 'react';
import { Box, Container, Grid, Typography, IconButton as Mui<PERSON>con<PERSON>utton } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import desktopClientImage from '@assets/desktop_client.png';
import { landingStyles } from '@styles/components/landing';

const HeroSection: React.FC = () => {
  const [currentFeature, setCurrentFeature] = useState(0);
  const features = [
    {
      title: "Multi-Session Process Manager",
      description: "Manage multiple OSRS sessions simultaneously, streamline your gameplay, and maximize efficiency."
    },
    {
      title: "Advanced Bot Detection Avoidance",
      description: "Stay undetected with our sophisticated anti-detection system, ensuring a safe and smooth gaming experience."
    },
    {
      title: "Custom Scripting Engine",
      description: "Create and run your own custom scripts to automate repetitive tasks and enhance your gameplay."
    },
    {
      title: "Real-Time Analytics",
      description: "Monitor your gameplay with real-time analytics and make data-driven decisions to improve your performance."
    },
    {
      title: "User-Friendly Interface",
      description: "Enjoy a seamless and intuitive user interface designed to enhance your overall gaming experience."
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [features.length]);

  const nextFeature = () => {
    setCurrentFeature((prev) => (prev + 1) % features.length);
  };

  const prevFeature = () => {
    setCurrentFeature((prev) => (prev - 1 + features.length) % features.length);
  };

  return (
    <Box sx={landingStyles.hero.container}>
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2, pt: 10 }}>
        <Box sx={landingStyles.hero.glass}>
          <Grid container spacing={2} alignItems="flex-start" justifyContent="center">
            <Grid size={{ xs: 12, md: 6 }}>
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Typography variant="h1" sx={landingStyles.hero.title}>
                  Revolutionize Your OSRS Gameplay
                </Typography>
                <Typography variant="h6" sx={landingStyles.hero.subtitle}>
                  Enhance your Oldschool Runescape experience with our advanced tools.
                </Typography>
                <Typography variant="body1" sx={landingStyles.hero.description}>
                  Our tools are designed to help you manage multiple sessions, avoid detection, and create custom scripts to automate repetitive tasks.
                </Typography>
                <Typography variant="body1" sx={landingStyles.hero.description}>
                  With real-time analytics and a user-friendly interface, you can monitor your gameplay and make data-driven decisions to enhance your performance.
                </Typography>
              </motion.div>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Box
                  component="img"
                  src={desktopClientImage}
                  alt="Desktop Client"
                  sx={landingStyles.hero.image}
                />
              </motion.div>
            </Grid>
          </Grid>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            style={{ width: '100%', margin: '0 auto' }}
          >
            <Box sx={landingStyles.hero.carousel.container}>
              <MuiIconButton 
                onClick={prevFeature} 
                aria-label="Previous feature"
                sx={landingStyles.hero.carousel.button}
              >
                <FiChevronLeft />
              </MuiIconButton>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentFeature}
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ duration: 0.5 }}
                  style={{ width: '100%', textAlign: 'center', padding: '0 40px' }}
                >
                  <Typography variant="h5" sx={landingStyles.hero.carousel.title}>
                    {features[currentFeature].title}
                  </Typography>
                  <Typography variant="body1" sx={landingStyles.hero.carousel.description}>
                    {features[currentFeature].description}
                  </Typography>
                </motion.div>
              </AnimatePresence>
              <MuiIconButton 
                onClick={nextFeature} 
                aria-label="Next feature"
                sx={landingStyles.hero.carousel.button}
              >
                <FiChevronRight />
              </MuiIconButton>
            </Box>
          </motion.div>
        </Box>
      </Container>
    </Box>
  );
};

export default HeroSection;