import React, { useEffect, useState } from 'react';
import { Box, Container, Grid, Typography, IconButton as Mu<PERSON><PERSON><PERSON><PERSON><PERSON>on } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { FiChevronLeft, FiChevronRight, FiPlay, FiPause } from 'react-icons/fi';
import desktopClientImage from '@assets/desktop_client.png';
import { landingStyles } from '@styles/components/landing';

const HeroSection: React.FC = () => {
  const [currentFeature, setCurrentFeature] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [progress, setProgress] = useState(0);

  const features = [
    {
      title: "Multi-Session Process Manager",
      description: "Manage multiple OSRS sessions simultaneously with intelligent resource allocation and seamless switching between accounts.",
      image: desktopClientImage, // We'll use the same image for now, but this could be different per feature
      highlight: "Unlimited concurrent sessions",
      color: "#00ff88"
    },
    {
      title: "Advanced Bot Detection Avoidance",
      description: "Stay undetected with our sophisticated anti-detection system featuring human-like behavior patterns and randomization.",
      image: desktopClientImage,
      highlight: "Undetectable",
      color: "#ff6b6b"
    },
    {
      title: "Custom Scripting Engine",
      description: "Create and run your own custom scripts with our powerful scripting engine supporting advanced automation workflows.",
      image: desktopClientImage,
      highlight: "JavaScript & TypeScript support",
      color: "#4ecdc4"
    },
    {
      title: "Real-Time Analytics Dashboard",
      description: "Monitor your gameplay with comprehensive analytics, performance metrics, and detailed progress tracking in real-time.",
      image: desktopClientImage,
      highlight: "Live performance metrics",
      color: "#45b7d1"
    },
    {
      title: "Intelligent User Interface",
      description: "Experience our modern, intuitive interface designed for efficiency with dark and light mode support.",
      image: desktopClientImage,
      highlight: "Dark & Light Mode",
      color: "#f9ca24"
    }
  ];

  // Auto-play functionality with progress tracking
  useEffect(() => {
    if (!isAutoPlaying) return;

    const progressInterval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          setCurrentFeature((current) => (current + 1) % features.length);
          return 0;
        }
        return prev + 2; // Increment by 2% every 100ms for 5-second duration
      });
    }, 100);

    return () => clearInterval(progressInterval);
  }, [isAutoPlaying, features.length]);

  // Reset progress when manually changing slides
  useEffect(() => {
    setProgress(0);
  }, [currentFeature]);

  const nextFeature = () => {
    setCurrentFeature((prev) => (prev + 1) % features.length);
    setProgress(0);
  };

  const prevFeature = () => {
    setCurrentFeature((prev) => (prev - 1 + features.length) % features.length);
    setProgress(0);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying);
  };

  return (
    <Box sx={landingStyles.hero.container}>
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2, pt: 10 }}>
        <Box sx={landingStyles.hero.glass}>
          {/* Main Content Grid */}
          <Grid container spacing={4} alignItems="center" justifyContent="center">
            {/* Left Side - Text Content */}
            <Grid size={{ xs: 12, lg: 5 }}>
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Typography variant="h1" sx={landingStyles.hero.title}>
                  Revolutionize Your
                  <Box component="span" sx={{
                    background: `linear-gradient(45deg, ${features[currentFeature].color}, #fff)`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    display: 'block'
                  }}>
                    OSRS Gameplay
                  </Box>
                </Typography>
                <Typography variant="h6" sx={landingStyles.hero.subtitle}>
                  Experience the next generation of Oldschool Runescape automation
                </Typography>
              </motion.div>
            </Grid>

            {/* Right Side - Enhanced Image Display */}
            <Grid size={{ xs: 12, lg: 7 }}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <Box sx={{ position: 'relative', perspective: '1000px' }}>
                  {/* Main Screenshot Container */}
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={currentFeature}
                      initial={{ opacity: 0, rotateY: 15, scale: 0.95 }}
                      animate={{ opacity: 1, rotateY: 0, scale: 1 }}
                      exit={{ opacity: 0, rotateY: -15, scale: 0.95 }}
                      transition={{ duration: 0.5 }}
                      style={{ position: 'relative' }}
                    >
                      <Box
                        component="img"
                        src={features[currentFeature].image}
                        alt={features[currentFeature].title}
                        sx={{
                          ...landingStyles.hero.image,
                          border: `3px solid ${features[currentFeature].color}`,
                          boxShadow: `0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px ${features[currentFeature].color}40`,
                        }}
                      />

                      {/* Feature Highlight Badge */}
                      <Box sx={{
                        position: 'absolute',
                        top: -10,
                        right: -10,
                        background: `linear-gradient(45deg, ${features[currentFeature].color}, ${features[currentFeature].color}cc)`,
                        color: '#000',
                        padding: '8px 16px',
                        borderRadius: '20px',
                        fontSize: '0.875rem',
                        fontWeight: 'bold',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
                        backdropFilter: 'blur(10px)',
                      }}>
                        {features[currentFeature].highlight}
                      </Box>
                    </motion.div>
                  </AnimatePresence>
                </Box>
              </motion.div>
            </Grid>
          </Grid>
          {/* Enhanced Carousel Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            style={{ width: '100%', marginTop: '3rem' }}
          >
            {/* Feature Content */}
            <Box sx={{ position: 'relative', minHeight: '200px', mb: 3 }}>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentFeature}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.4 }}
                  style={{ textAlign: 'center', padding: '0 20px' }}
                >
                  <Typography
                    variant="h4"
                    sx={{
                      ...landingStyles.hero.carousel.title,
                      background: `linear-gradient(45deg, ${features[currentFeature].color}, #fff)`,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text',
                      mb: 2
                    }}
                  >
                    {features[currentFeature].title}
                  </Typography>
                  <Typography variant="body1" sx={{
                    ...landingStyles.hero.carousel.description,
                    maxWidth: '600px',
                    margin: '0 auto',
                    lineHeight: 1.6
                  }}>
                    {features[currentFeature].description}
                  </Typography>
                </motion.div>
              </AnimatePresence>
            </Box>

            {/* Enhanced Controls */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 3,
              flexWrap: 'wrap'
            }}>
              {/* Navigation Buttons */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <MuiIconButton
                  onClick={prevFeature}
                  aria-label="Previous feature"
                  sx={{
                    ...landingStyles.hero.carousel.button,
                    position: 'static',
                    background: `linear-gradient(45deg, ${features[currentFeature].color}20, rgba(255,255,255,0.1))`,
                    border: `1px solid ${features[currentFeature].color}40`,
                  }}
                >
                  <FiChevronLeft />
                </MuiIconButton>

                {/* Progress Indicators */}
                <Box sx={{ display: 'flex', gap: 1, mx: 2 }}>
                  {features.map((_, index) => (
                    <Box
                      key={index}
                      onClick={() => {
                        setCurrentFeature(index);
                        setProgress(0);
                      }}
                      sx={{
                        width: index === currentFeature ? '40px' : '12px',
                        height: '4px',
                        borderRadius: '2px',
                        background: index === currentFeature
                          ? `linear-gradient(90deg, ${features[currentFeature].color}, ${features[currentFeature].color}60)`
                          : 'rgba(255,255,255,0.3)',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        position: 'relative',
                        overflow: 'hidden',
                        '&:hover': {
                          background: index === currentFeature
                            ? `linear-gradient(90deg, ${features[currentFeature].color}, ${features[currentFeature].color}80)`
                            : 'rgba(255,255,255,0.5)',
                        }
                      }}
                    >
                      {index === currentFeature && (
                        <Box sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          height: '100%',
                          width: `${progress}%`,
                          background: features[currentFeature].color,
                          transition: 'width 0.1s linear',
                        }} />
                      )}
                    </Box>
                  ))}
                </Box>

                <MuiIconButton
                  onClick={nextFeature}
                  aria-label="Next feature"
                  sx={{
                    ...landingStyles.hero.carousel.button,
                    position: 'static',
                    background: `linear-gradient(45deg, ${features[currentFeature].color}20, rgba(255,255,255,0.1))`,
                    border: `1px solid ${features[currentFeature].color}40`,
                  }}
                >
                  <FiChevronRight />
                </MuiIconButton>
              </Box>

              {/* Play/Pause Button */}
              <MuiIconButton
                onClick={toggleAutoPlay}
                aria-label={isAutoPlaying ? "Pause slideshow" : "Play slideshow"}
                sx={{
                  background: `linear-gradient(45deg, ${features[currentFeature].color}30, rgba(255,255,255,0.1))`,
                  border: `1px solid ${features[currentFeature].color}60`,
                  color: features[currentFeature].color,
                  '&:hover': {
                    background: `linear-gradient(45deg, ${features[currentFeature].color}40, rgba(255,255,255,0.2))`,
                  }
                }}
              >
                {isAutoPlaying ? <FiPause /> : <FiPlay />}
              </MuiIconButton>
            </Box>
          </motion.div>
        </Box>
      </Container>
    </Box>
  );
};

export default HeroSection;