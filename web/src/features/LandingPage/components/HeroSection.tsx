import React, { useEffect, useState } from 'react';
import { Box, Container, Grid, Typography } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import desktopClientImage from '@/assets/desktop_client.png';
import styled from 'styled-components';
import { alpha } from '@mui/material/styles';
import IconButton from '@mui/material/IconButton';

const HeroContainer = styled(Box)`
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  padding: 2rem 0;
  overflow: hidden;
`;

const ContentContainer = styled(Container)`
  position: relative;
  z-index: 2;
  padding-top: 80px; // Add padding to account for the fixed NavWrapper
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
`;

const GlassContainer = styled(Box)`
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 2rem;
  color: #ffffff;
`;

const GradientText = styled.h1`
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, #FFA500, #FF8C00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1rem;
  line-height: 1.2;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  @media (max-width: 600px) {
    font-size: 2rem;
  }
`;

const CarouselButton = styled(IconButton)`
  width: 40px;
  height: 40px;
  background-color: ${alpha('#ffffff', 0.1)};
  color: #ffffff;
  backdrop-filter: blur(4px);
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${alpha('#ffffff', 0.2)};
  }

  &:active {
    background-color: ${alpha('#ffffff', 0.3)};
  }

  svg {
    font-size: 24px;
  }
`;

const HeroSection: React.FC = () => {
  const [currentFeature, setCurrentFeature] = useState(0);
  const features = [
    {
      title: "Multi-Session Process Manager",
      description: "Manage multiple OSRS sessions simultaneously, streamline your gameplay, and maximize efficiency."
    },
    {
      title: "Advanced Bot Detection Avoidance",
      description: "Stay undetected with our sophisticated anti-detection system, ensuring a safe and smooth gaming experience."
    },
    {
      title: "Custom Scripting Engine",
      description: "Create and run your own custom scripts to automate repetitive tasks and enhance your gameplay."
    },
    {
      title: "Real-Time Analytics",
      description: "Monitor your gameplay with real-time analytics and make data-driven decisions to improve your performance."
    },
    {
      title: "User-Friendly Interface",
      description: "Enjoy a seamless and intuitive user interface designed to enhance your overall gaming experience."
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 5000); 

    return () => clearInterval(interval);
  }, [features.length]);

  const nextFeature = () => {
    setCurrentFeature((prev) => (prev + 1) % features.length);
  };

  const prevFeature = () => {
    setCurrentFeature((prev) => (prev - 1 + features.length) % features.length);
  };

  return (
    <HeroContainer>
      <ContentContainer maxWidth="lg">
        <GlassContainer>
          <Grid container spacing={2} alignItems="flex-start" justifyContent="center">
            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <GradientText>
                  Revolutionize Your OSRS Gameplay
                </GradientText>
                <Typography variant="h6" component="p" gutterBottom sx={{ textAlign: 'center', fontWeight: 300, mb: 2, fontSize: { xs: '1rem', md: '1.25rem' } }}>
                  Enhance your Oldschool Runescape experience with our advanced tools.
                </Typography>
                <Typography variant="body1" component="p" gutterBottom sx={{ textAlign: 'center', fontWeight: 300, mb: 2, fontSize: { xs: '0.875rem', md: '1rem' } }}>
                  Our tools are designed to help you manage multiple sessions, avoid detection, and create custom scripts to automate repetitive tasks.
                </Typography>
                <Typography variant="body1" component="p" gutterBottom sx={{ textAlign: 'center', fontWeight: 300, mb: 2, fontSize: { xs: '0.875rem', md: '1rem' } }}>
                  With real-time analytics and a user-friendly interface, you can monitor your gameplay and make data-driven decisions to enhance your performance.
                </Typography>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Box
                  component="img"
                  src={desktopClientImage}
                  alt="Desktop Client"
                  sx={{
                    width: '100%',
                    height: 'auto',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                    borderRadius: '8px',
                    mt: { xs: 2, md: 0 },
                  }}
                />
              </motion.div>
            </Grid>
          </Grid>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            style={{ width: '100%', margin: '0 auto' }}
          >
            <Box sx={{ position: 'relative', height: { xs: '180px', md: '150px' }, display: 'flex', alignItems: 'center', width: '100%', mt: { xs: 2, md: 4 } }}>
              <CarouselButton onClick={prevFeature} aria-label="Previous feature" sx={{ position: 'absolute', left: { xs: 0, md: 20 } }}>
                <FiChevronLeft />
              </CarouselButton>
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentFeature}
                  initial={{ opacity: 0, x: 100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -100 }}
                  transition={{ duration: 0.5 }}
                  style={{ width: '100%', textAlign: 'center', padding: '0 40px' }}
                >
                  <Typography variant="h5" component="h3" gutterBottom sx={{ fontSize: { xs: '1.25rem', md: '1.5rem' } }}>
                    {features[currentFeature].title}
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: { xs: '0.875rem', md: '1rem' } }}>
                    {features[currentFeature].description}
                  </Typography>
                </motion.div>
              </AnimatePresence>
              <CarouselButton onClick={nextFeature} aria-label="Next feature" sx={{ position: 'absolute', right: { xs: 0, md: 20 } }}>
                <FiChevronRight />
              </CarouselButton>
            </Box>
          </motion.div>
        </GlassContainer>
      </ContentContainer>
    </HeroContainer>
  );
};

export default HeroSection;