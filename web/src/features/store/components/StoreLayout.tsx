import React, { useState, useEffect } from 'react';
import {
    Box,
    Grid,
    Pa<PERSON>ation,
    TextField,
    FormGroup,
    FormControlLabel,
    Checkbox,
    Typography,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    IconButton,
    Stack,
    useMediaQuery,
    useTheme,
    Tooltip,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ViewListIcon from '@mui/icons-material/ViewList';
import ViewModuleIcon from '@mui/icons-material/ViewModule';
import StoreItemCard from './StoreItemCard';
import { mockStoreItems } from '@backend/mock/store/mock_items';
import { storeStyles } from '@styles/components/store';

type ViewMode = 'list' | 'grid';

const ITEMS_PER_PAGE = 12;
const CATEGORIES = [
    'Agility', 'Combat', 'Construction', 'Cooking', 'Crafting',
    'Farming', 'Firemaking', 'Fishing', 'Herblore', 'Hunter',
    'Magic', 'Minigames', 'Mining', 'Money Making', 'Prayer',
    'Questing', 'Runecrafting', 'Slayer', 'Smithing', 'Thieving',
    'Woodcutting'
].sort();

const StoreLayout: React.FC = () => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
    const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
    const [page, setPage] = useState(1);
    const [search, setSearch] = useState('');
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
    const [expanded, setExpanded] = useState<string>('categories');
    const [viewMode, setViewMode] = useState<ViewMode>('grid');

    useEffect(() => {
        if (isMobile) {
            setExpanded('');
        } else if (isTablet) {
            setExpanded('');
        } else if (isDesktop) {
            setExpanded('categories');
        }
    }, [isMobile, isTablet, isDesktop]);

    const handleCategoryChange = (category: string) => {
        setSelectedCategories(prev => {
            if (prev.includes(category)) {
                return prev.filter(c => c !== category);
            }
            return [...prev, category];
        });
        setPage(1);
    };

    const filteredItems = mockStoreItems.filter(item => {
        const matchesSearch = search === '' ||
            item.name.toLowerCase().includes(search.toLowerCase());
        const matchesCategories = selectedCategories.length === 0 ||
            selectedCategories.every(cat => item.categories.includes(cat));
        return matchesSearch && matchesCategories;
    });

    const paginatedItems = filteredItems.slice(
        (page - 1) * ITEMS_PER_PAGE,
        page * ITEMS_PER_PAGE
    );


    return (
        <Box sx={{
            ...storeStyles.layout.wrapper,
            px: { xs: 2 },
        }}>
            <Grid container spacing={2}>
                <Grid
                    size={{ xs: 12, md: 3 }}
                    sx={{
                        mb: { xs: 1, md: 1 },
                    }}
                >
                    <Box sx={storeStyles.sidebar.wrapper}>
                        <Accordion
                            expanded={expanded === 'categories'}
                            onChange={() => setExpanded(expanded ? '' : 'categories')}
                            sx={storeStyles.sidebar.accordion}
                            TransitionProps={{ unmountOnExit: true }}
                        >
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Typography>Categories</Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                                <FormGroup>
                                    {CATEGORIES.map((category) => (
                                        <FormControlLabel
                                            key={category}
                                            control={
                                                <Checkbox
                                                    checked={selectedCategories.includes(category)}
                                                    onChange={() => handleCategoryChange(category)}
                                                />
                                            }
                                            label={category}
                                        />
                                    ))}
                                </FormGroup>
                            </AccordionDetails>
                        </Accordion>
                    </Box>
                </Grid>

                <Grid size={{ xs: 12, md: 9 }}>
                    <Box sx={storeStyles.layout.header}>
                        <TextField
                            placeholder="Search"
                            value={search}
                            onChange={(e) => setSearch(e.target.value)}
                            sx={storeStyles.searchField}
                        />
                        {!isMobile && (
                            <Stack direction="row" spacing={1}>
                                <Tooltip title="List" arrow placement="top">
                                    <IconButton
                                        onClick={() => setViewMode('list')}
                                        sx={storeStyles.viewButton(viewMode === 'list')}
                                    >
                                        <ViewListIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Grid" arrow placement="top">
                                    <IconButton
                                        onClick={() => setViewMode('grid')}
                                        sx={storeStyles.viewButton(viewMode === 'grid')}
                                    >
                                        <ViewModuleIcon />
                                    </IconButton>
                                </Tooltip>
                            </Stack>
                        )}
                    </Box>

                    <Box sx={viewMode === 'list' ? storeStyles.layout.listContainer : storeStyles.layout.gridContainer}>
                        {paginatedItems.map((item) => (
                            <Box
                                key={item.id}
                                sx={viewMode === 'list' ? storeStyles.layout.listItem : storeStyles.layout.gridItem}
                            >
                                <StoreItemCard
                                    item={item}
                                    viewMode={isMobile ? 'list' : viewMode}
                                />
                            </Box>
                        ))}
                    </Box>

                    {paginatedItems.length > 0 && (
                        <Box sx={storeStyles.layout.pagination}>
                            <Pagination
                                count={Math.ceil(filteredItems.length / ITEMS_PER_PAGE)}
                                page={page}
                                onChange={(_, value) => setPage(value)}
                                color="primary"
                            />
                        </Box>
                    )}
                </Grid>
            </Grid>
        </Box>
    );
};

export default StoreLayout; 