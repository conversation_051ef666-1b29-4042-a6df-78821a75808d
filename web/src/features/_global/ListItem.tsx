import React from 'react';
import { ListItem as MuiListItem, ListItemProps } from '@mui/material';
import Button from '@features/_global/Button';
import { styled } from '@mui/material/styles';
import { Link } from 'react-router-dom';

interface CustomListItemProps extends ListItemProps {
  to?: string;
}

const StyledListItem = styled(MuiListItem)(({ theme }) => ({
  '& .MuiButton-root': {
    width: '100%',
    justifyContent: 'flex-start',
    color: theme.palette.text.primary,
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
}));

const ListItem: React.FC<CustomListItemProps> = (props) => {
  const { to, children, ...rest } = props;

  const buttonContent = (
    <Button component="div" variantType="secondary" fullWidth>
      {children}
    </Button>
  );

  if (to) {
    return (
      <StyledListItem {...rest}>
        <Link to={to} style={{ textDecoration: 'none', width: '100%' }}>
          {buttonContent}
        </Link>
      </StyledListItem>
    );
  }

  return (
    <StyledListItem {...rest}>
      {buttonContent}
    </StyledListItem>
  );
};

export default ListItem;