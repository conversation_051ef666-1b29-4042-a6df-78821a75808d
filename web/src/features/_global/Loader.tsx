import React from 'react';
import { motion, Variants } from "framer-motion";
import { Typography, Box } from '@mui/material';

const variants: Variants = {
  initial: {
    scaleY: 0.5,
    opacity: 0,
  },
  animate: {
    scaleY: 1,
    opacity: 1,
    transition: {
      repeat: Infinity,
      repeatType: "mirror",
      duration: 1,
      ease: "circIn",
    },
  },
};

const BarLoader: React.FC = () => {
  return (
    <motion.div
      transition={{
        staggerChildren: 0.25,
      }}
      initial="initial"
      animate="animate"
      style={{ display: 'flex', gap: '4px' }}
    >
      {[...Array(5)].map((_, index) => (
        <motion.div 
          key={index} 
          variants={variants} 
          style={{ height: '48px', width: '8px', backgroundColor: 'white' }} 
        />
      ))}
    </motion.div>
  );
};

const Loader: React.FC = () => {
  return (
    <Box 
      display="flex" 
      flexDirection="column" 
      alignItems="center" 
      justifyContent="center" 
      height="100vh"
    >
      <BarLoader />
      <Typography variant="body1" component="div" style={{ marginTop: '16px' }}>
        Loading...
      </Typography>
    </Box>
  );
};

export default Loader;