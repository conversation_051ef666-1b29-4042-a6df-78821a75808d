export const handleSectionClick = (e: React.MouseEvent<HTMLAnchorElement>, sectionId: string) => {
    e.preventDefault();
    const section = document.getElementById(sectionId);
    if (section) {
      const navHeight = document.querySelector('nav')?.offsetHeight || 0;
      const extraSpace = 16;
      const yOffset = -(navHeight + extraSpace);
      const y = section.getBoundingClientRect().top + window.scrollY + yOffset;
      window.scrollTo({top: y, behavior: 'smooth'});
    }
  };