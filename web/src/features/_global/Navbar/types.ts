export interface ButtonsProps {
  setMenuOpen: (value: boolean) => void;
  menuOpen: boolean;
  isAuthPage: boolean;
  isLandingPage: boolean;
  isAuthenticated: boolean;
  username: string;
}

export interface MobileMenuProps {
  menuOpen: boolean;
  setMenuOpen: (value: boolean) => void;
  isAuthPage: boolean;
  isLandingPage: boolean;
  handleBackClick: () => void;
  handleContactClick: () => void;
  isAuthenticated: boolean;
  username: string;
}
