import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Nav<PERSON>rapper, NavContainer, NavContent, AuthNavContent, BackButton, ContactButton, LogoWrapper, MobileMenuWrapper, DesktopButtonsWrapper } from "./styles/NavbarStyles";
import Logo from "./components/Logo";
import Links from "./components/Links";
import Buttons from "./components/Buttons";
import MobileMenu from "./components/MobileMenu";
import { useAuth } from "@features/auth/hooks/useAuth";
import Hamburger from "./components/Hamburger";
import { useMediaQuery } from "@mui/material";

const Navbar: React.FC = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const { isAuthenticated, user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery('(max-width:768px)');

  const isAuthPage = location.pathname.startsWith("/auth");
  const isLandingPage = location.pathname === "/";

  const handleContactClick = () => {
    window.location.href = "mailto:<EMAIL>";
  };

  const handleBackClick = () => {
    navigate(-1);
  };

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  return (
    <NavWrapper>
      <NavContainer $isAuthPage={isAuthPage}>
        <MobileMenuWrapper>
          <Hamburger isOpen={menuOpen} toggle={toggleMenu} />
        </MobileMenuWrapper>
        <LogoWrapper>
          <Logo isLandingPage={isLandingPage} />
        </LogoWrapper>
        {!isMobile && (
          <>
            {isAuthPage ? (
              <AuthNavContent>
                <BackButton onClick={handleBackClick}>&larr; Back</BackButton>
                <ContactButton onClick={handleContactClick}>Contact</ContactButton>
              </AuthNavContent>
            ) : (
              <NavContent>
                {isLandingPage && <Links />}
                <DesktopButtonsWrapper>
                  <Buttons
                    isAuthPage={isAuthPage}
                    isLandingPage={isLandingPage}
                    setMenuOpen={setMenuOpen}
                    isAuthenticated={isAuthenticated}
                    username={user?.name || user?.email || ""}
                    menuOpen={menuOpen}
                  />
                </DesktopButtonsWrapper>
              </NavContent>
            )}
          </>
        )}
        <MobileMenu
          menuOpen={menuOpen}
          setMenuOpen={setMenuOpen}
          isAuthPage={isAuthPage}
          isLandingPage={isLandingPage}
          handleBackClick={handleBackClick}
          handleContactClick={handleContactClick}
          isAuthenticated={isAuthenticated}
          username={user?.name || user?.email || ""}
        />
      </NavContainer>
    </NavWrapper>
  );
};

export default Navbar;