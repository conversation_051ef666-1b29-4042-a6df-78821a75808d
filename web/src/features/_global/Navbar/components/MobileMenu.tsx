import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import useMeasure from "react-use-measure";
import { MobileMenuContent, MobileNavLink, MobileButtonContainer, MobileSignInButton, MobileRequestInviteButton } from "../styles/NavbarStyles";
import { handleSectionClick } from "../hooks/useNavigation";
import { MobileMenuProps } from "../types";
import UserMenu from "./UserMenu";
import { useAuth } from "@features/auth/hooks/useAuth";

const MobileMenu: React.FC<MobileMenuProps> = ({ 
  menuOpen, 
  setMenuOpen, 
  isAuthPage, 
  isLandingPage,
  handleBackClick,
  handleContactClick
}) => {
  const [ref, { height }] = useMeasure();
  const { isAuthenticated, user } = useAuth();
  
  const handleLinkClick = (e: React.MouseEvent<HTMLAnchorElement>, sectionId: string) => {
    handleSectionClick(e, sectionId);
    setMenuOpen(false);
  };

  return (
    <motion.div
      initial={false}
      animate={{
        height: menuOpen ? height : "0px",
      }}
      className="overflow-hidden md:hidden fixed top-16 left-0 right-0 bg-black z-50"
    >
      <MobileMenuContent ref={ref}>
        {isAuthPage ? (
          <>
            <MobileNavLink onClick={handleBackClick}>&larr; Back</MobileNavLink>
            <MobileNavLink onClick={handleContactClick}>Contact</MobileNavLink>
          </>
        ) : (
          <>
            {isLandingPage && (
              <>
                <MobileNavLink href="#benefits" onClick={(e) => handleLinkClick(e, 'why-choose-section')}>Benefits</MobileNavLink>
                <MobileNavLink href="#features" onClick={(e) => handleLinkClick(e, 'future-section')}>Features</MobileNavLink>
              </>
            )}
            <MobileButtonContainer>
              {isAuthenticated ? (
                <UserMenu username={user?.name || user?.email || ""} />
              ) : (
                <>
                  <MobileSignInButton as={Link} to="/auth/login">Sign In</MobileSignInButton>
                  <MobileRequestInviteButton as={Link} to="/invite">Request Invite</MobileRequestInviteButton>
                </>
              )}
            </MobileButtonContainer>
          </>
        )}
      </MobileMenuContent>
    </motion.div>
  );
};

export default MobileMenu;