import React from 'react';
import { useAuth } from '@features/auth/hooks/useAuth';

interface UserMenuProps {
  username: string;
}

const UserMenu: React.FC<UserMenuProps> = ({ username }) => {
  const { logout } = useAuth();

  const handleLogout = () => {
    logout();
    // Add any additional logout logic here
  };

  return (
    <div>
      <span>Welcome, {username}</span>
      <button onClick={handleLogout}>Logout</button>
    </div>
  );
};

export default UserMenu;