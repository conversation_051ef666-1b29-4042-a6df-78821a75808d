import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@features/auth/hooks/useAuth';
import { Avatar, MenuItem, Tooltip, IconButton, Menu } from '@mui/material';
import { navbarStyles } from '@styles/components/navbar';

const UserMenu: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { logout, user } = useAuth();
  const navigate = useNavigate();

  const handleOpenMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleMenuClick = (action: string) => {
    handleCloseMenu();
    switch (action) {
      case 'Dashboard':
        navigate('/dashboard');
        break;
      case 'Order History':
        navigate('/dashboard/orders');
        break;
      case 'Account Settings':
        navigate('/dashboard/security');
        break;
      case 'Session Manager':
        navigate('/dashboard/sessions');
        break;
      case 'Logout':
        logout();
        break;
    }
  };

  return (
    <>
      <Tooltip title="Open settings">
        <IconButton onClick={handleOpenMenu} sx={navbarStyles.userMenu.button}>
          <Avatar 
            sx={{ 
              width: 40, 
              height: 40,
              bgcolor: '#ffa500',
              fontSize: '0.9rem',
              fontWeight: 'bold',
              color: '#000',
            }}
          >
            {user?.email.charAt(0).toUpperCase()}
          </Avatar>
        </IconButton>
      </Tooltip>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        sx={navbarStyles.userMenu.dropdown}
      >
        {['Dashboard', 'Order History', 'Account Settings', 'Session Manager', 'Logout'].map((setting) => (
          <MenuItem key={setting} onClick={() => handleMenuClick(setting)}>
            {setting}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default UserMenu; 