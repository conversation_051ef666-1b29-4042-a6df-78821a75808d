import React from "react";
import { <PERSON> } from "react-router-dom";
import { FiMenu } from "react-icons/fi";
import { ButtonsContainer, DesktopButtons, SignInButton, RequestInviteButton, MenuButton, WelcomeText, DashboardButton } from "../styles/NavbarStyles";
import { ButtonsProps } from "../types";

const Buttons: React.FC<ButtonsProps> = ({ setMenuOpen, menuOpen, isAuthPage, isAuthenticated, username }) => {
  return (
    <ButtonsContainer>
      <DesktopButtons>
        {isAuthenticated ? (
          <>
            <WelcomeText>Welcome back, {username}</WelcomeText>
            <DashboardButton as={Link} to="/dashboard">Dashboard &rarr;</DashboardButton>
          </>
        ) : (
          !isAuthPage && (
            <>
              <SignInButton as={Link} to="/auth/login">Sign In</SignInButton>
              <RequestInviteButton as={Link} to="/invite">Request Invite</RequestInviteButton>
            </>
          )
        )}
      </DesktopButtons>
      {!isAuthPage && (
        <MenuButton onClick={() => setMenuOpen(!menuOpen)}>
          <FiMenu />
        </MenuButton>
      )}
    </ButtonsContainer>
  );
};

export default Buttons;