import React from "react";
import { LinksContainer, GlassLink } from "../styles/NavbarStyles";
import { handleSectionClick } from "../hooks/useNavigation";

const Links: React.FC = () => (
  <LinksContainer>
    <GlassLink href="#benefits" onClick={(e) => handleSectionClick(e, 'why-choose-section')}>Benefits</GlassLink>
    <GlassLink href="#features" onClick={(e) => handleSectionClick(e, 'future-section')}>Features</GlassLink>
  </LinksContainer>
);

export default Links;