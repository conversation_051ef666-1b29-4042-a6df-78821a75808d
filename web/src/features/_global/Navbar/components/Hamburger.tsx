import React from 'react';
import { motion } from 'framer-motion';
import styled from '@emotion/styled';

interface HamburgerProps {
  isOpen: boolean;
  toggle: () => void;
}

const HamburgerButton = styled.button`
  position: absolute;
  top: -10px;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 1000;
`;

const Line = styled(motion.div)`
  width: 24px;
  height: 2px;
  background-color: #FFA500;
  margin: 4px 0;
`;

const Hamburger: React.FC<HamburgerProps> = ({ isOpen, toggle }) => {
  return (
    <HamburgerButton onClick={toggle}>
      <Line
        animate={isOpen ? { rotate: 45, y: 6 } : { rotate: 0, y: 0 }}
        transition={{ duration: 0.2 }}
      />
      <Line
        animate={isOpen ? { opacity: 0 } : { opacity: 1 }}
        transition={{ duration: 0.2 }}
      />
      <Line
        animate={isOpen ? { rotate: -45, y: -6 } : { rotate: 0, y: 0 }}
        transition={{ duration: 0.2 }}
      />
    </HamburgerButton>
  );
};

export default Hamburger;