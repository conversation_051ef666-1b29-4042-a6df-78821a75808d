import React from "react";
import { useNavigate } from "react-router-dom";
import { LogoLink, LogoImage, LogoText, BranchChip } from "../styles/NavbarStyles";
import logo from "@assets/logo.png";
import { CURRENT_BRANCH } from "@backend/backend";

interface LogoProps {
  isLandingPage?: boolean;
}

const Logo: React.FC<LogoProps> = ({ isLandingPage }) => {
    const navigate = useNavigate();
  
    const handleLogoClick = (e: React.MouseEvent) => {
      if (isLandingPage) {
        e.preventDefault();
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        navigate("/");
      }
    };
  
    return (
      <LogoLink to="/" onClick={handleLogoClick}>
        <LogoImage src={logo} alt="RSGlider Logo" />
        <LogoText>
          <span className="rs">rs</span>
          <span className="glider">glider</span>
        </LogoText>
        {CURRENT_BRANCH && CURRENT_BRANCH !== 'production' && (
          <BranchChip>{CURRENT_BRANCH}</BranchChip>
        )}
      </LogoLink>
    );
  };

export default Logo;