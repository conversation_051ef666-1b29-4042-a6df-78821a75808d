import styled from "styled-components";
import { Link } from "react-router-dom";
import { Menu, MenuItem } from "@mui/material";

export const NavWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
  transform: translateZ(0);
  will-change: transform;
`;

export const NavContainer = styled.nav<{ $isAuthPage?: boolean }>`
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 12px 24px;
  width: 100%;
  max-width: 1152px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px; // Set a fixed height
`;

// Adjust NavContent to fill the container
export const NavContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
`;

// Adjust AuthNavContent similarly
export const Auth<PERSON>avContent = styled(NavContent)`
  position: relative;
  justify-content: center;
`;

export const LogoLink = styled(Link)`
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

export const LogoImage = styled.img`
  width: 30px;
  height: 30px;
  margin-right: 8px;
`;

export const LogoText = styled.span`
  font-family: "Arial", sans-serif;
  font-size: 1.5rem;
  font-weight: 900;
  display: flex;
  align-items: center;

  .rs {
    color: #ffffff;
    margin-right: 2px;
    text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000,
      1px 1px 0 #000;
  }

  .glider {
    background: linear-gradient(45deg, #ffa500, #ff8c00, #ffa500, #ff4500);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    animation: gradientShift 6s ease infinite;
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
`;

export const LinksContainer = styled.div`
  display: flex;
  gap: 16px;

  @media (max-width: 768px) {
    display: none;
  }
`;

export const GlassLink = styled.a`
  color: #ffffff;
  text-decoration: none;
  font-family: "Arial", sans-serif;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 6px 12px;
  border-radius: 8px;
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover,
  &:focus {
    background: rgba(255, 255, 255, 0.2);
  }

  &:active {
    transform: scale(0.98);
  }
`;

export const ButtonsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const DesktopButtons = styled.div`
  display: flex;
  gap: 16px;

  @media (max-width: 768px) {
    display: none;
  }
`;

const BaseButton = styled.button`
  font-family: "Arial", sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 8px 16px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
`;

export const SignInButton = styled(BaseButton)`
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

export const RequestInviteButton = styled(BaseButton)`
  background: #ffa500;
  color: #000000;
  border: none;

  &:hover {
    background: #ff8c00;
  }
`;

export const MenuButton = styled.button`
  display: none;
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #ffffff;
  }

  @media (max-width: 768px) {
    display: block;
  }
`;

export const MobileMenuContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 24px;
`;

export const MobileNavLink = styled.a`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-family: "Arial", sans-serif;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 12px 16px;
  border-radius: 8px;
  background: transparent;
  border: 1px solid transparent;

  &:hover,
  &:focus {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
  }

  &:active {
    transform: scale(0.98);
  }
`;

export const MobileButtonContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 10px;
`;

const MobileBaseButton = styled(BaseButton)`
  flex: 1;
  padding: 12px 20px;
  font-size: 0.9rem;
  text-align: center;
  transition: all 0.3s ease;
  border-radius: 8px;
  border: none;
`;

export const MobileSignInButton = styled(MobileBaseButton)`
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover,
  &:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
  }
`;

export const MobileRequestInviteButton = styled(MobileBaseButton)`
  background: #ffa500;
  color: #000000;

  &:hover,
  &:focus {
    background: #ff8c00;
  }
`;

export const WelcomeText = styled.span`
  color: #ffffff;
  font-family: "Arial", sans-serif;
  font-size: 0.9rem;
  margin-right: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
`;

export const DashboardButton = styled(BaseButton)`
  background: #4caf50;
  color: #ffffff;
  border: none;

  &:hover {
    background: #45a049;
  }
`;

export const AuthButton = styled(BaseButton)`
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  font-size: 0.9rem;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(0);
  }

  &:active {
    transform: translateY(1px);
  }
`;

export const BackButton = styled(AuthButton)`
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);

  &:hover {
    transform: translateY(-50%);
  }

  &:active {
    transform: translateY(-49%);
  }
`;

export const ContactButton = styled(AuthButton)`
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);

  &:hover {
    transform: translateY(-50%);
  }

  &:active {
    transform: translateY(-49%);
  }

  &::before {
    content: "✉️";
    margin-right: 6px;
  }
`;

export const LogoWrapper = styled.div`
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
`;

export const MobileMenuWrapper = styled.div`
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  @media (min-width: 769px) {
    display: none;
  }
`;

export const DesktopLinksWrapper = styled.div`
  @media (max-width: 768px) {
    display: none;
  }
`;

export const DesktopButtonsWrapper = styled.div`
  @media (max-width: 768px) {
    display: none;
  }
`;

export const StyledMenu = styled(Menu)`
  .MuiPaper-root {
    background-color: #2c2c2c;
    color: #ffffff;
  }
`;

export const StyledMenuItem = styled(MenuItem)`
  &:hover {
    background-color: #3c3c3c;
  }
`;

export const BranchChip = styled.span`
  font-size: 0.6rem;
  padding: 2px 4px;
  background-color: #ff9800; // Orange color, you can adjust as needed
  color: #fff;
  border-radius: 4px;
  position: absolute;
  top: -8px;
  right: -8px;
  font-weight: bold;
  text-transform: uppercase;
`;
