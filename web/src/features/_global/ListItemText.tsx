import React from 'react';
import { ListItemText as MuiListItemText, ListItemTextProps } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledListItemText = styled(MuiListItemText)(({ theme }) => ({
  color: theme.palette.text.primary,
}));

const ListItemText: React.FC<ListItemTextProps> = (props) => {
  return <StyledListItemText {...props} />;
};

export default ListItemText;