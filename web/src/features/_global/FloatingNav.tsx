import React, { useState } from "react";
import { motion } from "framer-motion";
import { FiMenu } from "react-icons/fi";
import useMeasure from "react-use-measure";
import styled from "styled-components";
import { Link } from "react-router-dom";
import logo from "@assets/logo.png";

const GlassNavigation: React.FC = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  return (
    <NavWrapper>
      <NavContainer>
        <NavContent>
          <Links />
          <Logo />
          <Buttons setMenuOpen={setMenuOpen} />
        </NavContent>
        <MobileMenu menuOpen={menuOpen} setMenuOpen={setMenuOpen} />
      </NavContainer>
    </NavWrapper>
  );
};

const NavWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
`;

const NavContainer = styled.nav`
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 12px 24px;
  width: 100%;
  max-width: 1152px;
  box-sizing: border-box;
`;

const NavContent = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Logo: React.FC = () => (
  <LogoLink to="/">
    <LogoImage src={logo} alt="rsglider logo" />
    <LogoText>rsglider</LogoText>
  </LogoLink>
);

const LogoLink = styled(Link)`
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
`;

const LogoImage = styled.img`
  width: 20px;
  height: 20px;
`;

const LogoText = styled.span`
  font-family: 'Arial', sans-serif;
  font-size: 1.3rem;
  font-weight: 800;
  background: linear-gradient(45deg, #FFA500, #FF8C00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
`;

const Links: React.FC = () => (
  <LinksContainer>
    <GlassLink href="#benefits" onClick={(e) => handleSectionClick(e, 'why-choose-section')}>Benefits</GlassLink>
    <GlassLink href="#features" onClick={(e) => handleSectionClick(e, 'future-section')}>Features</GlassLink>
  </LinksContainer>
);

const handleSectionClick = (e: React.MouseEvent<HTMLAnchorElement>, sectionId: string) => {
  e.preventDefault();
  const section = document.getElementById(sectionId);
  if (section) {
    const navHeight = document.querySelector('nav')?.offsetHeight || 0;
    const extraSpace = 16;
    const yOffset = -(navHeight + extraSpace);
    const y = section.getBoundingClientRect().top + window.scrollY + yOffset;
    window.scrollTo({top: y, behavior: 'smooth'});
  }
};

const LinksContainer = styled.div`
  display: flex;
  gap: 16px;

  @media (max-width: 768px) {
    display: none;
  }
`;

const GlassLink = styled.a`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-family: 'Arial', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 6px 12px;
  border-radius: 8px;
  position: relative;

  &:hover, &:focus {
    color: #ffffff;
  }

  &:nth-of-type(1) {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover, &:focus {
      background: rgba(255, 255, 255, 0.2);
    }
  }

  &:nth-of-type(2) {
    &:hover, &:focus {
      background: transparent;
    }
  }

  &:active {
    transform: scale(0.98);
  }
`;

interface ButtonsProps {
  setMenuOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Buttons: React.FC<ButtonsProps> = ({ setMenuOpen }) => (
  <ButtonsContainer>
    <DesktopButtons>
      <SignInButton as={Link} to="/auth/login">Sign In</SignInButton>
      <RequestInviteButton as={Link} to="/invite">Request Invite</RequestInviteButton>
    </DesktopButtons>
    <MenuButton onClick={() => setMenuOpen((prev) => !prev)}>
      <FiMenu />
    </MenuButton>
  </ButtonsContainer>
);

const ButtonsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const DesktopButtons = styled.div`
  display: flex;
  gap: 16px;

  @media (max-width: 768px) {
    display: none;
  }
`;

const BaseButton = styled.button`
  font-family: 'Arial', sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
`;

const SignInButton = styled(BaseButton)`
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
`;

const RequestInviteButton = styled(BaseButton)`
  background: #FFA500;
  color: #000000;
  border: none;

  &:hover {
    background: #FF8C00;
  }
`;

const MenuButton = styled.button`
  display: none;
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #ffffff;
  }

  @media (max-width: 768px) {
    display: block;
  }
`;

const MobileMenu: React.FC<{ menuOpen: boolean; setMenuOpen: (open: boolean) => void }> = ({ menuOpen, setMenuOpen }) => {
  const [ref, { height }] = useMeasure();
  
  const handleLinkClick = (e: React.MouseEvent<HTMLAnchorElement>, sectionId: string) => {
    handleSectionClick(e, sectionId);
    setMenuOpen(false);
  };

  return (
    <motion.div
      initial={false}
      animate={{
        height: menuOpen ? height : "0px",
      }}
      className="overflow-hidden md:hidden"
    >
      <MobileMenuContent ref={ref}>
        <MobileNavLink href="#benefits" onClick={(e) => handleLinkClick(e, 'why-choose-section')}>Benefits</MobileNavLink>
        <MobileNavLink href="#features" onClick={(e) => handleLinkClick(e, 'future-section')}>Features</MobileNavLink>
        <MobileButtonContainer>
          <MobileSignInButton as={Link} to="/auth/login">Sign In</MobileSignInButton>
          <MobileRequestInviteButton as={Link} to="/invite">Request Invite</MobileRequestInviteButton>
        </MobileButtonContainer>
      </MobileMenuContent>
    </motion.div>
  );
};

const MobileMenuContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 24px;
`;

const MobileNavLink = styled.a`
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-family: 'Arial', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 12px 16px;
  border-radius: 8px;
  background: transparent;
  border: 1px solid transparent;

  &:hover, &:focus {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
  }

  &:active {
    transform: scale(0.98);
  }
`;

const MobileButtonContainer = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 10px;
`;

const MobileBaseButton = styled(BaseButton)`
  flex: 1;
  padding: 12px 20px;
  font-size: 0.9rem;
  text-align: center;
  transition: all 0.3s ease;
  border-radius: 8px;
  border: none;
`;

const MobileSignInButton = styled(MobileBaseButton)`
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover, &:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
  }
`;

const MobileRequestInviteButton = styled(MobileBaseButton)`
  background: #FFA500;
  color: #000000;

  &:hover, &:focus {
    background: #FF8C00;
  }
`;

export default GlassNavigation;