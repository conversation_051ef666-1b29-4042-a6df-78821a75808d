import React from 'react';
import { motion } from 'framer-motion';

interface SectionDividerProps {
  color?: string;
  height?: number;
  position?: 'top' | 'bottom';
  animate?: boolean;
}

const SectionDivider: React.FC<SectionDividerProps> = ({ 
  color = '#020617', 
  height = 100, 
  position = 'bottom',
  animate = true
}) => {
  const path = position === 'top' 
    ? "M0 100L48 91.6667C96 83.3333 192 66.6667 288 58.3333C384 50 480 50 576 58.3333C672 66.6667 768 83.3333 864 91.6667C960 100 1056 100 1152 91.6667C1248 83.3333 1344 66.6667 1392 58.3333L1440 50V0H0V100Z"
    : "M0 0L48 8.33333C96 16.6667 192 33.3333 288 41.6667C384 50 480 50 576 41.6667C672 33.3333 768 16.6667 864 8.33333C960 0 1056 0 1152 8.33333C1248 16.6667 1344 33.3333 1392 41.6667L1440 50V100H0V0Z";

  const pathVariants = {
    hidden: {
      pathLength: 0,
      opacity: 0
    },
    visible: {
      pathLength: 1,
      opacity: 1,
      transition: {
        duration: 2,
        ease: "easeInOut"
      }
    }
  };

  return (
    <svg viewBox="0 0 1440 100" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ display: 'block', width: '100%', height: `${height}px` }}>
      <motion.path
        d={path}
        fill={color}
        variants={animate ? pathVariants : undefined}
        initial="hidden"
        animate="visible"
      />
    </svg>
  );
};

export default SectionDivider;