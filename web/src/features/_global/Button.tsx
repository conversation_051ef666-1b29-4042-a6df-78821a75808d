import React from 'react';
import { ButtonProps as MuiButtonProps } from '@mui/material';
import { MotionProps } from 'framer-motion';
import MotionButton from './MotionButton';

interface ButtonOwnProps extends MuiButtonProps {
  variantType?: 'primary' | 'secondary' | 'tertiary';
  component?: React.ElementType;
  to?: string;
}

type CombinedButtonProps = ButtonOwnProps & MotionProps;

const Button = React.forwardRef<HTMLButtonElement, CombinedButtonProps>((props, ref) => {
  const { variantType = 'primary', ...otherProps } = props;
  return (
    <MotionButton
      ref={ref}
      sx={{
        position: 'relative',
        padding: '10px 20px',
        fontSize: '16px',
        fontWeight: 'medium',
        border: 'none',
        cursor: 'pointer',
        overflow: 'hidden',
        transition: 'all 0.4s ease-in-out',
        color: variantType === 'primary' ? '#13FFAA' : '#00BFFF',
        backgroundColor: 'transparent',
        '&::before, &::after, & > span::before, & > span::after': {
          content: '""',
          position: 'absolute',
          background: variantType === 'primary' ? '#13FFAA' : '#00BFFF',
          transition: 'all 0.4s ease-in-out',
        },
        '&::before, &::after': {
          height: '2px',
          width: '0',
        },
        '& > span::before, & > span::after': {
          width: '2px',
          height: '0',
        },
        '&::before, & > span::before': {
          left: 0,
          top: 0,
        },
        '&::after, & > span::after': {
          right: 0,
          bottom: 0,
        },
        '&:hover': {
          '&::before, &::after': {
            width: '100%',
          },
          '& > span::before, & > span::after': {
            height: '100%',
          },
          backgroundColor: variantType === 'primary' ? 'rgba(19, 255, 170, 0.1)' : 'rgba(0, 191, 255, 0.1)',
        },
      }}
      {...otherProps}
    >
      <span>{props.children}</span>
    </MotionButton>
  );
});

Button.displayName = 'Button';

export default Button;