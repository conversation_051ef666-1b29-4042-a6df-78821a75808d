import React from 'react';
import { Link as RouterLink, LinkProps as RouterLinkProps } from 'react-router-dom';
import { Button, ButtonProps } from '@mui/material';
import styled from '@emotion/styled';

const LinkButton = styled(
  React.forwardRef<HTMLAnchorElement, ButtonProps & RouterLinkProps>(
    (props, ref) => <Button component={RouterLink} ref={ref} {...props} />
  )
)`
  margin-top: 16px;
  width: 100%;
  background-color: ${(props: ButtonProps) => props.variant === 'contained' ? '#FFA500' : 'transparent'};
  color: ${(props: ButtonProps) => props.variant === 'contained' ? 'black' : 'white'};
  border: ${(props: ButtonProps) => props.variant === 'outlined' ? '1px solid rgba(255, 255, 255, 0.2)' : 'none'};

  &:hover {
    background-color: ${(props: ButtonProps) => props.variant === 'contained' ? '#FF8C00' : 'rgba(255, 255, 255, 0.1)'};
  }
`;

export default LinkButton;