import { SxProps, Theme } from '@mui/material';
import { colors, transitions } from '../constants';
import { glassStyles } from './glass';
import { buttonStyles } from './buttons';

// Move menu styles directly into navbar.ts
const userMenuStyles = {
  button: {
    padding: 0,
    marginLeft: 1,
    zIndex: 1001, // Ensure button stays above other elements
  },
  dropdown: {
    zIndex: 1002, // Ensure dropdown is above everything
    '& .MuiPaper-root': {
      background: 'rgba(0, 0, 0, 0.9)', // Slightly more opaque
      backdropFilter: 'blur(15px)', // Stronger blur
      border: `1px solid ${colors.glass.border}`,
      borderRadius: 4,
      minWidth: 160,
      marginTop: 1,
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)', // Add shadow for depth
    },
    '& .MuiMenuItem-root': {
      color: colors.text.primary,
      fontSize: '0.9rem',
      padding: '8px 16px',
      '&:hover': {
        background: 'rgba(255, 255, 255, 0.1)',
      },
    },
  },
} as const;

interface NavbarStylesType {
  wrapper: SxProps<Theme>;
  container: SxProps<Theme>;
  logo: {
    wrapper: SxProps<Theme>;
    link: SxProps<Theme>;
    image: SxProps<Theme>;
    text: SxProps<Theme>;
    brand: {
      rs: SxProps<Theme>;
      glider: SxProps<Theme>;
    };
  };
  buttons: {
    container: SxProps<Theme>;
    group: SxProps<Theme>;
    base: SxProps<Theme>;
    signIn: SxProps<Theme>;
    register: SxProps<Theme>;
    dashboard: SxProps<Theme>;
    home: SxProps<Theme>;
    back: SxProps<Theme>;
    contact: SxProps<Theme>;
    transparent: SxProps<Theme>;
    glass: SxProps<Theme>;
    branded: SxProps<Theme>;
    store: SxProps<Theme>;
  };
  mobile: {
    menu: {
      overlay: SxProps<Theme>;
      button: SxProps<Theme>;
    };
  };
  userMenu: typeof userMenuStyles;
}

export const navbarStyles: NavbarStylesType = {
  wrapper: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    display: 'flex',
    justifyContent: 'center',
    zIndex: 1000,
    padding: 2,
    transform: 'translateZ(0)',
    willChange: 'transform',
    minHeight: '80px', // Ensure minimum height
    boxSizing: 'border-box',
  },

  container: {
    ...glassStyles.nav,
    position: 'relative',
    overflow: 'visible', // Ensure dropdown can extend outside
  },

  logo: {
    wrapper: {
      userSelect: 'none',
      position: 'absolute',
      left: '50%',
      top: '50%',
      transform: 'translate(-50%, -50%)',
      zIndex: 1000,
      pointerEvents: 'all',
    },
    link: {
      display: 'flex',
      alignItems: 'center',
      gap: 1,
      textDecoration: 'none',
      transition: transitions.default,
      '&:hover': {
        transform: 'scale(1.05)',
      },
    },
    image: {
      width: 30,
      height: 30,
      marginRight: 1,
    },
    text: {
      fontFamily: '"Arial", sans-serif',
      fontSize: '1.5rem',
      fontWeight: 900,
      display: 'flex',
      alignItems: 'center',
    },
    brand: {
      rs: {
        color: colors.text.primary,
        marginRight: '2px',
        textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)',
      },
      glider: {
        background: 'linear-gradient(45deg, #FFA500, #FF8C00)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
      },
    },
  },

  buttons: {
    container: {
      display: 'flex',
      alignItems: 'center',
      gap: 2,
      width: '100%',
      justifyContent: 'space-between',
    },
    group: {
      display: 'flex',
      gap: 2,
      alignItems: 'center',
    },
    base: {
      fontFamily: '"Arial", sans-serif',
      fontWeight: 500,
      fontSize: '1rem',
      padding: '8px 16px',
      height: 40,
      borderRadius: 1,
      transition: transitions.default,
      cursor: 'pointer',
      minWidth: 100,
      border: '1px solid transparent',
      textTransform: 'none !important',
    },
    signIn: {
      background: 'rgba(255, 255, 255, 0.1)',
      color: colors.text.primary,
      border: '1px solid rgba(255, 255, 255, 0.2)',
      '&:hover': {
        background: 'rgba(255, 255, 255, 0.2)',
      },
    },
    register: {
      background: colors.text.brand,
      color: colors.text.black,
      height: '40px',
      padding: '8px 20px',
      borderRadius: 2,
      fontWeight: 500,
      '&:hover': {
        background: colors.text.darkbrand,
      },
    },
    dashboard: {
      background: colors.text.brand,
      color: colors.text.black,
      height: '40px',
      padding: '8px 20px',
      borderRadius: 2,
      fontWeight: 500,
      '&:hover': {
        background: colors.text.darkbrand,
      },
    },
    home: {
      background: 'rgba(255, 255, 255, 0.1)',
      color: colors.text.primary,
      border: '1px solid rgba(255, 255, 255, 0.2)',
      '&:hover': {
        background: 'rgba(255, 255, 255, 0.2)',
      },
    },
    back: {
      background: 'rgba(255, 255, 255, 0.1)',
      color: colors.text.primary,
      border: '1px solid rgba(255, 255, 255, 0.2)',
      '&:hover': {
        background: 'rgba(255, 255, 255, 0.2)',
      },
      '&::before': {
        content: '"←"',
        marginRight: 1,
      },
    },
    contact: {
      background: 'rgba(255, 255, 255, 0.1)',
      color: colors.text.primary,
      border: '1px solid rgba(255, 255, 255, 0.2)',
      '&:hover': {
        background: 'rgba(255, 255, 255, 0.2)',
      },
      '&::before': {
        content: '"✉️"',
        marginRight: 1,
      },
    },
    transparent: {
      background: 'transparent',
      color: colors.text.primary,
      border: `1px solid ${colors.glass.border}`,
      minWidth: 'unset',
      '&:hover': {
        background: 'rgba(255, 255, 255, 0.1)',
      },
    },
    glass: buttonStyles.glass,
    branded: buttonStyles.branded,
    store: buttonStyles.success,
  },

  mobile: {
    menu: {
      overlay: {
        ...glassStyles.container,
        position: 'fixed',
        top: 96,
        left: 2,
        right: 2,
        display: 'flex',
        flexDirection: 'column',
        gap: 1.5,
        padding: 2,
        zIndex: 1000,
        border: `1px solid ${colors.glass.border}`,
        borderRadius: 5,
        background: 'rgba(0, 0, 0, 0.9)',
        backdropFilter: 'blur(10px)',
        '& .MuiButton-root': {
          background: colors.glass.button.background,
          backdropFilter: 'blur(10px)',
          color: colors.text.primary,
          border: `1px solid ${colors.glass.button.border}`,
          borderRadius: 2,
          transition: transitions.default,
          cursor: 'pointer',
          minWidth: 'unset',
          height: '40px',
          padding: '8px 20px',
          width: '100%',
          justifyContent: 'center',
          '&:hover': {
            background: colors.glass.button.hover,
          },
        },
        '& [href="/dashboard"], & [href="/auth/register"]': {
          background: colors.text.brand,
          color: colors.text.black,
          height: '40px',
          padding: '8px 20px',
          borderRadius: 2,
          fontWeight: 500,
          '&:hover': {
            background: colors.text.darkbrand,
          },
        },
        '& [href="/store"]': {
          background: colors.success.main,
          '&:hover': {
            background: colors.success.dark,
          },
        },
      },
      button: {
        display: { xs: 'block', md: 'none' },
        background: 'none',
        border: 'none',
        padding: 1,
        cursor: 'pointer',
        zIndex: 1001,
      },
    },
  },

  userMenu: userMenuStyles,
}; 