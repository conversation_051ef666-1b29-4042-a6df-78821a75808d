import { SxProps, Theme } from '@mui/material';
import { colors, transitions, sharedMenuStyles } from '../constants';
import { inputStyles } from './inputs';

interface DashboardStylesType {
  layout: {
    notouch: SxProps<Theme>;
    wrapper: SxProps<Theme>;
    sidebar: SxProps<Theme>;
  };
  typography: {
    header: SxProps<Theme>;
    subheader: SxProps<Theme>;
    body: SxProps<Theme>;
  };
  buttons: {
    primary: SxProps<Theme>;
    success: SxProps<Theme>;
    cancel: SxProps<Theme>;
    danger: SxProps<Theme>;
  };
  table: {
    container: SxProps<Theme>;
    cell: {
      header: SxProps<Theme>;
      body: SxProps<Theme>;
    };
    chip: {
      success: SxProps<Theme>;
      warning: SxProps<Theme>;
      error: SxProps<Theme>;
      default: SxProps<Theme>;
      base: SxProps<Theme>;
    };
    header: {
      wrapper: SxProps<Theme>;
    };
    pagination: TablePaginationType;
  };
  menuSection: {
    wrapper: SxProps<Theme>;
    header: SxProps<Theme>;
    list: SxProps<Theme>;
  };
  menuItem: (isActive: boolean) => SxProps<Theme>;
  menuItemIcon: SxProps<Theme>;
  security: {
    passwordMeter: {
      wrapper: SxProps<Theme>;
      segments: SxProps<Theme>;
      segment: (active: boolean, color: string) => SxProps<Theme>;
      helper: SxProps<Theme>;
    };
    twoFactor: {
      wrapper: SxProps<Theme>;
      switch: SxProps<Theme>;
    };
  };
  discord: {
    button: SxProps<Theme>;
    statCard: SxProps<Theme>;
    icon: SxProps<Theme>;
    header: SxProps<Theme>;
    notConnected: SxProps<Theme>;
    callback: {
      container: SxProps<Theme>;
    };
  };
  statCard: SxProps<Theme>;
  overview: {
    statCard: SxProps<Theme>;
  };
  sessions: {
    deviceIcon: SxProps<Theme>;
    statusChip: (isActive: boolean) => SxProps<Theme>;
  };
  contentPaper: SxProps<Theme>;
  chart: {
    wrapper: SxProps<Theme>;
    toggleGroup: SxProps<Theme>;
    toggleButton: SxProps<Theme>;
    margin: { top: number; bottom: number; left: number; right: number };
    xAxis: {
      tickLabel: (timeRange: string) => {
        fill: string;
        angle: number;
        textAnchor: 'start' | 'middle' | 'end';
      };
    };
    yAxis: {
      label: { fill: string };
      tickLabel: { fill: string };
    };
    styles: SxProps<Theme>;
    slotProps: {
      loadingOverlay: { message: string };
      noDataOverlay: { message: string };
      legend: {
        position: { vertical: 'top'; horizontal: 'end' };
        padding: number;
        labelStyle: { fill: string };
      };
      popper: { sx: SxProps<Theme> };
    };
  };
  scripter: {
    metricCard: SxProps<Theme>;
    trendBox: (positive: boolean) => SxProps<Theme>;
    progressBar: (trend: number) => SxProps<Theme>;
    statusChip: (status: 'active' | 'inactive') => SxProps<Theme>;
  };
  textField: SxProps<Theme>;
  menu: typeof sharedMenuStyles;
  headerWithSearch: {
    wrapper: SxProps<Theme>;
    title: SxProps<Theme>;
    search: SxProps<Theme>;
  };
}

interface TablePaginationType {
  borderTop: string;
  color: string;
  userSelect: string;
  '.MuiTablePagination-select': {
    color: string;
  };
  '.MuiTablePagination-selectIcon': {
    color: string;
  };
  '.MuiTablePagination-displayedRows': {
    color: string;
  };
  '.MuiTablePagination-actions': {
    '& .MuiIconButton-root': {
      color: string;
    };
    '& .MuiIconButton-root.Mui-disabled': {
      color: string;
      opacity: number;
    };
  };
}

const baseStatCard: SxProps<Theme> = {
  background: colors.glass.background,
  backdropFilter: 'blur(10px)',
  border: `1px solid ${colors.glass.border}`,
  borderRadius: 4,
  p: 3,
  transition: transitions.default,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  '&:hover': {
    background: colors.glass.hover,
  },
};

const paginationStyles: TablePaginationType = {
  borderTop: `1px solid ${colors.glass.border}`,
  color: colors.text.primary,
  userSelect: 'none',
  '.MuiTablePagination-select': {
    color: colors.text.primary,
  },
  '.MuiTablePagination-selectIcon': {
    color: colors.text.primary,
  },
  '.MuiTablePagination-displayedRows': {
    color: colors.text.primary,
  },
  '.MuiTablePagination-actions': {
    '& .MuiIconButton-root': {
      color: colors.text.primary,
    },
    '& .MuiIconButton-root.Mui-disabled': {
      color: colors.text.disabled,
      opacity: 0.5,
    },
  },
};

export const dashboardStyles: DashboardStylesType = {

  layout: {
    notouch: {
      userSelect: 'none',
    },
    wrapper: {
      maxWidth: '1200px',
      width: '100%',
      mx: 'auto',
      paddingTop: 14,
      flexDirection: { xs: 'column', md: 'row' },
    },
    sidebar: {
      maxWidth: { xs: '100%', md: '235px' },
      minWidth: { xs: '100%', md: '235px' },
      width: { xs: '100%', md: '235px' },
      background: 'transparent',
      border: 'none',
    },
  },

  statCard: baseStatCard,

  discord: {
    button: {
      backgroundColor: '#5865F2',
      '&:hover': {
        backgroundColor: '#4752C4',
      },
    },
    statCard: {
      ...baseStatCard,
      p: 2,
    },
    icon: {
      color: 'rgba(255, 255, 255, 0.7)',
      mb: 1,
      fontSize: 32,
    },
    header: {
      mb: 3,
      display: 'flex',
      alignItems: 'center',
      gap: 2,
    },
    notConnected: {
      textAlign: 'center',
      py: 4,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: 2,
    },
    callback: {
      container: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '200px',
      },
    },
  },

  overview: {
    statCard: baseStatCard,
  },

  sessions: {
    deviceIcon: {
      color: colors.text.secondary,
    },
    statusChip: (isActive: boolean) => ({
      fontWeight: 500,
      minWidth: '90px',
      justifyContent: 'center',
      ...((!isActive) && {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        color: colors.text.secondary,
      }),
    }),
  },

  typography: {
    header: {
      color: colors.text.primary,
      fontWeight: 500,
    },
    subheader: {
      color: colors.text.primary,
      mb: 3,
    },
    body: {
      color: colors.text.secondary,
    },
  },

  buttons: {
    primary: {
      backgroundColor: colors.primary.main,
      color: colors.text.primary,
      '&:hover': {
        backgroundColor: colors.primary.dark,
      },
    },
    success: {
      backgroundColor: colors.success.main,
      color: colors.text.primary,
      '&:hover': {
        backgroundColor: colors.success.dark,
      },
    },
    cancel: {
      color: colors.text.primary,
      '&:hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
      },
    },
    danger: {
      color: colors.text.primary,
      backgroundColor: colors.error.main,
      '&:hover': {
        backgroundColor: colors.error.dark,
      },
    },
  },

  table: {
    container: {
      borderTopLeftRadius: 0,
      borderTopRightRadius: 0,
      mt: '-1px',
      background: colors.glass.background,
      backdropFilter: 'blur(10px)',
      borderRadius: '0 0 16px 16px',
      border: `1px solid ${colors.glass.border}`,
      '& .MuiTableCell-root': {
        borderBottom: `1px solid ${colors.glass.border}`,
        paddingTop: 0.5,
        paddingBottom: 0.5,
      },
      '& .MuiTableRow-root:hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
      },
      '& .MuiTableBody-root .MuiTableRow-root:last-child .MuiTableCell-root': {
        borderBottom: 'none',
      }
    },
    cell: {
      header: {
        userSelect: 'none',
        color: colors.text.secondary,
        fontWeight: 500,
        fontSize: '0.875rem',
        borderBottom: `1px solid ${colors.glass.border}`,
      },
      body: {
        color: colors.text.primary,
        fontWeight: 500,
      },
    },
    chip: {
      success: {
        backgroundColor: colors.success.main,
        color: colors.text.primary
      },
      warning: {
        backgroundColor: colors.warning.main,
        color: colors.text.primary
      },
      error: {
        backgroundColor: colors.error.main,
        color: colors.text.primary
      },
      default: {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        color: colors.text.secondary
      },
      base: {
        fontWeight: 500,
        minWidth: '90px',
        justifyContent: 'center',
      }
    },
    header: {
      wrapper: {
        userSelect: 'none',
        background: colors.glass.background,
        color: colors.text.primary,
        p: 2,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
        backdropFilter: 'blur(10px)',
      }
    },
    pagination: paginationStyles,
  },

  contentPaper: {
    p: 4,
    background: colors.glass.background,
    backdropFilter: 'blur(10px)',
    borderRadius: 4,
    border: `1px solid ${colors.glass.border}`,
  },

  menuSection: {
    wrapper: {
      '&:not(:last-child)': {
        mb: 3,
      }
    },
    header: {
      background: colors.glass.background,
      color: colors.text.primary,
      p: 2,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
      backdropFilter: 'blur(10px)',
      fontWeight: 500,
      fontSize: '1.125rem',
    },
    list: {
      p: 1,
      background: colors.glass.background,
      backdropFilter: 'blur(10px)',
      borderTopLeftRadius: 0,
      borderTopRightRadius: 0,
      borderBottomLeftRadius: 16,
      borderBottomRightRadius: 16,
      border: `1px solid ${colors.glass.border}`,
      mt: '-1px',
    }
  },

  menuItem: (isActive: boolean) => ({
    borderRadius: 4,
    mb: 1,
    backgroundColor: isActive ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
    color: isActive ? colors.text.primary : colors.text.secondary,
    '&:hover': {
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
    },
    cursor: 'pointer',
    transition: transitions.default,
  }),

  menuItemIcon: {
    color: 'inherit',
    minWidth: '40px',
  },

  security: {
    passwordMeter: {
      wrapper: {
        mt: 1,
        display: 'flex',
        flexDirection: 'column',
        gap: 1,
      },
      segments: {
        display: 'flex',
        gap: 0.5,
        mb: 1,
      },
      segment: (active: boolean, color: string) => ({
        height: '4px',
        flex: 1,
        bgcolor: active ? color : 'rgba(255, 255, 255, 0.2)',
        transition: transitions.default,
      }),
      helper: {
        color: colors.text.secondary,
        fontSize: '0.75rem',
      }
    },
    twoFactor: {
      wrapper: {
        display: 'flex',
        flexDirection: 'column',
        gap: 3,
      },
      switch: {
        '& .MuiSwitch-switchBase': {
          '&.Mui-checked': {
            color: colors.primary.main,
          },
        },
      },
      label: {
        color: colors.text.primary,
        '& .MuiFormControlLabel-label': {
          color: colors.text.primary,
          fontWeight: 500,
        },
      }
    }
  },

  chart: {
    wrapper: {
      p: 2,
      height: 400,
    },
    toggleGroup: {
      background: colors.glass.background,
      backdropFilter: 'blur(10px)',
    },
    toggleButton: {
      color: colors.text.secondary,
      borderColor: 'rgba(0, 0, 0, 0.05)',
      '&.Mui-selected': {
        color: colors.text.primary,
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
      },
      '&:hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
      },
    },
    margin: { 
      top: 40,
      bottom: 30,
      left: 50,
      right: 0
    },
    xAxis: {
      tickLabel: (timeRange: string) => ({
        fill: colors.text.primary,
        angle: timeRange === 'month' ? 315 : 0,
        textAnchor: timeRange === 'month' ? 'end' : 'middle',
      }),
    },
    yAxis: {
      label: {
        fill: colors.text.primary,
      },
      tickLabel: {
        fill: colors.text.primary,
      },
    },
    styles: {
      '& .MuiChartsAxis-line': {
        stroke: colors.text.primary,
        strokeWidth: 0.5,
      },
      '& .MuiChartsAxis-tick': {
        stroke: colors.text.primary,
        strokeWidth: 1,
      },
      '& .MuiChartsAxis-grid': {
        stroke: 'rgba(255, 255, 255, 0.1)',
        strokeDasharray: '2 4',
      },
    },
    slotProps: {
      loadingOverlay: { message: 'Data should be available soon.' },
      noDataOverlay: { message: 'Select some data to display.' },
      legend: {
        position: { vertical: 'top', horizontal: 'end' },
        padding: 1,
        labelStyle: {
          fill: colors.text.secondary,
        },
      },
      popper: {
        sx: {
          '& .MuiChartsTooltip-paper': {
            background: 'rgba(0, 0, 0, 0.7)',
            backdropFilter: 'blur(10px)',
          },
          '& thead': {
            '& .MuiTypography-root': {
              color: colors.text.secondary,
            },
          },
          '& .MuiChartsTooltip-table': {
            background: 'transparent',
            p: 1.5,
            boxShadow: '0 4px 20px rgba(0, 0, 0, .87)',
          },
          '& .MuiChartsTooltip-cell': {
            background: 'transparent',
            color: colors.text.secondary,
            padding: '4px 8px',
            border: 'none',
          },
          '& .MuiChartsTooltip-valueCell': {
            background: 'transparent',
            color: colors.text.primary,
            fontWeight: 500,
            border: 'none',
          },
          '& .MuiChartsTooltip-mark': {
            width: '10px',
            height: '10px',
            borderRadius: 4,
            borderColor: 'transparent',
          },
        },
      },
    },
  },

  scripter: {
    metricCard: {
      p: 3,
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: 1,
    },
    trendBox: (positive: boolean) => ({
      display: 'flex',
      alignItems: 'center',
      gap: 1,
      color: positive ? 'success.main' : 'error.main',
    }),
    progressBar: (trend: number) => ({
      mt: 2,
      height: 6,
      borderRadius: 3,
      backgroundColor: 'grey.100',
      '& .MuiLinearProgress-bar': {
        backgroundColor: trend >= 0 ? 'success.main' : 'error.main',
      },
    }),
    statusChip: (status: 'active' | 'inactive') => ({
      display: 'inline-flex',
      alignItems: 'center',
      px: 1,
      py: 0.5,
      borderRadius: 1,
      typography: 'caption',
      textTransform: 'capitalize',
      backgroundColor: status === 'active' ? 'success.light' : 'grey.300',
      color: status === 'active' ? 'success.dark' : 'grey.700',
    }),
  },

  textField: {
    marginBottom: 2.5,
    width: '100%',
    '& .MuiOutlinedInput-root': {
      color: colors.text.primary,
      '& fieldset': {
        borderColor: 'rgba(255, 165, 0, 0.5)',
      },
      '&:hover fieldset': {
        borderColor: 'rgba(255, 165, 0, 0.7)',
      },
      '&.Mui-focused fieldset': {
        borderColor: colors.primary.main,
      },
    },
    '& .MuiInputLabel-root': {
      color: colors.text.secondary,
      '&.Mui-focused': {
        color: colors.primary.main,
      },
    },
  },

  menu: sharedMenuStyles,

  headerWithSearch: {
    wrapper: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      background: colors.glass.background,
      backdropFilter: 'blur(10px)',
      borderRadius: '16px 16px 0 0',
      border: `1px solid ${colors.glass.border}`,
      borderBottom: 'none',
      p: 2,
    },
    title: {
      color: colors.text.primary,
      userSelect: 'none',
    },
    search: {
      flex: 1,
      maxWidth: '200px',
      ...inputStyles.textField,
    },
  },
}; 