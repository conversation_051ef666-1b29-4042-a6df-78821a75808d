import { SxProps } from '@mui/material';
import { colors, transitions, gradients } from '../constants';

export const landingStyles = {
  wrapper: {
    notouch: {
      userSelect: 'none',
    } as SxProps,
  },
  future: {
    section: {
      position: 'relative',
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
    } as SxProps,

    card: {
      background: colors.glass.background,
      backdropFilter: 'blur(10px)',
      border: `1px solid ${colors.glass.border}`,
      borderRadius: 2,
      transition: transitions.default,
      p: 3,
      height: '100%',
      '&:hover': {
        background: colors.glass.hover,
        transform: 'translateY(-5px)',
      },
    } as SxProps,

    gradientText: {
      background: gradients.primary,
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      backgroundClip: 'text',
      color: 'transparent',
    } as SxProps,
  },

  hero: {
    container: {
      position: 'relative',
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: colors.text.primary,
      padding: '2rem 0',
      overflow: 'hidden',
    } as SxProps,

    glass: {
      background: colors.glass.background,
      backdropFilter: 'blur(10px)',
      border: `1px solid ${colors.glass.border}`,
      borderRadius: 4,
      padding: 3,
      color: colors.text.primary,
    } as SxProps,

    title: {
      fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
      fontWeight: 800,
      marginBottom: 3,
      textAlign: { xs: 'center', lg: 'left' },
      color: colors.text.primary,
      lineHeight: 1.1,
      letterSpacing: '-0.02em',
    } as SxProps,

    subtitle: {
      fontSize: { xs: '1.125rem', md: '1.25rem', lg: '1.375rem' },
      marginBottom: 4,
      textAlign: { xs: 'center', lg: 'left' },
      color: colors.text.secondary,
      lineHeight: 1.4,
      maxWidth: { lg: '500px' },
    } as SxProps,

    description: {
      fontSize: { xs: '1rem', md: '1.125rem' },
      marginBottom: 2,
      textAlign: 'center',
      color: colors.text.secondary,
    } as SxProps,

    image: {
      width: '100%',
      height: 'auto',
      boxShadow: '0 20px 40px rgba(0, 0, 0, 0.4)',
      borderRadius: 2,
      marginTop: { xs: 3, lg: 0 },
      transition: 'all 0.3s ease',
      '&:hover': {
        transform: 'translateY(-5px) scale(1.02)',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.5)',
      },
    } as SxProps,

    carousel: {
      container: {
        position: 'relative',
        height: { xs: '180px', md: '150px' },
        display: 'flex',
        alignItems: 'center',
        width: '100%',
        marginTop: { xs: 2, md: 4 },
      } as SxProps,

      button: {
        position: 'absolute',
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        color: colors.text.primary,
        backdropFilter: 'blur(4px)',
        transition: transitions.default,
        '&:hover': {
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
        },
        '&:active': {
          backgroundColor: 'rgba(255, 255, 255, 0.3)',
        },
        '& svg': {
          fontSize: '24px',
        },
        left: { xs: 0, md: 20 },
        '&:last-of-type': {
          left: 'auto',
          right: { xs: 0, md: 20 },
        },
      } as SxProps,

      title: {
        fontSize: { xs: '1.5rem', md: '1.75rem', lg: '2rem' },
        marginBottom: 2,
        color: colors.text.primary,
        fontWeight: 700,
        letterSpacing: '-0.01em',
      } as SxProps,

      description: {
        fontSize: { xs: '1rem', md: '1.125rem' },
        color: colors.text.secondary,
        lineHeight: 1.6,
        opacity: 0.9,
      } as SxProps,
    },
  },

  howItWorks: {
    section: {
      position: 'relative',
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      color: colors.text.primary,
      py: 8,
    } as SxProps,

    title: {
      mb: 6,
      color: colors.primary.main,
      textAlign: 'center',
    } as SxProps,

    stepsContainer: {
      display: 'flex',
      flexDirection: 'column',
      gap: 6,
      my: 8,
    } as SxProps,

    step: {
      display: 'flex',
      alignItems: 'center',
      gap: 3,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      padding: 3,
      borderRadius: 2,
    } as SxProps,

    stepNumber: {
      color: colors.primary.main,
      opacity: 0.7,
    } as SxProps,

    stepTitle: {
      color: colors.text.primary,
      mb: 1,
    } as SxProps,

    stepDescription: {
      color: colors.text.secondary,
    } as SxProps,

    cta: {
      textAlign: 'center',
      mt: 10,
    } as SxProps,

    ctaText: {
      color: colors.text.primary,
      mb: 3,
    } as SxProps,

    ctaButton: {
      backgroundColor: colors.primary.main,
      color: '#000',
      '&:hover': {
        backgroundColor: colors.primary.dark,
      },
    } as SxProps,
  },
}; 