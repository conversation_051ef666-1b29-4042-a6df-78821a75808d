// API DTOs - copied from src/auth/dto/ for type safety
export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  roles: string[];
  emailVerified: boolean;
}

export interface AuthResponse {
  user: UserProfile;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType?: string;
}

export interface TokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  twoFactorCode?: string;
  deviceInfo?: object;
  platform?: string;
  location?: object;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name?: string;
}

// User profile response from /users/me endpoint
export interface UserDto {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  bio?: string;
  roles: string[];
  twoFactorEnabled: boolean;
  emailVerified: boolean;
  status: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Use AuthResponse as LoginResponse for compatibility
export type LoginResponse = AuthResponse;

// Legacy interface for compatibility (will be removed)
export interface User {
    id: string;
    email: string;
    username: string;
    tenant_id: string;
    roles: string[];
    created_at: string;
    updated_at: string;
  }
  
  export interface ErrorResponse {
    message: string;
    errors?: Record<string, string>;
  }
  
  export interface ScriptVersion {
    version: string;
    publishedAt: string;
    changelog: string;
  }
  
  export interface ScriptProject {
    id: string;
    name: string;
    description: string;
    currentVersion: string;
    publishedAt: string;
    status: 'active' | 'inactive';
    versions: ScriptVersion[];
  }
  
  export interface CreateScriptProjectDto {
    name: string;
    description: string;
  }
  
  export interface UpdateScriptProjectDto {
    name?: string;
    description?: string;
  }
  
  export interface PublishVersionDto {
    version: string;
    changelog: string;
    file: File;
  }
  
  export interface ScripterMetric {
    label: string;
    value: string;
    trend: number;
    compareType: 'day' | 'daybefore' | 'week' | 'month';
  }
  
  export interface ScripterService {
    id: string;
    name: string;
    price: string;
    soldCount: number;
    revenue: string;
    status: 'active' | 'inactive';
  }
  
  export interface ScripterOverviewData {
    metrics: ScripterMetric[];
    services: ScripterService[];
  }
  
  export interface Order {
    id: string;
    date: string;
    description: string;
    amount: number;
    status: 'completed' | 'pending' | 'failed';
  }
  
  export interface UserSession {
    id: string;
    deviceType: 'browser' | 'desktop' | 'mobile';
    deviceName: string;
    browser?: string;
    operatingSystem?: string;
    location: string;
    lastActive: string;
    isActive: boolean;
  }
  
  export interface Sale {
    id: string;
    date: string;
    buyerUsername: string;
    scriptName: string;
    instanceCount: number;
    duration: string;
    status: 'completed' | 'refunded';
    amount: number;
  }
  
  export interface AdminUser {
    id: string;
    username: string;
    email: string;
    role: 'admin' | 'moderator' | 'user' | 'scripter' | 'unverified' | 'banned';
    isVerified: boolean;
    has2FA: boolean;
    clientCount: number;
    maxClients: number;
    registrationDate: string;
    lastLoginDate: string;
    status: 'active' | 'inactive' | 'banned';
  }
  
  export interface UserLoginHistory {
    id: string;
    timestamp: string;
    ipAddress: string;
    userAgent: string;
    isActiveSession: boolean;
    location?: string;
  }
  
  export interface UserActivity {
    id: string;
    timestamp: string;
    actionType: 'page_view' | 'password_change' | '2fa_enabled' | '2fa_disabled' | 'client_login' | 'script_start' | 'script_stop';
    details: string;
  }
  
  export interface UserPurchase {
    id: string;
    transactionId: string;
    date: string;
    productName: string;
    amount: number;
    status: 'completed' | 'refunded' | 'pending';
  }
  
  export interface UserClient {
    id: string;
    operatingSystem: string;
    scriptName: string;
    scriptVersion: string;
    ipAddress: string;
    startTime: string;
    runTime: string;
    lastHeartbeat: string;
    isActive: boolean;
  }
  
  export interface UserDetails {
    loginHistory: UserLoginHistory[];
    activities: UserActivity[];
    purchases: UserPurchase[];
    clients: UserClient[];
  }
  
  export type UserRole = 'admin' | 'moderator' | 'user' | 'scripter' | 'unverified' | 'banned';
  
  export interface StoreItem {
    id: string;
    name: string;
    description: string;
    seller: {
      id: string;
      name: string;
    };
    price: number;
    image: string;
    updatedAt: string;
    categories: string[];
  }