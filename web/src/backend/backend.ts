import { logger } from "@/utils/logger";
import { mockDashboardData as mockDashboardOverview, MockDashboardData as MockDashboardOverview } from "./mock/dashboard/user/mock_overview";
import { ChartViewModel } from "./mock/dashboard/scripter/mock_chart";
import { mockScriptProjects } from './mock/dashboard/scripter/mock_projects';
import { 
  ScriptProject, 
  CreateScriptProjectDto, 
  UpdateScriptProjectDto, 
  PublishVersionDto 
} from './types';
import { mockScripterOverview } from './mock/dashboard/scripter/mock_overview';
import { mockOrders } from './mock/dashboard/user/mock_orders';
import { mockSessions } from './mock/dashboard/user/mock_sessions';
import { ScripterOverviewData } from '@backend/types';
import { Order } from '@backend/types';
import { UserSession } from '@backend/types';
import { Sale } from '@backend/types';
import { mockSales } from './mock/dashboard/scripter/mock_sales';
import { AdminUser, AuthResponse, UserDto } from '@backend/types';
import { mockUsers } from './mock/dashboard/admin/mock_users';

export const CURRENT_BRANCH = import.meta.env.VITE_CURRENT_BRANCH;
const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

export interface TableHeader {
  id: string;
  label: string;
  align: 'left' | 'right' | 'center' | 'inherit' | 'justify';
}

export type TimeRange = 'week' | 'month' | 'year'; 

export interface Subscription {
  id: string;
  add_date: string;
  expire_date: string;
  status: string;
  amount: number;
  name: string;
  author?: string;
  categories?: string;
  version?: string;
  lastUpdate?: string;
}

export interface DashboardStats {
  lastLoginDate: string;
  lastLoginTime: string;
  scriptsRunning: number;
  scriptsMax: number;
}

class ApiClient {
  private access_token: string | null = null;
  private defaultErrorMessages = {
    NETWORK_ERROR: 'Unable to connect to the server.',
    SERVER_ERROR: 'Something went wrong on our end.\nPlease try again later.',
    UNAUTHORIZED: 'Your session has expired.\nPlease try again later.',
    NOT_FOUND: 'The requested resource was not found.',
    VALIDATION_ERROR: 'Please check your input and try again.',
  };

  setToken(access_token: string) {
    this.access_token = access_token;
  }

  clearToken() {
    this.access_token = null;
  }

  async request(endpoint: string, method: string, data?: any) {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.access_token) {
      headers['Authorization'] = `Bearer ${this.access_token}`;
    }

    const config: RequestInit = {
      method,
      headers,
    };

    if (data) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(`${API_URL}${endpoint}`, config);
      
      const contentType = response.headers.get('content-type');
      let responseData;
      
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        const text = await response.text();
        logger.error(`Non JSON API Response:`, text);
        responseData = { message: this.defaultErrorMessages.SERVER_ERROR };
      }

      if (!response.ok) {
        const errorMessage = this.getErrorMessage(response.status, responseData);
        logger.error(`API Request NOT OK: ${endpoint}`, { error: errorMessage });
        throw new Error(errorMessage);
      }

      return responseData;
    } catch (error) {
      const errorMessage = this.handleError(error);
      logger.error(`API Request Failed: ${endpoint}`, { error: errorMessage });
      throw new Error(errorMessage);
    }
  }

  private getErrorMessage(status: number, responseData: any): string {
    switch (status) {
      case 400:
        return responseData?.message || this.defaultErrorMessages.VALIDATION_ERROR;
      case 401:
        return responseData?.message || this.defaultErrorMessages.UNAUTHORIZED;
      case 404:
        return responseData?.message || this.defaultErrorMessages.NOT_FOUND;
      case 422:
        return responseData?.message || this.defaultErrorMessages.VALIDATION_ERROR;
      case 500:
      case 502:
        return this.defaultErrorMessages.SERVER_ERROR;
      default:
        return responseData?.message || this.defaultErrorMessages.SERVER_ERROR;
    }
  }

  private handleError(error: any): string {
    if (error.message === 'Failed to fetch') {
      return this.defaultErrorMessages.NETWORK_ERROR;
    }
    if (error.message === 'Invalid credentials or tenant') {
      return 'Invalid credentials.';
    }
    if (error.message.includes('ERROR:')) {
      return this.defaultErrorMessages.SERVER_ERROR;
    }
    return error.message || this.defaultErrorMessages.SERVER_ERROR;
  }

  get(endpoint: string) {
    return this.request(endpoint, 'GET');
  }

  post(endpoint: string, data: any) {
    return this.request(endpoint, 'POST', data);
  }

  put(endpoint: string, data: any) {
    return this.request(endpoint, 'PUT', data);
  }

  delete(endpoint: string) {
    return this.request(endpoint, 'DELETE');
  }
}

export const api = new ApiClient();

export const auth = {
  login: (email: string, password: string): Promise<AuthResponse> =>
    api.post('/auth/login', { email, password }),
  register: (email: string, password: string, name: string): Promise<AuthResponse> =>
    api.post('/auth/register', { email, password, name }),
  logout: () => api.post('/auth/logout', {}),
  forgotPassword: (email: string) =>
    api.post('/auth/forgot-password', { email }),
  resetPassword: (newPassword: string, token: string) =>
    api.post('/auth/reset-password', { new_password: newPassword, token }),
  resendEmailVerification: (email: string) =>
    api.post('/auth/resend-verification', { email }),
  verify2FA: (userId: string, code: string) =>
    api.post('/auth/verify-2fa', { user_id: userId, code }),
};

export const twoFactor = {
  setup: () => api.post('/users/me/2fa/setup', {}),
  verifySetup: (code: string) => api.post('/users/me/2fa/verify-setup', { code }),
  disable: (password: string, code?: string) =>
    api.post('/users/me/2fa/disable', { password, code }),
};

export const account = {
  get: (): Promise<UserDto> => api.get('/users/me'),
  changePassword: (userId: string, oldPassword: string, newPassword: string) =>
    api.post('/users/change-password', { user_id: userId, old_password: oldPassword, new_password: newPassword }),
  enable2FA: (verificationCode: string) =>
    api.post('/users/enable-2fa', { verification_code: verificationCode }),
  disable2FA: (verificationCode: string) =>
    api.post('/users/disable-2fa', { verification_code: verificationCode }),
};

export const dashboard = {
  //User Dashboard
  getStats: () => api.get('/dashboard/stats'),
  getDashboardOverview: (): MockDashboardOverview => mockDashboardOverview,
  getPremiumScripts: () => api.get('/dashboard/scripts/premium'),
  getCommunityScripts: () => api.get('/dashboard/scripts/community'),
  getOrders: (): Promise<Order[]> => Promise.resolve(mockOrders),
  getSessions: (): Promise<UserSession[]> => Promise.resolve(mockSessions),

  // Scripter Dashboard
  getScripterChartDataREAL: () => api.get('/dashboard/scripter/sales-chart'),
  getScripterChartData: (): ChartViewModel => ChartViewModel.getInstance(),
  getScripterOverview: (): Promise<ScripterOverviewData> => Promise.resolve(mockScripterOverview),
};

export const scripter = {
  // Projects
  getProjects: (): Promise<ScriptProject[]> => {
    // When API is ready: return api.get('/scripter/projects');
    return Promise.resolve(mockScriptProjects);
  },

  createProject: (data: CreateScriptProjectDto): Promise<ScriptProject> => {
    // When API is ready: return api.post('/scripter/projects', data);
    const newProject: ScriptProject = {
      id: (mockScriptProjects.length + 1).toString(),
      name: data.name,
      description: data.description,
      currentVersion: '1.0.0',
      publishedAt: new Date().toISOString().split('T')[0],
      status: 'active',
      versions: [],
    };
    mockScriptProjects.push(newProject);
    return Promise.resolve(newProject);
  },

  updateProject: (id: string, data: UpdateScriptProjectDto): Promise<ScriptProject> => {
    // When API is ready: return api.put(`/scripter/projects/${id}`, data);
    const project = mockScriptProjects.find(p => p.id === id);
    if (!project) {
      return Promise.reject(new Error('Project not found'));
    }
    Object.assign(project, data);
    return Promise.resolve(project);
  },

  publishVersion: (id: string, data: PublishVersionDto): Promise<ScriptProject> => {
    // When API is ready: return api.post(`/scripter/projects/${id}/versions`, data);
    const project = mockScriptProjects.find(p => p.id === id);
    if (!project) {
      return Promise.reject(new Error('Project not found'));
    }
    const newVersion = {
      version: data.version,
      publishedAt: new Date().toISOString().split('T')[0],
      changelog: data.changelog,
    };
    project.versions.unshift(newVersion);
    project.currentVersion = data.version;
    project.publishedAt = newVersion.publishedAt;
    return Promise.resolve(project);
  },

  rollbackVersion: (id: string, version: string): Promise<ScriptProject> => {
    // When API is ready: return api.post(`/scripter/projects/${id}/rollback`, { version });
    const project = mockScriptProjects.find(p => p.id === id);
    if (!project) {
      return Promise.reject(new Error('Project not found'));
    }
    const targetVersion = project.versions.find(v => v.version === version);
    if (!targetVersion) {
      return Promise.reject(new Error('Version not found'));
    }
    project.currentVersion = version;
    project.publishedAt = targetVersion.publishedAt;
    return Promise.resolve(project);
  },

  getSales: (): Promise<Sale[]> => Promise.resolve(mockSales),
};

export const admin = {
  getUsers: async (): Promise<AdminUser[]> => {
    // In a real app, this would be an API call
    return mockUsers;
  }
};