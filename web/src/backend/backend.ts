import { Client, Account, Databases, Teams } from "appwrite";
import { logger } from "../utils/logger";

const client = new Client();

const BASE_URL = import.meta.env.VITE_BASE_URL;
const CURRENT_BRANCH = import.meta.env.VITE_CURRENT_BRANCH;
const APPWRITE_ENDPOINT = import.meta.env.VITE_APPWRITE_ENDPOINT;
const APPWRITE_PROJECT_ID = import.meta.env.VITE_APPWRITE_PROJECT_ID;

logger.log("BASE_URL:", BASE_URL);
logger.log("CURRENT_BRANCH:", CURRENT_BRANCH);
logger.log("APPWRITE_ENDPOINT:", APPWRITE_ENDPOINT);
logger.log("APPWRITE_PROJECT_ID:", APPWRITE_PROJECT_ID);

if (CURRENT_BRANCH === "dev") {
  logger.log("Using DEV branch");
} else if (CURRENT_BRANCH === "staging") {
  logger.log("Using STAGING branch");
} else if (CURRENT_BRANCH === "production") {
  logger.log("Using PRODUCTION branch");
} else {
  logger.error("Invalid branch selected");
}

if (!APPWRITE_ENDPOINT) {
  logger.error(
    "Endpoint is not defined. Please check your environment variables."
  );
  throw new Error("Endpoint configuration is missing.");
}

if (!APPWRITE_PROJECT_ID) {
  logger.error(
    "Appwrite Project ID is not defined. Please check your environment variables."
  );
  throw new Error("Appwrite Project ID is missing.");
}

try {
  client.setEndpoint(`${APPWRITE_ENDPOINT}/v1`).setProject(APPWRITE_PROJECT_ID);
  logger.log("Appwrite client configured with endpoint:", APPWRITE_ENDPOINT);
} catch (error) {
  logger.error("Failed to configure Appwrite client:", error);
  throw error;
}

const account = new Account(client);
const databases = new Databases(client);
const teams = new Teams(client);

export { client, account, databases, teams, CURRENT_BRANCH };
