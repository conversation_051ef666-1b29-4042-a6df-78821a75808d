import debug from "debug";

const log = debug("rsglider:log");
const error = debug("rsglider:error");

// Enable logging if DEBUG environment variable is set
if (import.meta.env.VITE_DEBUG === "true") {
  debug.enable("rsglider:*");
}

export const logger = {
  log: (message: string, ...args: unknown[]) => log(message, ...args),
  error: (message: string, ...args: unknown[]) => error(message, ...args),
};
