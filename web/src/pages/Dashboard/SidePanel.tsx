import React from 'react';
import { Box, List, ListItem, ListItemText } from '@features/_global/Index';

const SidePanel: React.FC = () => {
  return (
    <Box
      sx={{
        width: 250,
        bgcolor: 'background.paper',
        height: '100vh',
        position: 'fixed',
        top: 0,
        left: 0,
        display: 'flex',
        flexDirection: 'column',
        p: 2,
      }}
    >
      <List>
        <ListItem to="/dashboard">
          <ListItemText primary="Dashboard" />
        </ListItem>
        <ListItem to="/profile">
          <ListItemText primary="Profile" />
        </ListItem>
        <ListItem to="/settings">
          <ListItemText primary="Settings" />
        </ListItem>
      </List>
    </Box>
  );
};

export default SidePanel;