import React from 'react';
import {  Typography, Box } from '@mui/material';
import Button from '@features/_global/Button';

interface DashboardLayoutProps {
  title: string;
  description: string;
  buttonText: string;
  onButtonClick: () => void;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  title,
  description,
  buttonText,
  onButtonClick,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'flex-start',
        height: '100%',
        textAlign: 'center',
        bgcolor: 'background.default',
        color: 'text.primary',
        p: 3,
      }}
    >
      <Typography variant="h4" component="h1" gutterBottom>
        {title}
      </Typography>
      <Typography variant="body1" gutterBottom>
        {description}
      </Typography>
      <Button variantType="primary" onClick={onButtonClick}>
        {buttonText}
      </Button>
    </Box>
  );
};

export default DashboardLayout;