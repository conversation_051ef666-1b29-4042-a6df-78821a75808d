import React from 'react';
import { App<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography, Button, Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import logo from '@assets/logo.png';
import { Theme } from '@mui/material/styles';
import { useAuth } from '@features/auth/hooks/useAuth';
import { CURRENT_BRANCH } from '@backend/backend';
import { BranchChip } from '@features/_global/Navbar/styles/NavbarStyles';

const TopHeader: React.FC = () => {
  const navigate = useNavigate();
  const { logout } = useAuth();

  const handleLogout = async () => {
    console.log('Logout button clicked');
    try {
      await logout();
      console.log('Logout successful, navigating to login page');
      navigate('/auth/login');
    } catch (error) {
      console.error('Failed to logout:', error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
    }
  };

  return (
    <AppBar position="fixed" sx={{ zIndex: (theme: Theme) => theme.zIndex.drawer + 1 }}>
      <Toolbar>
        <Box sx={{ display: 'flex', alignItems: 'center', position: 'relative' }}>
          <img src={logo} alt="RSGlider Logo" style={{ width: '40px', marginRight: '10px' }} />
          {CURRENT_BRANCH && CURRENT_BRANCH !== 'production' && (
            <BranchChip>{CURRENT_BRANCH}</BranchChip>
          )}
          <Typography variant="h6" component="div">
            rsglider
          </Typography>
        </Box>
        <Box sx={{ flexGrow: 1 }} />
        <Button color="inherit" onClick={handleLogout}>
          Logout
        </Button>
      </Toolbar>
    </AppBar>
  );
};

export default TopHeader;