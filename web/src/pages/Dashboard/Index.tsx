import React, { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@features/auth/hooks/useAuth';
import Loader from '@features/_global/Loader';
import LoadingScreen from '@features/LandingPage/components/LoadingScreen';
import TopHeader from './Header';
import SidePanel from './SidePanel';

const Dashboard: React.FC = () => {
  const { role, isLoading, isAuthenticated, checkAuth } = useAuth();
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      await checkAuth();
      setIsInitialLoad(false);
    };
    initAuth();
  }, [checkAuth]);

  if (isInitialLoad || isLoading) {
    return <LoadingScreen isLoading={true} />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/auth/login" replace />;
  }

  let DashboardComponent;
  switch (role) {
    case 'admin':
      DashboardComponent = React.lazy(() => import('./Staff/Admin'));
      break;
    case 'moderator':
      DashboardComponent = React.lazy(() => import('./Staff/Moderator'));
      break;
    case 'developer':
      DashboardComponent = React.lazy(() => import('./Developer/Developer'));
      break;
    default:
      DashboardComponent = React.lazy(() => import('./User/User'));
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      <TopHeader />
      <Box sx={{ display: 'flex', flexGrow: 1 }}>
        <SidePanel />
        <Box component="main" sx={{ flexGrow: 1, p: 3, marginLeft: '250px', marginTop: '64px' }}>
          <React.Suspense fallback={<Loader />}>
            <DashboardComponent />
          </React.Suspense>
        </Box>
      </Box>
    </Box>
  );
};

export default Dashboard;