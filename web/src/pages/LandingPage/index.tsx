import { Box } from '@mui/material';
import LoadingScreen from '@/features/_global/LoadingScreen';
import HeroSection from '@features/LandingPage/components/HeroSection';
import HowItWorksSection from '@features/LandingPage/components/HowItWorksSection';
import WhyChooseSection from '@features/LandingPage/components/WhyChooseSection';
import FutureSection from '@features/LandingPage/components/FutureSection';
import Footer from '@features/LandingPage/components/Footer';
import { useAuth } from '@features/auth/hooks/useAuth';
import { motion } from 'framer-motion';
import { landingStyles } from '@/styles/components/landing';


const LandingPage: React.FC = () => {
  const { isLoading: authLoading } = useAuth();

  if (authLoading) {
    return <LoadingScreen isLoading={true} />;
  }

  return (
    <Box sx={landingStyles.wrapper.notouch}>
      <div id="top" style={{ position: 'absolute', top: 0 }} />
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <HeroSection />
      </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Box id="how-it-works-section">
          <HowItWorksSection />
        </Box>
      </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Box id="why-choose-section">
          <WhyChooseSection />
        </Box>
      </motion.div>
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
      >
        <Box id="future-section">
          <FutureSection />
        </Box>
      </motion.div>

      {/* Footer */}
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        <Footer />
      </motion.div>

    </Box>
  );
};

export default LandingPage;