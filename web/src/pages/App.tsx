import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import { CssBaseline, ThemeProvider, Box } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import LandingPage from '@pages/LandingPage';
import Auth from '@pages/Auth';
import Dashboard from '@pages/Dashboard/Index';
import ErrorBoundary from '@features/_global/ErrorBoundary';
import ProtectedRoute from '@features/_global/components/ProtectedRoute';
import { AuthProvider } from '@features/auth/context/AuthContext';
import '../styles/tailwind.css';
import Overview from '@features/dashboard/components/user/Overview';
import OrderHistory from '@features/dashboard/components/user/OrderHistory';
import AccountSecurity from '@features/dashboard/components/user/AccountSecurity/index';
import SessionManager from '@features/dashboard/components/user/SessionManager';
import ScripterDashboard from '@features/dashboard/components/scripter/Overview';
import ChartView from '@features/dashboard/components/scripter/ChartView';
import DiscordCallback from '@features/dashboard/components/community/DiscordCallback';
import { pageStyles } from '@styles/components/pages';
import Navbar from '@/features/_global/Navbar';
import ScriptProjects from '@features/dashboard/components/scripter/Projects';
import TableView from '@features/dashboard/components/scripter/TableView';
import SalesHistory from '@features/dashboard/components/scripter/SalesHistory';
import Users from '@features/dashboard/components/admin/Users';
import Store from '@pages/Store/index';
import StoreItemDetails from '@features/store/components/StoreItemDetails';

const theme = createTheme();

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/" element={<LandingPage />} />
      <Route path="/auth/*" element={<Auth />} />
      <Route path="/docs/*" element={<Navigate to="/docs" replace  />} />
      <Route path="/store" element={<Store />} />
      <Route path="/store/script/:id" element={<StoreItemDetails />} />
      
      {/* Protected Routes */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        }
      >
        <Route index element={<Overview />} />
        <Route path="orders" element={<OrderHistory />} />
        <Route path="security" element={<AccountSecurity />} />
        <Route path="sessions" element={<SessionManager />} />
        <Route path="scripter/dashboard" element={<ScripterDashboard />} />
        <Route path="scripter/chart" element={<ChartView />} />
        <Route path="scripter/table" element={<TableView />} />
        <Route path="scripter/sales" element={<SalesHistory />} />
        <Route path="scripter/projects" element={<ScriptProjects />} />
        <Route path="community/discord" element={<DiscordCallback />} />
        <Route path="discord/callback" element={<DiscordCallback />} />
        <Route 
          path="admin/users" 
          element={
            <ProtectedRoute>
              <Users />
            </ProtectedRoute>
          } 
        />
      </Route>

      {/* Fallback */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <AuthProvider>
            <Box id="main-container" sx={pageStyles.wrapper}>
              <Box sx={pageStyles.opacity}>
                <Navbar />
                <AppRoutes />
              </Box>
            </Box>
          </AuthProvider>
        </Router>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;