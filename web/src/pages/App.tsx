import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import { CssBaseline, ThemeProvider, Box } from '@mui/material';
import { createTheme } from '@mui/material/styles';
import LandingPage from '@global/LandingPage';
import Auth from '@global/Auth';
import Dashboard from '@pages/Dashboard/Index';
import ErrorBoundary from '@features/_global/ErrorBoundary';
import bg from '@assets/bg.png';
import { AuthProvider } from '@features/auth/context/AuthContext';
import { useAuth } from '@features/auth/hooks/useAuth';
import '../styles/tailwind.css';

const theme = createTheme();

const AppRoutes: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <Routes>
      <Route path="/" element={<LandingPage />} />
      <Route path="/auth/*" element={<Auth />} />
      <Route
        path="/dashboard"
        element={isAuthenticated ? <Dashboard /> : <Navigate to="/auth/login" />}
      />
      <Route path="*" element={<Navigate to="/auth/login" />} />
    </Routes>
  );
};

const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <AuthProvider>
            <Box
              sx={{
                backgroundImage: `url(${bg})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundAttachment: 'fixed',
                minHeight: '100vh',
                boxShadow: 'inset 0 0 10px rgba(0, 0, 0, 0.5)',
              }}
            >
              <AppRoutes />
            </Box>
          </AuthProvider>
        </Router>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;