import { Box } from '@mui/material';
import { useEffect } from 'react';
import Navbar from '@features/_global/Navbar';
import LoadingScreen from '@features/LandingPage/components/LoadingScreen';
import HeroSection from '@features/LandingPage/components/HeroSection';
import HowItWorksSection from '@features/LandingPage/components/HowItWorksSection';
import WhyChooseSection from '@features/LandingPage/components/WhyChooseSection';
import FutureSection from '@features/LandingPage/components/FutureSection';
import styled from '@emotion/styled';
import { useAuth } from '@features/auth/hooks/useAuth';
import { motion } from 'framer-motion';

const LandingPage: React.FC = () => {
  const { isLoading: authLoading } = useAuth();

  useEffect(() => {
    // Remove the automatic redirection to dashboard
  }, []);

  if (authLoading) {
    return <LoadingScreen isLoading={true} />;
  }

  return (
    <PageWrapper theme={{ backgroundImage: 'url(/assets/background.png)' }}>
      <ContentWrapper>
        <NavWrapper>
          <Navbar />
        </NavWrapper>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <HeroSection />
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Box id="how-it-works-section">
            <HowItWorksSection />
          </Box>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Box id="why-choose-section">
            <WhyChooseSection />
          </Box>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Box id="future-section">
            <FutureSection />
          </Box>
        </motion.div>
      </ContentWrapper>
    </PageWrapper>
  );
};

const PageWrapper = styled(Box)`
  position: relative;
  min-height: 100vh;
  background-image: url('/assets/background.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
`;

const NavWrapper = styled(Box)`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 3;
`;

const ContentWrapper = styled(Box)`
  position: relative;
  z-index: 2;
  background-color: rgba(0, 0, 0, 0.5);
  min-height: 100vh;
`;

export default LandingPage;