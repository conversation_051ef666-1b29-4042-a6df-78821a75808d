import { useEffect, useState } from 'react';
import { Box, CircularProgress } from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import Login from '@features/auth/components/Login';
import Register from '@features/auth/components/Register';
import VerifyEmail from '@features/auth/components/VerifyEmail';
import ForgotPassword from '@features/auth/components/ForgotPassword';
import ResetPassword from '@features/auth/components/ResetPassword';
import TwoFactorAuth from '@features/auth/components/TwoFactorAuth';
import styled from '@emotion/styled';
import { useAuth } from '@features/auth/hooks/useAuth';
import Navbar from '@features/_global/Navbar';

const Auth = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated, isLoading, checkAuth } = useAuth();
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  useEffect(() => {
    const verifyAuth = async () => {
      if (!initialCheckDone) {
        await checkAuth();
        setInitialCheckDone(true);
      }
    };
    verifyAuth();
  }, [checkAuth, initialCheckDone]);

  useEffect(() => {
    if (!isLoading && initialCheckDone) {
      if (isAuthenticated) {
        navigate('/dashboard');
      } else if (location.pathname === '/auth') {
        navigate('/auth/login');
      }
    }
  }, [isAuthenticated, isLoading, initialCheckDone, location.pathname, navigate]);

  if (isLoading || !initialCheckDone) {
    return <CircularProgress />;
  }

  const renderAuthComponent = () => {
    switch (location.pathname) {
      case '/auth/login':
        return <Login />;
      case '/auth/register':
        return <Register />;
      case '/auth/verify-email':
        return <VerifyEmail />;
      case '/auth/forgot-password':
        return <ForgotPassword />;
      case '/auth/reset-password':
        return <ResetPassword />;
      case '/auth/2fa':
        return <TwoFactorAuth />;
      default:
        return <Login />;
    }
  };

  return (
    <PageWrapper>
      <Navbar />
      <ContentWrapper>
        <AnimatePresence mode="wait">
          <MotionContainer
            key={location.pathname}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
          >
            {renderAuthComponent()}
          </MotionContainer>
        </AnimatePresence>
      </ContentWrapper>
    </PageWrapper>
  );
};

const PageWrapper = styled(Box)`
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: rgba(0, 0, 0, 0.5);
  padding-top: 64px; // Add padding to account for the Navbar height
`;

const ContentWrapper = styled(Box)`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  overflow: hidden;
`;

const MotionContainer = styled(motion.div)`
  width: 100%;
  max-width: 400px;
  background: rgba(0, 0, 0, 0.5); // Add background color and transparency
  backdrop-filter: blur(10px); // Add blur effect
  border: 1px solid rgba(255, 255, 255, 0.1); // Add border
  border-radius: 16px; // Match border-radius with FloatingNav
  padding: 24px; // Add padding
`;

export default Auth;