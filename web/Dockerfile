# Build stage
FROM node:18 as build

# Define build arguments for environment variables
ARG VITE_BASE_URL
ARG VITE_CURRENT_BRANCH
ARG VITE_APPWRITE_ENDPOINT
ARG VITE_APPWRITE_PROJECT_ID
ARG VITE_DEBUG

# Set environment variables during the build process
ENV VITE_BASE_URL=$VITE_BASE_URL
ENV VITE_CURRENT_BRANCH=$VITE_CURRENT_BRANCH
ENV VITE_APPWRITE_ENDPOINT=$VITE_APPWRITE_ENDPOINT
ENV VITE_APPWRITE_PROJECT_ID=$VITE_APPWRITE_PROJECT_ID
ENV VITE_DEBUG=$VITE_DEBUG

WORKDIR /app

# Copy package.json and yarn.lock
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# Copy the rest of the code
COPY . .
RUN yarn build

# Production stage
FROM nginx:alpine

LABEL appname="rsglider_web"

# Copy the built assets from the build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 8080
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]