services:
  # PostgreSQL Database
  postgres:
    image: postgres:17-alpine
    container_name: rsglider-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: rsglider
      POSTGRES_USER: rsglider
      POSTGRES_PASSWORD: rsglider_dev_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rsglider -d rsglider"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Sessions
  redis:
    image: redis:7-alpine
    container_name: rsglider-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass rsglider_redis_password
    volumes:
      - redis_data:/data
      - ../docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "rsglider_redis_password", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  api:
    build:
      context: ..
      dockerfile: Dockerfile
    env_file:
      - ../.env
    ports:
      - 3000:3000
      - 9229:9229
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      inbucket:
        condition: service_healthy
    volumes:
      - ..:/app:cached
      # Use named volume for node_modules to avoid architecture conflicts
      - api_node_modules:/app/node_modules

  inbucket:
    image: inbucket/inbucket:latest
    ports:
      - 9001:9000  # Changed from 9000 to 9001 to avoid conflict
      - 2501:2500
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9000"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  api_node_modules:
    driver: local