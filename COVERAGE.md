# Coverage Reporting Guide

This project includes comprehensive coverage reporting with multiple tools and output formats to provide both detailed metrics and tree coverage reports.

## Available Coverage Commands

### Basic Coverage
```bash
# Standard coverage with default reporters
pnpm run test:cov

# Watch mode with coverage
pnpm run test:cov:watch
```

### Detailed Coverage Reports
```bash
# Comprehensive coverage with multiple formats (HTML, JSON, LCOV, Text)
pnpm run test:cov:detailed

# Tree coverage report with HTML visualization
pnpm run test:cov:tree

# Coverage with quality thresholds (70% minimum)
pnpm run test:cov:threshold

# Open HTML coverage report in browser (macOS)
pnpm run test:cov:open
```

### Alternative Coverage Tools
```bash
# Using NYC (Istanbul) for advanced reporting
pnpm run test:cov:nyc

# Using C8 (modern V8 coverage) for faster execution
pnpm run test:cov:c8
```

## Coverage Output Formats

### 1. Text Summary
- Quick overview in terminal
- Shows overall percentages
- Highlights uncovered lines

### 2. HTML Reports
- Interactive tree view of coverage
- File-by-file breakdown
- Line-by-line coverage highlighting
- Located in `coverage/lcov-report/index.html`

### 3. JSON Reports
- Machine-readable coverage data
- Useful for CI/CD integration
- Located in `coverage/coverage-final.json`

### 4. LCOV Reports
- Industry standard format
- Compatible with external tools
- Located in `coverage/lcov.info`

### 5. Clover Reports
- XML format for integration tools
- Located in `coverage/clover.xml`

## Coverage Metrics Explained

### Line Coverage
- Percentage of executable lines that were executed
- **Current Threshold: 60%**

### Function Coverage
- Percentage of functions that were called
- **Current Threshold: 60%**

### Branch Coverage
- Percentage of conditional branches that were executed
- **Current Threshold: 60%**

### Statement Coverage
- Percentage of statements that were executed
- **Current Threshold: 60%**

## Coverage Thresholds

The project uses tiered coverage thresholds:

- **Minimum (Red)**: Below 60%
- **Acceptable (Yellow)**: 60-80%
- **Good (Green)**: Above 80%

## Excluded Files

The following files are excluded from coverage:
- Test files (`*.spec.ts`, `*.integration.spec.ts`)
- Configuration files (`*.config.ts`, `*.config.js`)
- Type definitions (`*.d.ts`)
- Main entry point (`main.ts`)
- Test utilities (`test/**`)
- Build artifacts (`dist/**`)
- Dependencies (`node_modules/**`)

## Best Practices

1. **Run coverage regularly** during development
2. **Aim for 80%+ coverage** on critical business logic
3. **Use HTML reports** to identify uncovered code paths
4. **Focus on branch coverage** for conditional logic
5. **Review coverage trends** over time

## CI/CD Integration

For continuous integration, use:
```bash
# Generate coverage reports for CI
pnpm run test:cov:detailed

# Check coverage thresholds (will fail if below 60%)
pnpm run test:cov:threshold
```

## Troubleshooting

### Coverage Not Generated
- Ensure tests are running successfully
- Check that source files are in the `src/` directory
- Verify Jest configuration in `package.json`

### Low Coverage Numbers
- Add more unit tests for uncovered functions
- Test error handling paths
- Include edge cases in tests

### HTML Report Not Opening
- Check that the coverage directory exists
- Ensure the HTML reporter is enabled
- Try opening `coverage/lcov-report/index.html` manually
