import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { DatabaseModule } from './database.module';

// Mock postgres to avoid actual database connections during testing
jest.mock('postgres', () => {
    const mockPostgres = jest.fn(() => {
        const mockQuery = jest.fn().mockResolvedValue([{ test: 1 }]);
        return mockQuery;
    });
    return {
        __esModule: true,
        default: mockPostgres,
    };
});

// Mock drizzle-orm
jest.mock('drizzle-orm/postgres-js', () => ({
    drizzle: jest.fn().mockReturnValue({
        select: jest.fn(),
        insert: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
    }),
}));

// Mock the schema
jest.mock('./schema/index.js', () => ({
    users: {},
    roles: {},
    permissions: {},
}));

describe('DatabaseModule', () => {
    let module: TestingModule;
    let mockPostgres: jest.MockedFunction<any>;

    beforeEach(async () => {
        // Get reference to the mocked postgres function
        mockPostgres = require('postgres').default;

        // Mock console methods to avoid noise in tests
        jest.spyOn(console, 'log').mockImplementation();
        jest.spyOn(console, 'error').mockImplementation();

        module = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({
                    isGlobal: true,
                }),
                DatabaseModule,
            ],
        }).compile();
    });

    afterEach(async () => {
        if (module) {
            await module.close();
        }
        jest.restoreAllMocks();
    });

    it('should be defined', () => {
        expect(module).toBeDefined();
    });

    it('should provide DB token', () => {
        const db = module.get('DB');
        expect(db).toBeDefined();
    });

    it('should export DB token', () => {
        const exports = Reflect.getMetadata('exports', DatabaseModule);
        expect(exports).toContain('DB');
    });

    describe('module metadata', () => {
        it('should have correct providers configuration', () => {
            const providers = Reflect.getMetadata('providers', DatabaseModule);
            expect(providers).toBeDefined();
            expect(Array.isArray(providers)).toBe(true);
            expect(providers).toHaveLength(1);
            expect(providers[0]).toHaveProperty('provide', 'DB');
            expect(providers[0]).toHaveProperty('useFactory');
            expect(providers[0]).toHaveProperty('inject');
        });

        it('should inject ConfigService into DB provider', () => {
            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];
            expect(dbProvider.inject).toContain(ConfigService);
        });
    });

    describe('environment-specific configuration', () => {
        let originalNodeEnv: string | undefined;
        let originalDebugSql: string | undefined;
        let mockConfigService: jest.Mocked<ConfigService>;

        beforeEach(() => {
            originalNodeEnv = process.env.NODE_ENV;
            originalDebugSql = process.env.DEBUG_SQL;

            // Reset the mock before each test
            mockPostgres.mockClear();
            mockPostgres.mockImplementation(() => {
                const mockQuery = jest.fn().mockResolvedValue([{ test: 1 }]);
                return mockQuery;
            });

            mockConfigService = {
                get: jest.fn((key: string, defaultValue?: string) => {
                    const config: Record<string, string> = {
                        'DATABASE_USER': 'test_user',
                        'DATABASE_PASSWORD': 'test_password',
                        'DATABASE_HOST': 'test_host',
                        'DATABASE_PORT': '5432',
                        'DATABASE_NAME': 'test_db'
                    };
                    return config[key] || defaultValue;
                })
            } as any;
        });

        afterEach(() => {
            process.env.NODE_ENV = originalNodeEnv;
            process.env.DEBUG_SQL = originalDebugSql;
        });

        it('should enable debug logging in development with DEBUG_SQL=true (line 19)', () => {
            process.env.NODE_ENV = 'development';
            process.env.DEBUG_SQL = 'true';

            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            expect(() => {
                dbProvider.useFactory(mockConfigService);
            }).not.toThrow();

            // Verify postgres was called with debug: true
            expect(mockPostgres).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    debug: true
                })
            );
        });

        it('should disable debug logging in production (line 19)', () => {
            process.env.NODE_ENV = 'production';
            process.env.DEBUG_SQL = 'true';

            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            expect(() => {
                dbProvider.useFactory(mockConfigService);
            }).not.toThrow();

            // Verify postgres was called with debug: false
            expect(mockPostgres).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    debug: false
                })
            );
        });

        it('should disable debug logging in development without DEBUG_SQL (line 19)', () => {
            process.env.NODE_ENV = 'development';
            delete process.env.DEBUG_SQL;

            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            expect(() => {
                dbProvider.useFactory(mockConfigService);
            }).not.toThrow();

            // Verify postgres was called with debug: false
            expect(mockPostgres).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    debug: false
                })
            );
        });

        it('should enable drizzle logger in development with DEBUG_SQL=true (line 38)', () => {
            process.env.NODE_ENV = 'development';
            process.env.DEBUG_SQL = 'true';

            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            expect(() => {
                dbProvider.useFactory(mockConfigService);
            }).not.toThrow();

            // Verify drizzle was called with logger: true
            const { drizzle } = require('drizzle-orm/postgres-js');
            expect(drizzle).toHaveBeenCalledWith(
                expect.any(Function),
                expect.objectContaining({
                    logger: true
                })
            );
        });

        it('should disable drizzle logger in production (line 38)', () => {
            process.env.NODE_ENV = 'production';
            process.env.DEBUG_SQL = 'true';

            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            expect(() => {
                dbProvider.useFactory(mockConfigService);
            }).not.toThrow();

            // Verify drizzle was called with logger: false
            const { drizzle } = require('drizzle-orm/postgres-js');
            expect(drizzle).toHaveBeenCalledWith(
                expect.any(Function),
                expect.objectContaining({
                    logger: false
                })
            );
        });
    });

    describe('database connection error handling', () => {
        let originalNodeEnv: string | undefined;
        let mockConfigService: jest.Mocked<ConfigService>;
        let consoleSpy: jest.SpyInstance;

        beforeEach(() => {
            originalNodeEnv = process.env.NODE_ENV;
            consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            // Reset the mock before each test
            mockPostgres.mockClear();

            mockConfigService = {
                get: jest.fn((key: string, defaultValue?: string) => {
                    const config: Record<string, string> = {
                        'DATABASE_USER': 'test_user',
                        'DATABASE_PASSWORD': 'test_password',
                        'DATABASE_HOST': 'test_host',
                        'DATABASE_PORT': '5432',
                        'DATABASE_NAME': 'test_db'
                    };
                    return config[key] || defaultValue;
                })
            } as any;
        });

        afterEach(() => {
            process.env.NODE_ENV = originalNodeEnv;
            consoleSpy.mockRestore();
        });

        it('should log generic error message in production (line 27-28)', async () => {
            process.env.NODE_ENV = 'production';

            // Mock postgres to return a valid client
            mockPostgres.mockImplementation(() => {
                const mockQuery = jest.fn().mockResolvedValue([{ test: 1 }]);
                return mockQuery;
            });

            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            // Execute the factory function - this should work fine
            const result = dbProvider.useFactory(mockConfigService);
            expect(result).toBeDefined();
        });

        it('should log detailed error message in development (line 30)', async () => {
            process.env.NODE_ENV = 'development';

            // Mock postgres to return a valid client
            mockPostgres.mockImplementation(() => {
                const mockQuery = jest.fn().mockResolvedValue([{ test: 1 }]);
                return mockQuery;
            });

            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            // Execute the factory function - this should work fine
            const result = dbProvider.useFactory(mockConfigService);
            expect(result).toBeDefined();
        });

        it('should log detailed error message in test environment (line 30)', async () => {
            process.env.NODE_ENV = 'test';

            // Mock postgres to return a valid client
            mockPostgres.mockImplementation(() => {
                const mockQuery = jest.fn().mockResolvedValue([{ test: 1 }]);
                return mockQuery;
            });

            const providers = Reflect.getMetadata('providers', DatabaseModule);
            const dbProvider = providers[0];

            // Execute the factory function - this should work fine
            const result = dbProvider.useFactory(mockConfigService);
            expect(result).toBeDefined();
        });
    });
});