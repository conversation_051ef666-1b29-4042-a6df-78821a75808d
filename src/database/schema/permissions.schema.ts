import { boolean, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';

export const permissions = pgTable('permissions', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 50 }).notNull().unique(),
  resource: varchar('resource', { length: 50 }).notNull(),
  action: varchar('action', { length: 50 }).notNull(),
  description: varchar('description', { length: 255 }),
  isSystemPermission: boolean('is_system_permission').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}); 