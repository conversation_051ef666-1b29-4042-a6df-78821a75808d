import { boolean, decimal, index, pgEnum, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';

// Enum for release channels
export const releaseChannelEnum = pgEnum('release_channel', ['stable', 'beta']);

// Enum for target platforms
export const targetPlatformEnum = pgEnum('target_platform', ['windows', 'macos', 'linux']);

// Enum for target architectures
export const targetArchEnum = pgEnum('target_arch', ['x64', 'arm64']);

export const clientReleases = pgTable('client_releases', {
  id: uuid('id').primaryKey().defaultRandom(),
  version: varchar('version', { length: 50 }).notNull(),
  channel: releaseChannelEnum('channel').notNull().default('stable'),
  targetPlatform: targetPlatformEnum('target_platform').notNull(),
  targetArch: targetArchEnum('target_arch').notNull(),
  
  // Release metadata
  notes: text('notes'),
  pubDate: timestamp('pub_date').notNull(),
  
  // File information
  fileName: varchar('file_name', { length: 255 }).notNull(),
  fileSize: decimal('file_size', { precision: 15, scale: 0 }), // Size in bytes
  s3Key: varchar('s3_key', { length: 500 }).notNull(), // S3 object key
  s3Bucket: varchar('s3_bucket', { length: 100 }).notNull(),
  
  // Signature for Tauri updater
  signature: text('signature').notNull(),
  
  // DLL version for client compatibility
  dllVersion: varchar('dll_version', { length: 50 }),
  
  // Release status
  isActive: boolean('is_active').notNull().default(false),
  isRollback: boolean('is_rollback').notNull().default(false),
  
  // Metadata
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
}, (table) => ({
  versionIdx: index('client_releases_version_idx').on(table.version),
  channelIdx: index('client_releases_channel_idx').on(table.channel),
  platformIdx: index('client_releases_platform_idx').on(table.targetPlatform),
  activeIdx: index('client_releases_active_idx').on(table.isActive),
  // Unique constraint for active releases per platform/arch/channel
  uniqueActiveRelease: index('client_releases_unique_active_idx').on(
    table.targetPlatform, 
    table.targetArch, 
    table.channel, 
    table.isActive
  ),
}));

export type ClientRelease = typeof clientReleases.$inferSelect;
export type NewClientRelease = typeof clientReleases.$inferInsert;
