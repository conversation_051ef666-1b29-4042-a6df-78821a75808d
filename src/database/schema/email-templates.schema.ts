import { relations } from 'drizzle-orm';
import { boolean, index, integer, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';

export const emailTemplates = pgTable(
    'email_templates',
    {
        id: uuid('id').primaryKey().defaultRandom(),
        name: varchar('name', { length: 255 }).notNull().unique(),
        subject: varchar('subject', { length: 500 }).notNull(),
        htmlContent: text('html_content').notNull(),
        textContent: text('text_content'),
        variables: text('variables'), // JSON string of available variables
        category: varchar('category', { length: 100 }), // e.g., 'auth', 'notification', 'marketing'
        isActive: boolean('is_active').default(true).notNull(),
        createdAt: timestamp('created_at').defaultNow().notNull(),
        updatedAt: timestamp('updated_at').defaultNow().notNull(),
    },
    (table) => ({
        nameIdx: index('email_templates_name_idx').on(table.name),
        categoryIdx: index('email_templates_category_idx').on(table.category),
    }),
);

export const emailTemplatesRelations = relations(emailTemplates, ({ many }) => ({
    emailLogs: many(emailLogs),
    emailCampaigns: many(emailCampaigns),
}));

export const emailLogs = pgTable(
    'email_logs',
    {
        id: uuid('id').primaryKey().defaultRandom(),
        templateId: uuid('template_id').references(() => emailTemplates.id, { onDelete: 'set null' }),
        campaignId: uuid('campaign_id').references(() => emailCampaigns.id, { onDelete: 'set null' }),
        recipientEmail: varchar('recipient_email', { length: 255 }).notNull(),
        recipientUserId: uuid('recipient_user_id'), // Optional - for registered users
        subject: varchar('subject', { length: 500 }).notNull(),
        status: varchar('status', { length: 50 }).notNull(), // 'sent', 'failed', 'queued'
        errorMessage: text('error_message'),
        sentAt: timestamp('sent_at'),
        createdAt: timestamp('created_at').defaultNow().notNull(),
    },
    (table) => ({
        recipientEmailIdx: index('email_logs_recipient_email_idx').on(table.recipientEmail),
        statusIdx: index('email_logs_status_idx').on(table.status),
        sentAtIdx: index('email_logs_sent_at_idx').on(table.sentAt),
        campaignIdx: index('email_logs_campaign_idx').on(table.campaignId),
    }),
);

export const emailLogsRelations = relations(emailLogs, ({ one }) => ({
    template: one(emailTemplates, {
        fields: [emailLogs.templateId],
        references: [emailTemplates.id],
    }),
    campaign: one(emailCampaigns, {
        fields: [emailLogs.campaignId],
        references: [emailCampaigns.id],
    }),
}));

export const emailCampaigns = pgTable(
    'email_campaigns',
    {
        id: uuid('id').primaryKey().defaultRandom(),
        name: varchar('name', { length: 255 }).notNull(),
        templateId: uuid('template_id').references(() => emailTemplates.id).notNull(),
        targetAudience: varchar('target_audience', { length: 100 }).notNull(), // 'all', 'admin', 'user', 'specific'
        recipientCriteria: text('recipient_criteria'), // JSON string for filtering
        status: varchar('status', { length: 50 }).notNull(), // 'draft', 'scheduled', 'sending', 'completed', 'failed'
        scheduledAt: timestamp('scheduled_at'),
        startedAt: timestamp('started_at'),
        completedAt: timestamp('completed_at'),
        totalRecipients: integer('total_recipients').default(0),
        sentCount: integer('sent_count').default(0),
        failedCount: integer('failed_count').default(0),
        createdAt: timestamp('created_at').defaultNow().notNull(),
        updatedAt: timestamp('updated_at').defaultNow().notNull(),
    },
    (table) => ({
        statusIdx: index('email_campaigns_status_idx').on(table.status),
        scheduledAtIdx: index('email_campaigns_scheduled_at_idx').on(table.scheduledAt),
    }),
);

export const emailCampaignsRelations = relations(emailCampaigns, ({ one, many }) => ({
    template: one(emailTemplates, {
        fields: [emailCampaigns.templateId],
        references: [emailTemplates.id],
    }),
    emailLogs: many(emailLogs),
}));

export type EmailTemplate = typeof emailTemplates.$inferSelect;
export type NewEmailTemplate = typeof emailTemplates.$inferInsert;
export type EmailLog = typeof emailLogs.$inferSelect;
export type NewEmailLog = typeof emailLogs.$inferInsert;
export type EmailCampaign = typeof emailCampaigns.$inferSelect;
export type NewEmailCampaign = typeof emailCampaigns.$inferInsert; 