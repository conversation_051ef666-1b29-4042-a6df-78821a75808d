// Import after the test setup to ensure coverage tracking
let userRoles: any;

describe('User Roles Schema', () => {
  beforeAll(() => {
    // Import the schema during test execution to ensure coverage tracking
    const schema = require('./user-roles.schema');
    userRoles = schema.userRoles;
  });

  it('should be defined', () => {
    expect(userRoles).toBeDefined();
  });

  it('should have correct table name', () => {
    expect(userRoles[Symbol.for('drizzle:Name')]).toBe('user_roles');
  });

  it('should have all required columns', () => {
    expect(userRoles.id).toBeDefined();
    expect(userRoles.userId).toBeDefined();
    expect(userRoles.roleId).toBeDefined();
    expect(userRoles.createdAt).toBeDefined();
  });

  it('should have id as primary key', () => {
    expect(userRoles.id.primary).toBe(true);
  });

  it('should have userId as not null', () => {
    expect(userRoles.userId.notNull).toBe(true);
  });

  it('should have roleId as not null', () => {
    expect(userRoles.roleId.notNull).toBe(true);
  });

  it('should have createdAt as not null', () => {
    expect(userRoles.createdAt.notNull).toBe(true);
  });

  it('should have correct column types', () => {
    expect(userRoles.id.dataType).toBe('string');
    expect(userRoles.userId.dataType).toBe('string');
    expect(userRoles.roleId.dataType).toBe('string');
    expect(userRoles.createdAt.dataType).toBe('date');
  });

  it('should have foreign key references defined in schema', () => {
    // Test that the schema imports and uses the referenced tables (lines 7-8)
    // This ensures the foreign key references are properly defined
    expect(() => {
      // Import the referenced schemas to ensure they're accessible
      const { users } = require('./users.schema');
      const { roles } = require('./roles.schema');

      expect(users).toBeDefined();
      expect(roles).toBeDefined();

      // Test that the schema can be used (this exercises the reference functions)
      expect(userRoles.userId.dataType).toBe('string');
      expect(userRoles.roleId.dataType).toBe('string');
    }).not.toThrow();
  });

  it('should support relationship queries', () => {
    // Test that all columns are accessible for queries
    expect(userRoles.id).toBeDefined();
    expect(userRoles.userId).toBeDefined();
    expect(userRoles.roleId).toBeDefined();
    expect(userRoles.createdAt).toBeDefined();
  });

  it('should maintain referential integrity', () => {
    // Test that foreign key columns reference the correct tables
    expect(() => {
      const { users } = require('./users.schema');
      const { roles } = require('./roles.schema');

      // These should not throw when the references are properly defined
      expect(users.id).toBeDefined();
      expect(roles.id).toBeDefined();
    }).not.toThrow();
  });

  it('should have correct table structure for many-to-many relationship', () => {
    // Verify this is a proper junction table
    const columns = Object.keys(userRoles);
    expect(columns).toContain('id');
    expect(columns).toContain('userId');
    expect(columns).toContain('roleId');
    expect(columns).toContain('createdAt');

    // Should have exactly these core columns for a junction table
    const coreColumns = ['id', 'userId', 'roleId', 'createdAt'];
    coreColumns.forEach(col => {
      expect(columns).toContain(col);
    });
  });

  it('should have default values where expected', () => {
    expect(userRoles.id.hasDefault).toBe(true);
    expect(userRoles.createdAt.hasDefault).toBe(true);
  });

  it('should execute foreign key reference functions', () => {
    // Test that the reference functions can be called (lines 7-8)
    // These are the arrow functions () => users.id and () => roles.id

    // Import the referenced tables to trigger the reference functions
    const { users } = require('./users.schema.js');
    const { roles } = require('./roles.schema.js');

    // The reference functions are executed when the schema is defined
    // We can verify the referenced tables exist and have the expected columns
    expect(users.id).toBeDefined();
    expect(roles.id).toBeDefined();

    // Verify the foreign key columns exist and are properly typed
    expect(userRoles.userId).toBeDefined();
    expect(userRoles.roleId).toBeDefined();
    expect(userRoles.userId.notNull).toBe(true);
    expect(userRoles.roleId.notNull).toBe(true);
  });

  it('should execute reference functions for 100% function coverage', () => {
    // Import and directly call the named reference functions
    const { getUserIdReference, getUserRoleIdReference } = require('./user-roles.schema.js');
    const { users } = require('./users.schema.js');
    const { roles } = require('./roles.schema.js');

    // Verify dependencies exist
    expect(users).toBeDefined();
    expect(users.id).toBeDefined();
    expect(roles).toBeDefined();
    expect(roles.id).toBeDefined();

    // Directly call the reference functions to achieve 100% function coverage
    const userIdRef = getUserIdReference();
    const roleIdRef = getUserRoleIdReference();

    // Verify the functions return the expected values
    expect(userIdRef).toBe(users.id);
    expect(roleIdRef).toBe(roles.id);

    // Verify the functions are properly defined
    expect(typeof getUserIdReference).toBe('function');
    expect(typeof getUserRoleIdReference).toBe('function');

    // Verify the schema still works correctly
    expect(userRoles).toBeDefined();
    expect(userRoles.userId).toBeDefined();
    expect(userRoles.roleId).toBeDefined();
  });
});
