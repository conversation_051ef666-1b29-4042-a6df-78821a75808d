CREATE TYPE "public"."release_channel" AS ENUM('stable', 'beta');--> statement-breakpoint
CREATE TYPE "public"."target_arch" AS ENUM('x64', 'arm64');--> statement-breakpoint
CREATE TYPE "public"."target_platform" AS ENUM('windows', 'macos', 'linux');--> statement-breakpoint
CREATE TYPE "public"."file_type" AS ENUM('image', 'video', 'document', 'archive', 'executable');--> statement-breakpoint
CREATE TYPE "public"."upload_status" AS ENUM('pending', 'uploading', 'completed', 'failed', 'deleted');--> statement-breakpoint
CREATE TABLE "client_releases" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"version" varchar(50) NOT NULL,
	"channel" "release_channel" DEFAULT 'stable' NOT NULL,
	"target_platform" "target_platform" NOT NULL,
	"target_arch" "target_arch" NOT NULL,
	"notes" text,
	"pub_date" timestamp NOT NULL,
	"file_name" varchar(255) NOT NULL,
	"file_size" numeric(15, 0),
	"s3_key" varchar(500) NOT NULL,
	"s3_bucket" varchar(100) NOT NULL,
	"signature" text NOT NULL,
	"dll_version" varchar(50),
	"is_active" boolean DEFAULT false NOT NULL,
	"is_rollback" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "file_uploads" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid,
	"original_name" varchar(255) NOT NULL,
	"file_name" varchar(255) NOT NULL,
	"mime_type" varchar(100) NOT NULL,
	"file_type" "file_type" NOT NULL,
	"file_size" numeric(15, 0) NOT NULL,
	"s3_key" varchar(500) NOT NULL,
	"s3_bucket" varchar(100) NOT NULL,
	"s3_etag" varchar(100),
	"status" "upload_status" DEFAULT 'pending' NOT NULL,
	"upload_progress" numeric(5, 2) DEFAULT '0',
	"is_validated" boolean DEFAULT false NOT NULL,
	"validation_errors" json,
	"is_public" boolean DEFAULT false NOT NULL,
	"access_token" varchar(64),
	"metadata" json,
	"expires_at" timestamp,
	"deleted_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "file_uploads" ADD CONSTRAINT "file_uploads_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "client_releases_version_idx" ON "client_releases" USING btree ("version");--> statement-breakpoint
CREATE INDEX "client_releases_channel_idx" ON "client_releases" USING btree ("channel");--> statement-breakpoint
CREATE INDEX "client_releases_platform_idx" ON "client_releases" USING btree ("target_platform");--> statement-breakpoint
CREATE INDEX "client_releases_active_idx" ON "client_releases" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "client_releases_unique_active_idx" ON "client_releases" USING btree ("target_platform","target_arch","channel","is_active");--> statement-breakpoint
CREATE INDEX "file_uploads_user_id_idx" ON "file_uploads" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "file_uploads_status_idx" ON "file_uploads" USING btree ("status");--> statement-breakpoint
CREATE INDEX "file_uploads_file_type_idx" ON "file_uploads" USING btree ("file_type");--> statement-breakpoint
CREATE INDEX "file_uploads_s3_key_idx" ON "file_uploads" USING btree ("s3_key");--> statement-breakpoint
CREATE INDEX "file_uploads_access_token_idx" ON "file_uploads" USING btree ("access_token");--> statement-breakpoint
CREATE INDEX "file_uploads_expires_at_idx" ON "file_uploads" USING btree ("expires_at");