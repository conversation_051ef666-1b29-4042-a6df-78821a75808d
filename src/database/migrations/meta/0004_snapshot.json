{"id": "2f2fb045-aec2-4a1a-93b0-836f8305eeb9", "prevId": "fc34f074-2db7-4d24-b50c-b31d88a4357f", "version": "7", "dialect": "postgresql", "tables": {"public.client_releases": {"name": "client_releases", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "channel": {"name": "channel", "type": "release_channel", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'stable'"}, "target_platform": {"name": "target_platform", "type": "target_platform", "typeSchema": "public", "primaryKey": false, "notNull": true}, "target_arch": {"name": "target_arch", "type": "target_arch", "typeSchema": "public", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "pub_date": {"name": "pub_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "numeric(15, 0)", "primaryKey": false, "notNull": false}, "s3_key": {"name": "s3_key", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "s3_bucket": {"name": "s3_bucket", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "signature": {"name": "signature", "type": "text", "primaryKey": false, "notNull": true}, "dll_version": {"name": "dll_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_rollback": {"name": "is_rollback", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"client_releases_version_idx": {"name": "client_releases_version_idx", "columns": [{"expression": "version", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "client_releases_channel_idx": {"name": "client_releases_channel_idx", "columns": [{"expression": "channel", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "client_releases_platform_idx": {"name": "client_releases_platform_idx", "columns": [{"expression": "target_platform", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "client_releases_active_idx": {"name": "client_releases_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "client_releases_unique_active_idx": {"name": "client_releases_unique_active_idx", "columns": [{"expression": "target_platform", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "target_arch", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "channel", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.devices": {"name": "devices", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "device_name": {"name": "device_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "device_type": {"name": "device_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "device_info": {"name": "device_info", "type": "json", "primaryKey": false, "notNull": false}, "is_trusted": {"name": "is_trusted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "registered_at": {"name": "registered_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"devices_user_id_idx": {"name": "devices_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "devices_device_name_idx": {"name": "devices_device_name_idx", "columns": [{"expression": "device_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"devices_user_id_users_id_fk": {"name": "devices_user_id_users_id_fk", "tableFrom": "devices", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.file_uploads": {"name": "file_uploads", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "original_name": {"name": "original_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "file_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "numeric(15, 0)", "primaryKey": false, "notNull": true}, "s3_key": {"name": "s3_key", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "s3_bucket": {"name": "s3_bucket", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "s3_etag": {"name": "s3_etag", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "upload_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "upload_progress": {"name": "upload_progress", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "is_validated": {"name": "is_validated", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "validation_errors": {"name": "validation_errors", "type": "json", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "access_token": {"name": "access_token", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"file_uploads_user_id_idx": {"name": "file_uploads_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "file_uploads_status_idx": {"name": "file_uploads_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "file_uploads_file_type_idx": {"name": "file_uploads_file_type_idx", "columns": [{"expression": "file_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "file_uploads_s3_key_idx": {"name": "file_uploads_s3_key_idx", "columns": [{"expression": "s3_key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "file_uploads_access_token_idx": {"name": "file_uploads_access_token_idx", "columns": [{"expression": "access_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "file_uploads_expires_at_idx": {"name": "file_uploads_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"file_uploads_user_id_users_id_fk": {"name": "file_uploads_user_id_users_id_fk", "tableFrom": "file_uploads", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.gitea_profiles": {"name": "gitea_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "gitea_user_id": {"name": "gitea_user_id", "type": "integer", "primaryKey": false, "notNull": true}, "gitea_username": {"name": "gitea_username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "gitea_email": {"name": "gitea_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "gitea_full_name": {"name": "gitea_full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "gitea_avatar_url": {"name": "gitea_avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_provisioned": {"name": "is_provisioned", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "sync_status": {"name": "sync_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "sync_errors": {"name": "sync_errors", "type": "json", "primaryKey": false, "notNull": false}, "total_repositories": {"name": "total_repositories", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "public_repositories": {"name": "public_repositories", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "private_repositories": {"name": "private_repositories", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "published_repositories": {"name": "published_repositories", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "gitea_profile": {"name": "gitea_profile", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"gitea_profiles_user_id_idx": {"name": "gitea_profiles_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_profiles_gitea_user_id_idx": {"name": "gitea_profiles_gitea_user_id_idx", "columns": [{"expression": "gitea_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_profiles_gitea_username_idx": {"name": "gitea_profiles_gitea_username_idx", "columns": [{"expression": "gitea_username", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_profiles_sync_status_idx": {"name": "gitea_profiles_sync_status_idx", "columns": [{"expression": "sync_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_profiles_is_active_idx": {"name": "gitea_profiles_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"gitea_profiles_user_id_users_id_fk": {"name": "gitea_profiles_user_id_users_id_fk", "tableFrom": "gitea_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"gitea_profiles_gitea_user_id_unique": {"name": "gitea_profiles_gitea_user_id_unique", "nullsNotDistinct": false, "columns": ["gitea_user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.gitea_repositories": {"name": "gitea_repositories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "gitea_profile_id": {"name": "gitea_profile_id", "type": "uuid", "primaryKey": false, "notNull": true}, "gitea_repo_id": {"name": "gitea_repo_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "visibility": {"name": "visibility", "type": "repository_visibility", "typeSchema": "public", "primaryKey": false, "notNull": true}, "is_fork": {"name": "is_fork", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_template": {"name": "is_template", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_archived": {"name": "is_archived", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_empty": {"name": "is_empty", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "stars_count": {"name": "stars_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "forks_count": {"name": "forks_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "watchers_count": {"name": "watchers_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "open_issues_count": {"name": "open_issues_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "default_branch": {"name": "default_branch", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'main'"}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "topics": {"name": "topics", "type": "json", "primaryKey": false, "notNull": false}, "html_url": {"name": "html_url", "type": "text", "primaryKey": false, "notNull": true}, "clone_url": {"name": "clone_url", "type": "text", "primaryKey": false, "notNull": true}, "ssh_url": {"name": "ssh_url", "type": "text", "primaryKey": false, "notNull": true}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "marketplace_item_id": {"name": "marketplace_item_id", "type": "uuid", "primaryKey": false, "notNull": false}, "has_marketplace_metadata": {"name": "has_marketplace_metadata", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "marketplace_metadata": {"name": "marketplace_metadata", "type": "json", "primaryKey": false, "notNull": false}, "last_sync_at": {"name": "last_sync_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "sync_status": {"name": "sync_status", "type": "repository_sync_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'pending'"}, "sync_errors": {"name": "sync_errors", "type": "json", "primaryKey": false, "notNull": false}, "gitea_created_at": {"name": "gitea_created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "gitea_updated_at": {"name": "gitea_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "gitea_pushed_at": {"name": "gitea_pushed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"gitea_repositories_gitea_profile_id_idx": {"name": "gitea_repositories_gitea_profile_id_idx", "columns": [{"expression": "gitea_profile_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_repositories_gitea_repo_id_idx": {"name": "gitea_repositories_gitea_repo_id_idx", "columns": [{"expression": "gitea_repo_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_repositories_full_name_idx": {"name": "gitea_repositories_full_name_idx", "columns": [{"expression": "full_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_repositories_visibility_idx": {"name": "gitea_repositories_visibility_idx", "columns": [{"expression": "visibility", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_repositories_is_published_idx": {"name": "gitea_repositories_is_published_idx", "columns": [{"expression": "is_published", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_repositories_marketplace_item_id_idx": {"name": "gitea_repositories_marketplace_item_id_idx", "columns": [{"expression": "marketplace_item_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_repositories_sync_status_idx": {"name": "gitea_repositories_sync_status_idx", "columns": [{"expression": "sync_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_repositories_language_idx": {"name": "gitea_repositories_language_idx", "columns": [{"expression": "language", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "gitea_repositories_unique_gitea_repo_idx": {"name": "gitea_repositories_unique_gitea_repo_idx", "columns": [{"expression": "gitea_profile_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "gitea_repo_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"gitea_repositories_gitea_profile_id_gitea_profiles_id_fk": {"name": "gitea_repositories_gitea_profile_id_gitea_profiles_id_fk", "tableFrom": "gitea_repositories", "tableTo": "gitea_profiles", "columnsFrom": ["gitea_profile_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.marketplace_items": {"name": "marketplace_items", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "developer_id": {"name": "developer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "gitea_repository_id": {"name": "gitea_repository_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "short_description": {"name": "short_description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "marketplace_category", "typeSchema": "public", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": false}, "pricing_type": {"name": "pricing_type", "type": "pricing_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "base_price": {"name": "base_price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'USD'"}, "status": {"name": "status", "type": "marketplace_item_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'public'"}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "readme": {"name": "readme", "type": "text", "primaryKey": false, "notNull": false}, "changelog": {"name": "changelog", "type": "text", "primaryKey": false, "notNull": false}, "license": {"name": "license", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "icon_url": {"name": "icon_url", "type": "text", "primaryKey": false, "notNull": false}, "screenshots": {"name": "screenshots", "type": "json", "primaryKey": false, "notNull": false}, "video_url": {"name": "video_url", "type": "text", "primaryKey": false, "notNull": false}, "homepage_url": {"name": "homepage_url", "type": "text", "primaryKey": false, "notNull": false}, "documentation_url": {"name": "documentation_url", "type": "text", "primaryKey": false, "notNull": false}, "support_url": {"name": "support_url", "type": "text", "primaryKey": false, "notNull": false}, "requirements": {"name": "requirements", "type": "json", "primaryKey": false, "notNull": false}, "download_count": {"name": "download_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "rating_average": {"name": "rating_average", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "rating_count": {"name": "rating_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "total_revenue": {"name": "total_revenue", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_sales": {"name": "total_sales", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "meta_title": {"name": "meta_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "meta_keywords": {"name": "meta_keywords", "type": "json", "primaryKey": false, "notNull": false}, "review_notes": {"name": "review_notes", "type": "text", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "text", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_updated_at": {"name": "last_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"marketplace_items_developer_id_idx": {"name": "marketplace_items_developer_id_idx", "columns": [{"expression": "developer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marketplace_items_gitea_repository_id_idx": {"name": "marketplace_items_gitea_repository_id_idx", "columns": [{"expression": "gitea_repository_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marketplace_items_slug_idx": {"name": "marketplace_items_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marketplace_items_category_idx": {"name": "marketplace_items_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marketplace_items_status_idx": {"name": "marketplace_items_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marketplace_items_visibility_idx": {"name": "marketplace_items_visibility_idx", "columns": [{"expression": "visibility", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marketplace_items_is_featured_idx": {"name": "marketplace_items_is_featured_idx", "columns": [{"expression": "is_featured", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marketplace_items_is_active_idx": {"name": "marketplace_items_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marketplace_items_pricing_type_idx": {"name": "marketplace_items_pricing_type_idx", "columns": [{"expression": "pricing_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marketplace_items_published_at_idx": {"name": "marketplace_items_published_at_idx", "columns": [{"expression": "published_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"marketplace_items_developer_id_users_id_fk": {"name": "marketplace_items_developer_id_users_id_fk", "tableFrom": "marketplace_items", "tableTo": "users", "columnsFrom": ["developer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "marketplace_items_gitea_repository_id_gitea_repositories_id_fk": {"name": "marketplace_items_gitea_repository_id_gitea_repositories_id_fk", "tableFrom": "marketplace_items", "tableTo": "gitea_repositories", "columnsFrom": ["gitea_repository_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "marketplace_items_reviewed_by_users_id_fk": {"name": "marketplace_items_reviewed_by_users_id_fk", "tableFrom": "marketplace_items", "tableTo": "users", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"marketplace_items_slug_unique": {"name": "marketplace_items_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permissions": {"name": "permissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "resource": {"name": "resource", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_system_permission": {"name": "is_system_permission", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"permissions_name_unique": {"name": "permissions_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.refresh_tokens": {"name": "refresh_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_revoked": {"name": "is_revoked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "revoked_at": {"name": "revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revoked_by_ip": {"name": "revoked_by_ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "replaced_by_token": {"name": "replaced_by_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_by_ip": {"name": "created_by_ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"refresh_tokens_token_idx": {"name": "refresh_tokens_token_idx", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "refresh_tokens_user_id_idx": {"name": "refresh_tokens_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"refresh_tokens_user_id_users_id_fk": {"name": "refresh_tokens_user_id_users_id_fk", "tableFrom": "refresh_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permissions": {"name": "role_permissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"role_permissions_role_id_roles_id_fk": {"name": "role_permissions_role_id_roles_id_fk", "tableFrom": "role_permissions", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "role_permissions_permission_id_permissions_id_fk": {"name": "role_permissions_permission_id_permissions_id_fk", "tableFrom": "role_permissions", "tableTo": "permissions", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_system_role": {"name": "is_system_role", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"roles_name_unique": {"name": "roles_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_roles_user_id_users_id_fk": {"name": "user_roles_user_id_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_role_id_roles_id_fk": {"name": "user_roles_role_id_roles_id_fk", "tableFrom": "user_roles", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_sessions": {"name": "user_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "session_platform", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'web'"}, "device_info": {"name": "device_info", "type": "json", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "json", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_current": {"name": "is_current", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "requires_verification": {"name": "requires_verification", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revoked_at": {"name": "revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revoked_by_ip": {"name": "revoked_by_ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "revoked_reason": {"name": "revoked_reason", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_sessions_user_id_idx": {"name": "user_sessions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_sessions_user_id_users_id_fk": {"name": "user_sessions_user_id_users_id_fk", "tableFrom": "user_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "roles": {"name": "roles", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{\"user\"}'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "email_verification_token": {"name": "email_verification_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_token": {"name": "password_reset_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_expires": {"name": "password_reset_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "two_factor_secret": {"name": "two_factor_secret", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "two_factor_enabled": {"name": "two_factor_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login_ip": {"name": "last_login_ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.release_channel": {"name": "release_channel", "schema": "public", "values": ["stable", "beta"]}, "public.target_arch": {"name": "target_arch", "schema": "public", "values": ["x64", "arm64"]}, "public.target_platform": {"name": "target_platform", "schema": "public", "values": ["windows", "macos", "linux"]}, "public.file_type": {"name": "file_type", "schema": "public", "values": ["image", "video", "document", "archive", "executable"]}, "public.upload_status": {"name": "upload_status", "schema": "public", "values": ["pending", "uploading", "completed", "failed", "deleted"]}, "public.repository_sync_status": {"name": "repository_sync_status", "schema": "public", "values": ["pending", "syncing", "completed", "failed"]}, "public.repository_visibility": {"name": "repository_visibility", "schema": "public", "values": ["public", "private"]}, "public.marketplace_category": {"name": "marketplace_category", "schema": "public", "values": ["combat", "skilling", "questing", "minigames", "pvm", "pvp", "farming", "fishing", "mining", "woodcutting", "crafting", "cooking", "fletching", "smithing", "runecrafting", "construction", "hunter", "thieving", "slayer", "agility", "herblore", "firemaking", "prayer", "magic", "ranged", "attack", "strength", "defence", "hitpoints", "archaeology", "invention", "divination", "dungeoneering", "summoning", "bossing", "clue_scrolls", "treasure_trails", "dailies", "weeklies", "money_making", "grand_exchange", "utilities", "other"]}, "public.marketplace_item_status": {"name": "marketplace_item_status", "schema": "public", "values": ["draft", "pending_review", "approved", "rejected", "published", "suspended", "archived"]}, "public.pricing_type": {"name": "pricing_type", "schema": "public", "values": ["free", "one_time", "subscription"]}, "public.session_platform": {"name": "session_platform", "schema": "public", "values": ["web", "desktop", "bot"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}