CREATE TYPE "public"."repository_sync_status" AS ENUM('pending', 'syncing', 'completed', 'failed');--> statement-breakpoint
CREATE TYPE "public"."repository_visibility" AS ENUM('public', 'private');--> statement-breakpoint
CREATE TYPE "public"."marketplace_category" AS ENUM('combat', 'skilling', 'questing', 'minigames', 'pvm', 'pvp', 'farming', 'fishing', 'mining', 'woodcutting', 'crafting', 'cooking', 'fletching', 'smithing', 'runecrafting', 'construction', 'hunter', 'thieving', 'slayer', 'agility', 'herblore', 'firemaking', 'prayer', 'magic', 'ranged', 'attack', 'strength', 'defence', 'hitpoints', 'archaeology', 'invention', 'divination', 'dungeoneering', 'summoning', 'bossing', 'clue_scrolls', 'treasure_trails', 'dailies', 'weeklies', 'money_making', 'grand_exchange', 'utilities', 'other');--> statement-breakpoint
CREATE TYPE "public"."marketplace_item_status" AS ENUM('draft', 'pending_review', 'approved', 'rejected', 'published', 'suspended', 'archived');--> statement-breakpoint
CREATE TYPE "public"."pricing_type" AS ENUM('free', 'one_time', 'subscription');--> statement-breakpoint
CREATE TABLE "gitea_profiles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"gitea_user_id" integer NOT NULL,
	"gitea_username" varchar(100) NOT NULL,
	"gitea_email" varchar(255) NOT NULL,
	"gitea_full_name" varchar(255),
	"gitea_avatar_url" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"is_provisioned" boolean DEFAULT false NOT NULL,
	"last_sync_at" timestamp,
	"sync_status" varchar(50) DEFAULT 'pending',
	"sync_errors" json,
	"total_repositories" integer DEFAULT 0,
	"public_repositories" integer DEFAULT 0,
	"private_repositories" integer DEFAULT 0,
	"published_repositories" integer DEFAULT 0,
	"gitea_profile" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "gitea_profiles_gitea_user_id_unique" UNIQUE("gitea_user_id")
);
--> statement-breakpoint
CREATE TABLE "gitea_repositories" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"gitea_profile_id" uuid NOT NULL,
	"gitea_repo_id" integer NOT NULL,
	"name" varchar(255) NOT NULL,
	"full_name" varchar(500) NOT NULL,
	"description" text,
	"visibility" "repository_visibility" NOT NULL,
	"is_fork" boolean DEFAULT false NOT NULL,
	"is_template" boolean DEFAULT false NOT NULL,
	"is_archived" boolean DEFAULT false NOT NULL,
	"is_empty" boolean DEFAULT false NOT NULL,
	"size" integer DEFAULT 0,
	"stars_count" integer DEFAULT 0,
	"forks_count" integer DEFAULT 0,
	"watchers_count" integer DEFAULT 0,
	"open_issues_count" integer DEFAULT 0,
	"default_branch" varchar(100) DEFAULT 'main',
	"language" varchar(100),
	"topics" json,
	"html_url" text NOT NULL,
	"clone_url" text NOT NULL,
	"ssh_url" text NOT NULL,
	"is_published" boolean DEFAULT false NOT NULL,
	"marketplace_item_id" uuid,
	"has_marketplace_metadata" boolean DEFAULT false NOT NULL,
	"marketplace_metadata" json,
	"last_sync_at" timestamp,
	"sync_status" "repository_sync_status" DEFAULT 'pending',
	"sync_errors" json,
	"gitea_created_at" timestamp,
	"gitea_updated_at" timestamp,
	"gitea_pushed_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "marketplace_items" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"developer_id" uuid NOT NULL,
	"gitea_repository_id" uuid,
	"name" varchar(255) NOT NULL,
	"slug" varchar(255) NOT NULL,
	"description" text NOT NULL,
	"short_description" varchar(500),
	"category" "marketplace_category" NOT NULL,
	"tags" json,
	"pricing_type" "pricing_type" NOT NULL,
	"base_price" numeric(10, 2),
	"currency" varchar(3) DEFAULT 'USD',
	"status" "marketplace_item_status" DEFAULT 'draft' NOT NULL,
	"visibility" varchar(20) DEFAULT 'public' NOT NULL,
	"is_featured" boolean DEFAULT false NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"readme" text,
	"changelog" text,
	"license" varchar(100),
	"version" varchar(50),
	"icon_url" text,
	"screenshots" json,
	"video_url" text,
	"homepage_url" text,
	"documentation_url" text,
	"support_url" text,
	"requirements" json,
	"download_count" integer DEFAULT 0,
	"view_count" integer DEFAULT 0,
	"rating_average" numeric(3, 2) DEFAULT '0',
	"rating_count" integer DEFAULT 0,
	"total_revenue" numeric(15, 2) DEFAULT '0',
	"total_sales" integer DEFAULT 0,
	"meta_title" varchar(255),
	"meta_description" varchar(500),
	"meta_keywords" json,
	"review_notes" text,
	"rejection_reason" text,
	"reviewed_by" uuid,
	"reviewed_at" timestamp,
	"published_at" timestamp,
	"last_updated_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "marketplace_items_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
ALTER TABLE "gitea_profiles" ADD CONSTRAINT "gitea_profiles_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "gitea_repositories" ADD CONSTRAINT "gitea_repositories_gitea_profile_id_gitea_profiles_id_fk" FOREIGN KEY ("gitea_profile_id") REFERENCES "public"."gitea_profiles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "marketplace_items" ADD CONSTRAINT "marketplace_items_developer_id_users_id_fk" FOREIGN KEY ("developer_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "marketplace_items" ADD CONSTRAINT "marketplace_items_gitea_repository_id_gitea_repositories_id_fk" FOREIGN KEY ("gitea_repository_id") REFERENCES "public"."gitea_repositories"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "marketplace_items" ADD CONSTRAINT "marketplace_items_reviewed_by_users_id_fk" FOREIGN KEY ("reviewed_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "gitea_profiles_user_id_idx" ON "gitea_profiles" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "gitea_profiles_gitea_user_id_idx" ON "gitea_profiles" USING btree ("gitea_user_id");--> statement-breakpoint
CREATE INDEX "gitea_profiles_gitea_username_idx" ON "gitea_profiles" USING btree ("gitea_username");--> statement-breakpoint
CREATE INDEX "gitea_profiles_sync_status_idx" ON "gitea_profiles" USING btree ("sync_status");--> statement-breakpoint
CREATE INDEX "gitea_profiles_is_active_idx" ON "gitea_profiles" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "gitea_repositories_gitea_profile_id_idx" ON "gitea_repositories" USING btree ("gitea_profile_id");--> statement-breakpoint
CREATE INDEX "gitea_repositories_gitea_repo_id_idx" ON "gitea_repositories" USING btree ("gitea_repo_id");--> statement-breakpoint
CREATE INDEX "gitea_repositories_full_name_idx" ON "gitea_repositories" USING btree ("full_name");--> statement-breakpoint
CREATE INDEX "gitea_repositories_visibility_idx" ON "gitea_repositories" USING btree ("visibility");--> statement-breakpoint
CREATE INDEX "gitea_repositories_is_published_idx" ON "gitea_repositories" USING btree ("is_published");--> statement-breakpoint
CREATE INDEX "gitea_repositories_marketplace_item_id_idx" ON "gitea_repositories" USING btree ("marketplace_item_id");--> statement-breakpoint
CREATE INDEX "gitea_repositories_sync_status_idx" ON "gitea_repositories" USING btree ("sync_status");--> statement-breakpoint
CREATE INDEX "gitea_repositories_language_idx" ON "gitea_repositories" USING btree ("language");--> statement-breakpoint
CREATE INDEX "gitea_repositories_unique_gitea_repo_idx" ON "gitea_repositories" USING btree ("gitea_profile_id","gitea_repo_id");--> statement-breakpoint
CREATE INDEX "marketplace_items_developer_id_idx" ON "marketplace_items" USING btree ("developer_id");--> statement-breakpoint
CREATE INDEX "marketplace_items_gitea_repository_id_idx" ON "marketplace_items" USING btree ("gitea_repository_id");--> statement-breakpoint
CREATE INDEX "marketplace_items_slug_idx" ON "marketplace_items" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "marketplace_items_category_idx" ON "marketplace_items" USING btree ("category");--> statement-breakpoint
CREATE INDEX "marketplace_items_status_idx" ON "marketplace_items" USING btree ("status");--> statement-breakpoint
CREATE INDEX "marketplace_items_visibility_idx" ON "marketplace_items" USING btree ("visibility");--> statement-breakpoint
CREATE INDEX "marketplace_items_is_featured_idx" ON "marketplace_items" USING btree ("is_featured");--> statement-breakpoint
CREATE INDEX "marketplace_items_is_active_idx" ON "marketplace_items" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "marketplace_items_pricing_type_idx" ON "marketplace_items" USING btree ("pricing_type");--> statement-breakpoint
CREATE INDEX "marketplace_items_published_at_idx" ON "marketplace_items" USING btree ("published_at");