import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { DeveloperManagementService } from '../common/services/developer-management.service';
import { RepositorySyncService } from '../common/services/repository-sync.service';
import { DeveloperController } from './developer.controller';

describe('DeveloperController Unit Tests - Complete Branch Coverage', () => {
    let controller: DeveloperController;
    let developerManagementService: jest.Mocked<DeveloperManagementService>;
    let repositorySyncService: jest.Mocked<RepositorySyncService>;

    const mockUser = { id: 'user-123' } as any;

    beforeEach(async () => {
        const mockDeveloperManagementService = {
            createDeveloper: jest.fn(),
            getDeveloperProfile: jest.fn(),
            provisionGiteaAccount: jest.fn(),
            syncDeveloperProfile: jest.fn(),
        };

        const mockRepositorySyncService = {
            getDeveloperRepositories: jest.fn(),
            syncDeveloperRepositories: jest.fn(),
            getRepository: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            controllers: [DeveloperController],
            providers: [
                {
                    provide: DeveloperManagementService,
                    useValue: mockDeveloperManagementService,
                },
                {
                    provide: RepositorySyncService,
                    useValue: mockRepositorySyncService,
                },
            ],
        }).compile();

        controller = module.get<DeveloperController>(DeveloperController);
        developerManagementService = module.get(DeveloperManagementService);
        repositorySyncService = module.get(RepositorySyncService);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('createDeveloper', () => {
        const mockCreateRequest = {
            giteaUsername: 'testuser',
            giteaPassword: 'password123',
            autoProvision: true,
        };

        it('should create developer with autoProvision true', async () => {
            const mockResult = {
                user: {
                    id: 'user-123',
                    name: 'Test User',
                    email: '<EMAIL>',
                    password: 'hashedpassword',
                    bio: 'Test bio',
                    roles: ['developer'],
                    isActive: true,
                    emailVerified: true,
                    emailVerificationToken: 'token123',
                    passwordResetToken: null,
                    passwordResetExpires: null,
                    twoFactorSecret: null,
                    twoFactorEnabled: false,
                    lastLoginAt: new Date('2023-01-01'),
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
                giteaProfile: {
                    id: 'profile-123',
                    giteaUserId: 456,
                    giteaUsername: 'testuser',
                    giteaEmail: '<EMAIL>',
                    giteaFullName: 'Test User',
                    giteaAvatarUrl: 'https://avatar.url',
                    isActive: true,
                    syncStatus: 'completed',
                    lastSyncAt: new Date('2023-01-01'),
                    publicRepositories: 5,
                    privateRepositories: 3,
                    giteaProfile: { some: 'data' },
                    syncErrors: null,
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
                isProvisioned: true,
                repositoryCount: 8,
                publishedCount: 2,
            } as any;

            developerManagementService.createDeveloper.mockResolvedValue(mockResult);

            const result = await controller.createDeveloper(mockUser, mockCreateRequest);

            expect(developerManagementService.createDeveloper).toHaveBeenCalledWith({
                userId: 'user-123',
                giteaUsername: 'testuser',
                giteaPassword: 'password123',
                autoProvision: true, // Tests line 57 - true path
            });

            expect(result).toEqual({
                id: 'profile-123',
                userId: 'user-123',
                giteaUserId: 456,
                giteaUsername: 'testuser',
                giteaEmail: '<EMAIL>',
                giteaFullName: 'Test User',
                giteaAvatarUrl: 'https://avatar.url',
                isActive: true,
                isProvisioned: true,
                syncStatus: 'completed',
                lastSyncAt: new Date('2023-01-01'),
                totalRepositories: 8,
                publicRepositories: 5,
                privateRepositories: 3,
                publishedRepositories: 2,
                giteaProfile: { some: 'data' },
                syncErrors: null,
                createdAt: new Date('2023-01-01'),
                updatedAt: new Date('2023-01-02'),
            });
        });

        it('should create developer with autoProvision false (undefined)', async () => {
            const requestWithoutAutoProvision = {
                giteaUsername: 'testuser',
                giteaPassword: 'password123',
                // autoProvision is undefined
            };

            const mockResult = {
                user: {
                    id: 'user-123',
                    name: 'Test User',
                    email: '<EMAIL>',
                    password: 'hashedpassword',
                    bio: 'Test bio',
                    roles: ['developer'],
                    isActive: true,
                    emailVerified: true,
                    emailVerificationToken: 'token123',
                    passwordResetToken: null,
                    passwordResetExpires: null,
                    twoFactorSecret: null,
                    twoFactorEnabled: false,
                    lastLoginAt: new Date('2023-01-01'),
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
                giteaProfile: null, // Test null giteaProfile
                isProvisioned: false,
                repositoryCount: 0,
                publishedCount: 0,
            } as any;

            developerManagementService.createDeveloper.mockResolvedValue(mockResult);

            const result = await controller.createDeveloper(mockUser, requestWithoutAutoProvision);

            expect(developerManagementService.createDeveloper).toHaveBeenCalledWith({
                userId: 'user-123',
                giteaUsername: 'testuser',
                giteaPassword: 'password123',
                autoProvision: true, // Tests line 57 - ?? true (default)
            });

            // Test all the || fallbacks when giteaProfile is null
            expect(result).toEqual({
                id: '',
                userId: 'user-123',
                giteaUserId: 0,
                giteaUsername: '',
                giteaEmail: '',
                giteaFullName: '',
                giteaAvatarUrl: null,
                isActive: false,
                isProvisioned: false,
                syncStatus: 'pending',
                lastSyncAt: null,
                totalRepositories: 0,
                publicRepositories: 0,
                privateRepositories: 0,
                publishedRepositories: 0,
                giteaProfile: null,
                syncErrors: null,
                createdAt: expect.any(Date),
                updatedAt: expect.any(Date),
            });
        });

        it('should handle "already exists" error', async () => {
            developerManagementService.createDeveloper.mockRejectedValue(
                new Error('Developer profile already exists for user')
            );

            await expect(controller.createDeveloper(mockUser, mockCreateRequest))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.createDeveloper(mockUser, mockCreateRequest);
            } catch (error) {
                expect(error.message).toBe('Developer profile already exists for this user');
            }
        });

        it('should handle generic error', async () => {
            developerManagementService.createDeveloper.mockRejectedValue(
                new Error('Database connection failed')
            );

            await expect(controller.createDeveloper(mockUser, mockCreateRequest))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.createDeveloper(mockUser, mockCreateRequest);
            } catch (error) {
                expect(error.message).toBe('Failed to create developer profile: Database connection failed');
            }
        });
    });

    describe('getDeveloperProfile', () => {
        it('should return developer profile when found', async () => {
            const mockResult = {
                user: { id: 'user-123' },
                giteaProfile: {
                    id: 'profile-123',
                    giteaUserId: 456,
                    giteaUsername: 'testuser',
                    giteaEmail: '<EMAIL>',
                    giteaFullName: 'Test User',
                    giteaAvatarUrl: 'https://avatar.url',
                    isActive: true,
                    syncStatus: 'completed',
                    lastSyncAt: new Date('2023-01-01'),
                    publicRepositories: 5,
                    privateRepositories: 3,
                    giteaProfile: { some: 'data' },
                    syncErrors: null,
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
                isProvisioned: true,
                repositoryCount: 8,
                publishedCount: 2,
            } as any;

            developerManagementService.getDeveloperProfile.mockResolvedValue(mockResult);

            const result = await controller.getDeveloperProfile(mockUser);

            expect(result).toEqual({
                id: 'profile-123',
                userId: 'user-123',
                giteaUserId: 456,
                giteaUsername: 'testuser',
                giteaEmail: '<EMAIL>',
                giteaFullName: 'Test User',
                giteaAvatarUrl: 'https://avatar.url',
                isActive: true,
                isProvisioned: true,
                syncStatus: 'completed',
                lastSyncAt: new Date('2023-01-01'),
                totalRepositories: 8,
                publicRepositories: 5,
                privateRepositories: 3,
                publishedRepositories: 2,
                giteaProfile: { some: 'data' },
                syncErrors: null,
                createdAt: new Date('2023-01-01'),
                updatedAt: new Date('2023-01-02'),
            });
        });

        it('should throw NotFoundException when profile not found', async () => {
            developerManagementService.getDeveloperProfile.mockResolvedValue(null);

            await expect(controller.getDeveloperProfile(mockUser))
                .rejects.toThrow(NotFoundException);

            try {
                await controller.getDeveloperProfile(mockUser);
            } catch (error) {
                expect(error.message).toBe('Developer profile not found');
            }
        });
    });

    describe('provisionGiteaAccount', () => {
        const mockProvisionRequest = { password: 'newpassword123' };

        it('should provision Gitea account successfully', async () => {
            const mockResult = {
                user: { id: 'user-123' },
                giteaProfile: {
                    id: 'profile-123',
                    giteaUserId: 456,
                    giteaUsername: 'testuser',
                    giteaEmail: '<EMAIL>',
                    giteaFullName: 'Test User',
                    giteaAvatarUrl: 'https://avatar.url',
                    isActive: true,
                    syncStatus: 'completed',
                    lastSyncAt: new Date('2023-01-01'),
                    publicRepositories: 5,
                    privateRepositories: 3,
                    giteaProfile: { some: 'data' },
                    syncErrors: null,
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
                isProvisioned: true,
                repositoryCount: 8,
                publishedCount: 2,
            } as any;

            developerManagementService.provisionGiteaAccount.mockResolvedValue(mockResult);

            const result = await controller.provisionGiteaAccount(mockUser, mockProvisionRequest);

            expect(developerManagementService.provisionGiteaAccount).toHaveBeenCalledWith(
                'user-123',
                'newpassword123'
            );

            expect(result.isProvisioned).toBe(true);
        });

        it('should handle "not found" error', async () => {
            developerManagementService.provisionGiteaAccount.mockRejectedValue(
                new Error('Developer profile not found')
            );

            await expect(controller.provisionGiteaAccount(mockUser, mockProvisionRequest))
                .rejects.toThrow(NotFoundException);

            try {
                await controller.provisionGiteaAccount(mockUser, mockProvisionRequest);
            } catch (error) {
                expect(error.message).toBe('Developer profile not found');
            }
        });

        it('should handle "already provisioned" error', async () => {
            developerManagementService.provisionGiteaAccount.mockRejectedValue(
                new Error('Gitea account already provisioned')
            );

            await expect(controller.provisionGiteaAccount(mockUser, mockProvisionRequest))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.provisionGiteaAccount(mockUser, mockProvisionRequest);
            } catch (error) {
                expect(error.message).toBe('Gitea account already provisioned');
            }
        });

        it('should handle generic error', async () => {
            developerManagementService.provisionGiteaAccount.mockRejectedValue(
                new Error('Network timeout')
            );

            await expect(controller.provisionGiteaAccount(mockUser, mockProvisionRequest))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.provisionGiteaAccount(mockUser, mockProvisionRequest);
            } catch (error) {
                expect(error.message).toBe('Failed to provision Gitea account: Network timeout');
            }
        });
    });

    describe('syncDeveloperProfile - Generic Error Handler Coverage', () => {
        it('should handle generic error in syncDeveloperProfile - COVERS LINE 203', async () => {
            // Mock a generic error that does NOT contain "not found" to trigger the generic error handler
            // This will hit the generic BadRequestException throw on line 203
            developerManagementService.syncDeveloperProfile.mockRejectedValue(
                new Error('Generic sync error - database connection failed')
            );

            await expect(controller.syncDeveloperProfile(mockUser))
                .rejects.toThrow(BadRequestException);

            // Verify the specific error message format from line 203
            try {
                await controller.syncDeveloperProfile(mockUser);
            } catch (error) {
                expect(error.message).toBe('Failed to sync developer profile: Generic sync error - database connection failed');
            }
        });

        it('should handle timeout error in syncDeveloperProfile - COVERS LINE 203', async () => {
            // Another test case to ensure line 203 is covered with different error types
            developerManagementService.syncDeveloperProfile.mockRejectedValue(
                new Error('Service timeout exceeded')
            );

            await expect(controller.syncDeveloperProfile(mockUser))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.syncDeveloperProfile(mockUser);
            } catch (error) {
                expect(error.message).toBe('Failed to sync developer profile: Service timeout exceeded');
            }
        });

        it('should handle network error in syncDeveloperProfile - COVERS LINE 203', async () => {
            // Third test case to thoroughly cover the generic error handler
            developerManagementService.syncDeveloperProfile.mockRejectedValue(
                new Error('Network error: unable to reach external service')
            );

            await expect(controller.syncDeveloperProfile(mockUser))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.syncDeveloperProfile(mockUser);
            } catch (error) {
                expect(error.message).toBe('Failed to sync developer profile: Network error: unable to reach external service');
            }
        });

        it('should handle "not found" error in syncDeveloperProfile', async () => {
            developerManagementService.syncDeveloperProfile.mockRejectedValue(
                new Error('Developer profile not found')
            );

            await expect(controller.syncDeveloperProfile(mockUser))
                .rejects.toThrow(NotFoundException);

            try {
                await controller.syncDeveloperProfile(mockUser);
            } catch (error) {
                expect(error.message).toBe('Provisioned developer profile not found');
            }
        });
    });

    describe('getDeveloperRepositories', () => {
        it('should return repositories successfully', async () => {
            const mockRepositories = [
                {
                    id: 'repo-1',
                    giteaRepoId: 123,
                    name: 'test-repo',
                    fullName: 'user/test-repo',
                    description: 'Test repository',
                    visibility: 'public',
                    isFork: false,
                    isTemplate: false,
                    isArchived: false,
                    isEmpty: false,
                    size: 1024,
                    starsCount: 5,
                    forksCount: 2,
                    watchersCount: 3,
                    openIssuesCount: 1,
                    defaultBranch: 'main',
                    language: 'TypeScript',
                    topics: ['web', 'api'],
                    htmlUrl: 'https://git.example.com/user/test-repo',
                    cloneUrl: 'https://git.example.com/user/test-repo.git',
                    sshUrl: '*******************:user/test-repo.git',
                    isPublished: true,
                    marketplaceItemId: 'item-123',
                    hasMarketplaceMetadata: true,
                    marketplaceMetadata: { category: 'tools' },
                    syncStatus: 'completed',
                    lastSyncAt: new Date('2023-01-01'),
                    giteaCreatedAt: new Date('2023-01-01'),
                    giteaUpdatedAt: new Date('2023-01-02'),
                    giteaPushedAt: new Date('2023-01-03'),
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
            ];

            repositorySyncService.getDeveloperRepositories.mockResolvedValue(mockRepositories as any);

            const result = await controller.getDeveloperRepositories(mockUser);

            expect(repositorySyncService.getDeveloperRepositories).toHaveBeenCalledWith('user-123', true);
            expect(result).toEqual([
                {
                    id: 'repo-1',
                    giteaRepoId: 123,
                    name: 'test-repo',
                    fullName: 'user/test-repo',
                    description: 'Test repository',
                    visibility: 'public',
                    isFork: false,
                    isTemplate: false,
                    isArchived: false,
                    isEmpty: false,
                    size: 1024,
                    starsCount: 5,
                    forksCount: 2,
                    watchersCount: 3,
                    openIssuesCount: 1,
                    defaultBranch: 'main',
                    language: 'TypeScript',
                    topics: ['web', 'api'],
                    htmlUrl: 'https://git.example.com/user/test-repo',
                    cloneUrl: 'https://git.example.com/user/test-repo.git',
                    sshUrl: '*******************:user/test-repo.git',
                    isPublished: true,
                    marketplaceItemId: 'item-123',
                    hasMarketplaceMetadata: true,
                    marketplaceMetadata: { category: 'tools' },
                    syncStatus: 'completed',
                    lastSyncAt: new Date('2023-01-01'),
                    giteaCreatedAt: new Date('2023-01-01'),
                    giteaUpdatedAt: new Date('2023-01-02'),
                    giteaPushedAt: new Date('2023-01-03'),
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
            ]);
        });

        it('should handle repositories with null/undefined optional fields', async () => {
            const mockRepositories = [
                {
                    id: 'repo-1',
                    giteaRepoId: 123,
                    name: 'test-repo',
                    fullName: 'user/test-repo',
                    description: 'Test repository',
                    visibility: 'private',
                    isFork: false,
                    isTemplate: false,
                    isArchived: false,
                    isEmpty: false,
                    size: 1024,
                    starsCount: 5,
                    forksCount: 2,
                    watchersCount: 3,
                    openIssuesCount: 1,
                    defaultBranch: 'main',
                    language: 'TypeScript',
                    topics: null, // Test null topics
                    htmlUrl: 'https://git.example.com/user/test-repo',
                    cloneUrl: 'https://git.example.com/user/test-repo.git',
                    sshUrl: '*******************:user/test-repo.git',
                    isPublished: null, // Test null isPublished
                    marketplaceItemId: 'item-123',
                    hasMarketplaceMetadata: null, // Test null hasMarketplaceMetadata
                    marketplaceMetadata: { category: 'tools' },
                    syncStatus: 'completed',
                    lastSyncAt: new Date('2023-01-01'),
                    giteaCreatedAt: new Date('2023-01-01'),
                    giteaUpdatedAt: new Date('2023-01-02'),
                    giteaPushedAt: new Date('2023-01-03'),
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
            ];

            repositorySyncService.getDeveloperRepositories.mockResolvedValue(mockRepositories as any);

            const result = await controller.getDeveloperRepositories(mockUser);

            expect(result[0].topics).toEqual([]); // Should default to []
            expect(result[0].isPublished).toBe(false); // Should default to false
            expect(result[0].hasMarketplaceMetadata).toBe(false); // Should default to false
        });

        it('should handle "not found" error', async () => {
            repositorySyncService.getDeveloperRepositories.mockRejectedValue(
                new Error('Developer profile not found')
            );

            await expect(controller.getDeveloperRepositories(mockUser))
                .rejects.toThrow(NotFoundException);

            try {
                await controller.getDeveloperRepositories(mockUser);
            } catch (error) {
                expect(error.message).toBe('Developer profile not found');
            }
        });

        it('should handle generic error', async () => {
            repositorySyncService.getDeveloperRepositories.mockRejectedValue(
                new Error('Database error')
            );

            await expect(controller.getDeveloperRepositories(mockUser))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.getDeveloperRepositories(mockUser);
            } catch (error) {
                expect(error.message).toBe('Failed to get repositories: Database error');
            }
        });
    });

    describe('syncRepositories', () => {
        it('should sync repositories successfully', async () => {
            const mockSyncResult = {
                success: true,
                synced: 5,
                created: 2,
                updated: 3,
                errors: [],
            };

            repositorySyncService.syncDeveloperRepositories.mockResolvedValue(mockSyncResult as any);

            const result = await controller.syncRepositories(mockUser);

            expect(repositorySyncService.syncDeveloperRepositories).toHaveBeenCalledWith('user-123');
            expect(result).toEqual(mockSyncResult);
        });

        it('should handle "not found" error', async () => {
            repositorySyncService.syncDeveloperRepositories.mockRejectedValue(
                new Error('Developer profile not found')
            );

            await expect(controller.syncRepositories(mockUser))
                .rejects.toThrow(NotFoundException);

            try {
                await controller.syncRepositories(mockUser);
            } catch (error) {
                expect(error.message).toBe('Developer profile not found');
            }
        });

        it('should handle "not provisioned" error', async () => {
            repositorySyncService.syncDeveloperRepositories.mockRejectedValue(
                new Error('Developer profile not provisioned with Gitea')
            );

            await expect(controller.syncRepositories(mockUser))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.syncRepositories(mockUser);
            } catch (error) {
                expect(error.message).toBe('Developer profile not provisioned with Gitea');
            }
        });

        it('should handle generic error', async () => {
            repositorySyncService.syncDeveloperRepositories.mockRejectedValue(
                new Error('Sync service unavailable')
            );

            await expect(controller.syncRepositories(mockUser))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.syncRepositories(mockUser);
            } catch (error) {
                expect(error.message).toBe('Failed to sync repositories: Sync service unavailable');
            }
        });
    });

    describe('getRepository', () => {
        const repositoryId = 'repo-123';

        it('should return repository details successfully', async () => {
            const mockRepository = {
                id: 'repo-123',
                giteaRepoId: 456,
                name: 'my-repo',
                fullName: 'user/my-repo',
                description: 'My repository',
                visibility: 'public',
                isFork: false,
                isTemplate: false,
                isArchived: false,
                isEmpty: false,
                size: 2048,
                starsCount: 10,
                forksCount: 3,
                watchersCount: 5,
                openIssuesCount: 2,
                defaultBranch: 'main',
                language: 'JavaScript',
                topics: ['frontend', 'react'],
                htmlUrl: 'https://git.example.com/user/my-repo',
                cloneUrl: 'https://git.example.com/user/my-repo.git',
                sshUrl: '*******************:user/my-repo.git',
                isPublished: true,
                marketplaceItemId: 'item-456',
                hasMarketplaceMetadata: true,
                marketplaceMetadata: { category: 'frontend' },
                syncStatus: 'completed',
                lastSyncAt: new Date('2023-01-01'),
                giteaCreatedAt: new Date('2023-01-01'),
                giteaUpdatedAt: new Date('2023-01-02'),
                giteaPushedAt: new Date('2023-01-03'),
                createdAt: new Date('2023-01-01'),
                updatedAt: new Date('2023-01-02'),
            };

            repositorySyncService.getRepository.mockResolvedValue(mockRepository as any);

            const result = await controller.getRepository(repositoryId);

            expect(repositorySyncService.getRepository).toHaveBeenCalledWith('repo-123');
            expect(result).toEqual({
                id: 'repo-123',
                giteaRepoId: 456,
                name: 'my-repo',
                fullName: 'user/my-repo',
                description: 'My repository',
                visibility: 'public',
                isFork: false,
                isTemplate: false,
                isArchived: false,
                isEmpty: false,
                size: 2048,
                starsCount: 10,
                forksCount: 3,
                watchersCount: 5,
                openIssuesCount: 2,
                defaultBranch: 'main',
                language: 'JavaScript',
                topics: ['frontend', 'react'],
                htmlUrl: 'https://git.example.com/user/my-repo',
                cloneUrl: 'https://git.example.com/user/my-repo.git',
                sshUrl: '*******************:user/my-repo.git',
                isPublished: true,
                marketplaceItemId: 'item-456',
                hasMarketplaceMetadata: true,
                marketplaceMetadata: { category: 'frontend' },
                syncStatus: 'completed',
                lastSyncAt: new Date('2023-01-01'),
                giteaCreatedAt: new Date('2023-01-01'),
                giteaUpdatedAt: new Date('2023-01-02'),
                giteaPushedAt: new Date('2023-01-03'),
                createdAt: new Date('2023-01-01'),
                updatedAt: new Date('2023-01-02'),
            });
        });

        it('should handle repository with null/undefined optional fields', async () => {
            const mockRepository = {
                id: 'repo-123',
                giteaRepoId: 456,
                name: 'my-repo',
                fullName: 'user/my-repo',
                description: 'My repository',
                visibility: 'private',
                isFork: false,
                isTemplate: false,
                isArchived: false,
                isEmpty: false,
                size: 2048,
                starsCount: 10,
                forksCount: 3,
                watchersCount: 5,
                openIssuesCount: 2,
                defaultBranch: 'main',
                language: 'JavaScript',
                topics: null, // Test null topics
                htmlUrl: 'https://git.example.com/user/my-repo',
                cloneUrl: 'https://git.example.com/user/my-repo.git',
                sshUrl: '*******************:user/my-repo.git',
                isPublished: null, // Test null isPublished
                marketplaceItemId: 'item-456',
                hasMarketplaceMetadata: null, // Test null hasMarketplaceMetadata
                marketplaceMetadata: { category: 'frontend' },
                syncStatus: 'completed',
                lastSyncAt: new Date('2023-01-01'),
                giteaCreatedAt: new Date('2023-01-01'),
                giteaUpdatedAt: new Date('2023-01-02'),
                giteaPushedAt: new Date('2023-01-03'),
                createdAt: new Date('2023-01-01'),
                updatedAt: new Date('2023-01-02'),
            };

            repositorySyncService.getRepository.mockResolvedValue(mockRepository as any);

            const result = await controller.getRepository(repositoryId);

            expect(result.topics).toEqual([]); // Should default to []
            expect(result.isPublished).toBe(false); // Should default to false
            expect(result.hasMarketplaceMetadata).toBe(false); // Should default to false
        });

        it('should handle "not found" error', async () => {
            repositorySyncService.getRepository.mockRejectedValue(
                new Error('Repository not found')
            );

            await expect(controller.getRepository(repositoryId))
                .rejects.toThrow(NotFoundException);

            try {
                await controller.getRepository(repositoryId);
            } catch (error) {
                expect(error.message).toBe('Repository not found');
            }
        });

        it('should handle generic error', async () => {
            repositorySyncService.getRepository.mockRejectedValue(
                new Error('Database connection failed')
            );

            await expect(controller.getRepository(repositoryId))
                .rejects.toThrow(BadRequestException);

            try {
                await controller.getRepository(repositoryId);
            } catch (error) {
                expect(error.message).toBe('Failed to get repository: Database connection failed');
            }
        });
    });

    describe('Edge Cases for Branch Coverage', () => {
        it('should handle all null/undefined fields in createDeveloper response', async () => {
            const mockCreateRequest = {
                giteaUsername: 'testuser',
                giteaPassword: 'password123',
                autoProvision: false, // Test false path
            };

            const mockResult = {
                user: {
                    id: 'user-123',
                    name: 'Test User',
                    email: '<EMAIL>',
                    password: 'hashedpassword',
                    bio: 'Test bio',
                    roles: ['developer'],
                    isActive: true,
                    emailVerified: true,
                    emailVerificationToken: 'token123',
                    passwordResetToken: null,
                    passwordResetExpires: null,
                    twoFactorSecret: null,
                    twoFactorEnabled: false,
                    lastLoginAt: new Date('2023-01-01'),
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
                giteaProfile: {
                    id: undefined, // Test undefined id
                    giteaUserId: undefined, // Test undefined giteaUserId
                    giteaUsername: undefined, // Test undefined giteaUsername
                    giteaEmail: undefined, // Test undefined giteaEmail
                    giteaFullName: undefined, // Test undefined giteaFullName
                    giteaAvatarUrl: undefined, // Test undefined giteaAvatarUrl
                    isActive: undefined, // Test undefined isActive
                    syncStatus: undefined, // Test undefined syncStatus
                    lastSyncAt: undefined, // Test undefined lastSyncAt
                    publicRepositories: undefined, // Test undefined publicRepositories
                    privateRepositories: undefined, // Test undefined privateRepositories
                    giteaProfile: undefined, // Test undefined giteaProfile
                    syncErrors: undefined, // Test undefined syncErrors
                    createdAt: undefined, // Test undefined createdAt
                    updatedAt: undefined, // Test undefined updatedAt
                },
                isProvisioned: undefined, // Test undefined isProvisioned
                repositoryCount: undefined, // Test undefined repositoryCount
                publishedCount: undefined, // Test undefined publishedCount
            } as any;

            developerManagementService.createDeveloper.mockResolvedValue(mockResult);

            const result = await controller.createDeveloper(mockUser, mockCreateRequest);

            expect(developerManagementService.createDeveloper).toHaveBeenCalledWith({
                userId: 'user-123',
                giteaUsername: 'testuser',
                giteaPassword: 'password123',
                autoProvision: false, // Tests false path of autoProvision ?? true
            });

            // Test all the || fallbacks
            expect(result).toEqual({
                id: '', // giteaProfile?.id || ''
                userId: 'user-123',
                giteaUserId: 0, // giteaProfile?.giteaUserId || 0
                giteaUsername: '', // giteaProfile?.giteaUsername || ''
                giteaEmail: '', // giteaProfile?.giteaEmail || ''
                giteaFullName: '', // giteaProfile?.giteaFullName || ''
                giteaAvatarUrl: null, // giteaProfile?.giteaAvatarUrl || null
                isActive: false, // giteaProfile?.isActive || false
                isProvisioned: undefined, // result.isProvisioned (no fallback)
                syncStatus: 'pending', // giteaProfile?.syncStatus || 'pending'
                lastSyncAt: null, // giteaProfile?.lastSyncAt || null
                totalRepositories: undefined, // result.repositoryCount (no fallback)
                publicRepositories: 0, // giteaProfile?.publicRepositories || 0
                privateRepositories: 0, // giteaProfile?.privateRepositories || 0
                publishedRepositories: undefined, // result.publishedCount (no fallback)
                giteaProfile: null, // giteaProfile?.giteaProfile || null
                syncErrors: null, // giteaProfile?.syncErrors || null
                createdAt: expect.any(Date), // giteaProfile?.createdAt || new Date()
                updatedAt: expect.any(Date), // giteaProfile?.updatedAt || new Date()
            });
        });

        it('should handle repositories with all undefined optional fields', async () => {
            const mockRepositories = [
                {
                    id: 'repo-1',
                    giteaRepoId: 123,
                    name: 'test-repo',
                    fullName: 'user/test-repo',
                    description: 'Test repository',
                    visibility: 'public',
                    isFork: false,
                    isTemplate: false,
                    isArchived: false,
                    isEmpty: false,
                    size: 1024,
                    starsCount: 5,
                    forksCount: 2,
                    watchersCount: 3,
                    openIssuesCount: 1,
                    defaultBranch: 'main',
                    language: 'TypeScript',
                    topics: undefined, // Test undefined topics
                    htmlUrl: 'https://git.example.com/user/test-repo',
                    cloneUrl: 'https://git.example.com/user/test-repo.git',
                    sshUrl: '*******************:user/test-repo.git',
                    isPublished: undefined, // Test undefined isPublished
                    marketplaceItemId: 'item-123',
                    hasMarketplaceMetadata: undefined, // Test undefined hasMarketplaceMetadata
                    marketplaceMetadata: { category: 'tools' },
                    syncStatus: 'completed',
                    lastSyncAt: new Date('2023-01-01'),
                    giteaCreatedAt: new Date('2023-01-01'),
                    giteaUpdatedAt: new Date('2023-01-02'),
                    giteaPushedAt: new Date('2023-01-03'),
                    createdAt: new Date('2023-01-01'),
                    updatedAt: new Date('2023-01-02'),
                },
            ];

            repositorySyncService.getDeveloperRepositories.mockResolvedValue(mockRepositories as any);

            const result = await controller.getDeveloperRepositories(mockUser);

            expect(result[0].topics).toEqual([]); // repo.topics || []
            expect(result[0].isPublished).toBe(false); // repo.isPublished || false
            expect(result[0].hasMarketplaceMetadata).toBe(false); // repo.hasMarketplaceMetadata || false
        });

        it('should handle getRepository with all undefined optional fields', async () => {
            const mockRepository = {
                id: 'repo-123',
                giteaRepoId: 456,
                name: 'my-repo',
                fullName: 'user/my-repo',
                description: 'My repository',
                visibility: 'public',
                isFork: false,
                isTemplate: false,
                isArchived: false,
                isEmpty: false,
                size: 2048,
                starsCount: 10,
                forksCount: 3,
                watchersCount: 5,
                openIssuesCount: 2,
                defaultBranch: 'main',
                language: 'JavaScript',
                topics: undefined, // Test undefined topics
                htmlUrl: 'https://git.example.com/user/my-repo',
                cloneUrl: 'https://git.example.com/user/my-repo.git',
                sshUrl: '*******************:user/my-repo.git',
                isPublished: undefined, // Test undefined isPublished
                marketplaceItemId: 'item-456',
                hasMarketplaceMetadata: undefined, // Test undefined hasMarketplaceMetadata
                marketplaceMetadata: { category: 'frontend' },
                syncStatus: 'completed',
                lastSyncAt: new Date('2023-01-01'),
                giteaCreatedAt: new Date('2023-01-01'),
                giteaUpdatedAt: new Date('2023-01-02'),
                giteaPushedAt: new Date('2023-01-03'),
                createdAt: new Date('2023-01-01'),
                updatedAt: new Date('2023-01-02'),
            };

            repositorySyncService.getRepository.mockResolvedValue(mockRepository as any);

            const result = await controller.getRepository('repo-123');

            expect(result.topics).toEqual([]); // repo.topics || []
            expect(result.isPublished).toBe(false); // repo.isPublished || false
            expect(result.hasMarketplaceMetadata).toBe(false); // repo.hasMarketplaceMetadata || false
        });

        it('should handle getDeveloperProfile with all undefined optional fields', async () => {
            const mockResult = {
                user: { id: 'user-123' },
                giteaProfile: {
                    id: undefined,
                    giteaUserId: undefined,
                    giteaUsername: undefined,
                    giteaEmail: undefined,
                    giteaFullName: undefined,
                    giteaAvatarUrl: undefined,
                    isActive: undefined,
                    syncStatus: undefined,
                    lastSyncAt: undefined,
                    publicRepositories: undefined,
                    privateRepositories: undefined,
                    giteaProfile: undefined,
                    syncErrors: undefined,
                    createdAt: undefined,
                    updatedAt: undefined,
                },
                isProvisioned: undefined,
                repositoryCount: undefined,
                publishedCount: undefined,
            } as any;

            developerManagementService.getDeveloperProfile.mockResolvedValue(mockResult);

            const result = await controller.getDeveloperProfile(mockUser);

            expect(result.id).toBe('');
            expect(result.giteaUserId).toBe(0);
            expect(result.giteaUsername).toBe('');
            expect(result.giteaEmail).toBe('');
            expect(result.giteaFullName).toBe('');
            expect(result.giteaAvatarUrl).toBe(null);
            expect(result.isActive).toBe(false);
            expect(result.syncStatus).toBe('pending');
            expect(result.lastSyncAt).toBe(null);
            expect(result.publicRepositories).toBe(0);
            expect(result.privateRepositories).toBe(0);
            expect(result.giteaProfile).toBe(null);
            expect(result.syncErrors).toBe(null);
            expect(result.createdAt).toBeInstanceOf(Date);
            expect(result.updatedAt).toBeInstanceOf(Date);
        });

        it('should handle provisionGiteaAccount with all undefined optional fields', async () => {
            const mockResult = {
                user: { id: 'user-123' },
                giteaProfile: {
                    id: undefined,
                    giteaUserId: undefined,
                    giteaUsername: undefined,
                    giteaEmail: undefined,
                    giteaFullName: undefined,
                    giteaAvatarUrl: undefined,
                    isActive: undefined,
                    syncStatus: undefined,
                    lastSyncAt: undefined,
                    publicRepositories: undefined,
                    privateRepositories: undefined,
                    giteaProfile: undefined,
                    syncErrors: undefined,
                    createdAt: undefined,
                    updatedAt: undefined,
                },
                isProvisioned: undefined,
                repositoryCount: undefined,
                publishedCount: undefined,
            } as any;

            developerManagementService.provisionGiteaAccount.mockResolvedValue(mockResult);

            const result = await controller.provisionGiteaAccount(mockUser, { password: 'test123' });

            expect(result.id).toBe('');
            expect(result.giteaUserId).toBe(0);
            expect(result.giteaUsername).toBe('');
            expect(result.giteaEmail).toBe('');
            expect(result.giteaFullName).toBe('');
            expect(result.giteaAvatarUrl).toBe(null);
            expect(result.isActive).toBe(false);
            expect(result.syncStatus).toBe('pending');
            expect(result.lastSyncAt).toBe(null);
            expect(result.publicRepositories).toBe(0);
            expect(result.privateRepositories).toBe(0);
            expect(result.giteaProfile).toBe(null);
            expect(result.syncErrors).toBe(null);
            expect(result.createdAt).toBeInstanceOf(Date);
            expect(result.updatedAt).toBeInstanceOf(Date);
        });

        it('should handle syncDeveloperProfile with all undefined optional fields', async () => {
            const mockResult = {
                user: { id: 'user-123' },
                giteaProfile: {
                    id: undefined,
                    giteaUserId: undefined,
                    giteaUsername: undefined,
                    giteaEmail: undefined,
                    giteaFullName: undefined,
                    giteaAvatarUrl: undefined,
                    isActive: undefined,
                    syncStatus: undefined,
                    lastSyncAt: undefined,
                    publicRepositories: undefined,
                    privateRepositories: undefined,
                    giteaProfile: undefined,
                    syncErrors: undefined,
                    createdAt: undefined,
                    updatedAt: undefined,
                },
                isProvisioned: undefined,
                repositoryCount: undefined,
                publishedCount: undefined,
            } as any;

            developerManagementService.syncDeveloperProfile.mockResolvedValue(mockResult);

            const result = await controller.syncDeveloperProfile(mockUser);

            expect(result.id).toBe('');
            expect(result.giteaUserId).toBe(0);
            expect(result.giteaUsername).toBe('');
            expect(result.giteaEmail).toBe('');
            expect(result.giteaFullName).toBe('');
            expect(result.giteaAvatarUrl).toBe(null);
            expect(result.isActive).toBe(false);
            expect(result.syncStatus).toBe('pending');
            expect(result.lastSyncAt).toBe(null);
            expect(result.publicRepositories).toBe(0);
            expect(result.privateRepositories).toBe(0);
            expect(result.giteaProfile).toBe(null);
            expect(result.syncErrors).toBe(null);
            expect(result.createdAt).toBeInstanceOf(Date);
            expect(result.updatedAt).toBeInstanceOf(Date);
        });
    });
});