import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class UpdatePayoutSettingsRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  minimumAmount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  frequency?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  revenueShare?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  autoPayoutEnabled?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  holdingPeriod?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  btcpaySettings?: any;

}
