import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class PayoutRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  developerId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  amount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  btcAmount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  btcAddress?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  btcRate?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  requestType?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  requestedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  approvedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  processedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  completedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  btcpayInvoiceId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  transactionId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  networkFee?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  failureReason?: string;

}
