import { ApiProperty } from '@nestjs/swagger';

export class RepositorySyncResponseDto {
  @ApiProperty({ 
    description: 'Total number of repositories synced',
    example: 5 
  })
  synced: number;

  @ApiProperty({ 
    description: 'Number of new repositories created',
    example: 2 
  })
  created: number;

  @ApiProperty({ 
    description: 'Number of existing repositories updated',
    example: 3 
  })
  updated: number;

  @ApiProperty({ 
    description: 'List of error messages during sync',
    example: ['repo1: Failed to fetch metadata'],
    type: [String]
  })
  errors: string[];
} 