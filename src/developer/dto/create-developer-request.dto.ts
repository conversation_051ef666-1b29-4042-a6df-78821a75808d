import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

export class CreateDeveloperRequestDto {
  @ApiPropertyOptional({ 
    description: 'Gitea username (if different from email prefix)',
    example: 'john_doe' 
  })
  @IsOptional()
  @IsString()
  giteaUsername?: string;

  @ApiPropertyOptional({ 
    description: 'Gitea password for the account',
    example: 'SecurePassword123!' 
  })
  @IsOptional()
  @IsString()
  giteaPassword?: string;

  @ApiPropertyOptional({ 
    description: 'Automatically provision Gitea account',
    example: true,
    default: true 
  })
  @IsOptional()
  @IsBoolean()
  autoProvision?: boolean;
} 