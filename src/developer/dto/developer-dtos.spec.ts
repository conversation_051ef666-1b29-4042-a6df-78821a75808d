import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

// Import developer DTOs with 0% coverage
import { DeveloperAnalytics } from './developer-analytics.dto';
import { DeveloperPayoutSettings } from './developer-payout-settings.dto';
import { DeveloperPayout } from './developer-payout.dto';
import { GiteaWebhookPayload } from './gitea-webhook-payload.dto';
import { MarketplaceMetadataDto } from './marketplace-metadata.dto';
import { PayoutRequest } from './payout-request.dto';
import { PayoutSettings } from './payout-settings.dto';
import { PublishRepositoryRequest } from './publish-repository-request.dto';
import { RepositoryAnalytics } from './repository-analytics.dto';
import { RolePayoutSettings } from './role-payout-settings.dto';
import { UpdateDeveloperPayoutSettingsRequest } from './update-developer-payout-settings-request.dto';
import { UpdatePayoutSettingsRequest } from './update-payout-settings-request.dto';
import { UpdateRolePayoutSettingsRequest } from './update-role-payout-settings-request.dto';

describe('Developer DTOs', () => {
  describe('DeveloperAnalytics', () => {
    it('should be defined', () => {
      expect(DeveloperAnalytics).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(DeveloperAnalytics, {
        period: '30days',
        totalRevenue: 1500.50,
        totalDownloads: 1000,
        totalViews: 5000,
        activeItems: 5,
        topPerformingItems: [{ id: '1', name: 'Item 1' }],
        revenueByPeriod: [{ period: 'Jan', revenue: 500 }],
        categoryBreakdown: { tools: 3, plugins: 2 },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with empty object', async () => {
      const dto = plainToClass(DeveloperAnalytics, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate number fields', async () => {
      const dto = plainToClass(DeveloperAnalytics, {
        totalRevenue: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('totalRevenue');
    });
  });

  describe('DeveloperPayoutSettings', () => {
    it('should be defined', () => {
      expect(DeveloperPayoutSettings).toBeDefined();
    });

    it('should create instance', () => {
      const settings = new DeveloperPayoutSettings();
      expect(settings).toBeInstanceOf(DeveloperPayoutSettings);
    });
  });

  describe('DeveloperPayout', () => {
    it('should be defined', () => {
      expect(DeveloperPayout).toBeDefined();
    });

    it('should create instance', () => {
      const payout = new DeveloperPayout();
      expect(payout).toBeInstanceOf(DeveloperPayout);
    });
  });

  describe('GiteaWebhookPayload', () => {
    it('should be defined', () => {
      expect(GiteaWebhookPayload).toBeDefined();
    });

    it('should create instance', () => {
      const payload = new GiteaWebhookPayload();
      expect(payload).toBeInstanceOf(GiteaWebhookPayload);
    });
  });

  describe('MarketplaceMetadataDto', () => {
    it('should be defined', () => {
      expect(MarketplaceMetadataDto).toBeDefined();
    });

    it('should validate with valid complete data', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        name: 'My Awesome Plugin',
        description: 'A plugin that does amazing things',
        category: 'Development Tools',
        tags: ['productivity', 'development'],
        version: '1.0.0',
        author: 'John Doe',
        license: 'MIT',
        homepage: 'https://example.com',
        documentation: 'https://docs.example.com',
        screenshots: ['https://example.com/screenshot1.png'],
        pricing: {
          type: 'one_time',
          basePrice: 9.99,
          currency: 'USD',
        },
        requirements: {
          rsgliderVersion: '>=1.0.0',
          dependencies: ['nodejs', 'git'],
          platforms: ['windows', 'mac', 'linux'],
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal data', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        name: 'Simple Plugin',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate pricing enum', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        pricing: {
          type: 'invalid_type',
        },
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
    });

    it('should validate free pricing', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        pricing: {
          type: 'free',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate subscription pricing', async () => {
      const dto = plainToClass(MarketplaceMetadataDto, {
        pricing: {
          type: 'subscription',
          basePrice: 4.99,
          currency: 'EUR',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('PayoutRequest', () => {
    it('should be defined', () => {
      expect(PayoutRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new PayoutRequest();
      expect(request).toBeInstanceOf(PayoutRequest);
    });
  });

  describe('PayoutSettings', () => {
    it('should be defined', () => {
      expect(PayoutSettings).toBeDefined();
    });

    it('should create instance', () => {
      const settings = new PayoutSettings();
      expect(settings).toBeInstanceOf(PayoutSettings);
    });
  });

  describe('PublishRepositoryRequest', () => {
    it('should be defined', () => {
      expect(PublishRepositoryRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new PublishRepositoryRequest();
      expect(request).toBeInstanceOf(PublishRepositoryRequest);
    });
  });

  describe('RepositoryAnalytics', () => {
    it('should be defined', () => {
      expect(RepositoryAnalytics).toBeDefined();
    });

    it('should create instance', () => {
      const analytics = new RepositoryAnalytics();
      expect(analytics).toBeInstanceOf(RepositoryAnalytics);
    });
  });

  describe('RolePayoutSettings', () => {
    it('should be defined', () => {
      expect(RolePayoutSettings).toBeDefined();
    });

    it('should create instance', () => {
      const settings = new RolePayoutSettings();
      expect(settings).toBeInstanceOf(RolePayoutSettings);
    });
  });

  describe('UpdateDeveloperPayoutSettingsRequest', () => {
    it('should be defined', () => {
      expect(UpdateDeveloperPayoutSettingsRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new UpdateDeveloperPayoutSettingsRequest();
      expect(request).toBeInstanceOf(UpdateDeveloperPayoutSettingsRequest);
    });
  });

  describe('UpdatePayoutSettingsRequest', () => {
    it('should be defined', () => {
      expect(UpdatePayoutSettingsRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new UpdatePayoutSettingsRequest();
      expect(request).toBeInstanceOf(UpdatePayoutSettingsRequest);
    });
  });

  describe('UpdateRolePayoutSettingsRequest', () => {
    it('should be defined', () => {
      expect(UpdateRolePayoutSettingsRequest).toBeDefined();
    });

    it('should create instance', () => {
      const request = new UpdateRolePayoutSettingsRequest();
      expect(request).toBeInstanceOf(UpdateRolePayoutSettingsRequest);
    });
  });

  // Add tests for large 0% coverage DTOs
  describe('DeveloperRepositoryDto', () => {
    it('should be defined', () => {
      // Dynamic import to avoid IDE auto-formatting issues
      const DeveloperRepositoryDto = require('./developer-repository.dto').DeveloperRepositoryDto;
      expect(DeveloperRepositoryDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const DeveloperRepositoryDto = require('./developer-repository.dto').DeveloperRepositoryDto;
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 'repo-123',
        giteaRepoId: 456,
        name: 'test-repository',
        fullName: 'testuser/test-repository',
        description: 'A test repository',
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 1024,
        starsCount: 5,
        forksCount: 2,
        watchersCount: 3,
        openIssuesCount: 1,
        defaultBranch: 'main',
        language: 'TypeScript',
        topics: ['nodejs', 'typescript', 'api'],
        htmlUrl: 'https://gitea.example.com/testuser/test-repository',
        cloneUrl: 'https://gitea.example.com/testuser/test-repository.git',
        sshUrl: '*********************:testuser/test-repository.git',
        isPublished: true,
        marketplaceItemId: 'item-789',
        hasMarketplaceMetadata: true,
        marketplaceMetadata: { category: 'tools', price: 9.99 },
        syncStatus: 'completed',
        lastSyncAt: new Date('2023-01-01T12:00:00Z'),
        giteaCreatedAt: new Date('2023-01-01T10:00:00Z'),
        giteaUpdatedAt: new Date('2023-01-01T11:00:00Z'),
        giteaPushedAt: new Date('2023-01-01T11:30:00Z'),
        createdAt: new Date('2023-01-01T10:00:00Z'),
        updatedAt: new Date('2023-01-01T12:00:00Z'),
      });

      const errors = await validate(dto as any);
      expect(errors).toHaveLength(0);
    });

    it('should require string fields', async () => {
      const DeveloperRepositoryDto = require('./developer-repository.dto').DeveloperRepositoryDto;
      const dto = plainToClass(DeveloperRepositoryDto, {
        id: 123, // Should be string
        giteaRepoId: 456,
        name: 789, // Should be string
        fullName: 'user/repo',
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 0,
        starsCount: 0,
        forksCount: 0,
        watchersCount: 0,
        openIssuesCount: 0,
        defaultBranch: 'main',
        topics: [],
        htmlUrl: 'https://example.com',
        cloneUrl: 'https://example.com',
        sshUrl: '***************',
        isPublished: false,
        hasMarketplaceMetadata: false,
        syncStatus: 'pending',
        giteaCreatedAt: new Date(),
        giteaUpdatedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto as any);
      expect(errors.length).toBeGreaterThan(0);

      const stringFieldErrors = errors.filter(error =>
        ['id', 'name'].includes(error.property)
      );
      expect(stringFieldErrors.length).toBeGreaterThan(0);
    });

    it('should create instance', () => {
      const DeveloperRepositoryDto = require('./developer-repository.dto').DeveloperRepositoryDto;
      const dto = new DeveloperRepositoryDto();
      expect(dto).toBeInstanceOf(DeveloperRepositoryDto);
    });
  });

  describe('GiteaProfileDto', () => {
    it('should be defined', () => {
      const GiteaProfileDto = require('./gitea-profile.dto').GiteaProfileDto;
      expect(GiteaProfileDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const GiteaProfileDto = require('./gitea-profile.dto').GiteaProfileDto;
      const dto = plainToClass(GiteaProfileDto, {
        id: 'profile-123',
        userId: 'user-456',
        giteaUserId: 789,
        giteaUsername: 'testuser',
        giteaEmail: '<EMAIL>',
        giteaFullName: 'Test User',
        giteaAvatarUrl: 'https://gitea.example.com/avatars/testuser.png',
        isActive: true,
        isProvisioned: true,
        syncStatus: 'completed',
        lastSyncAt: '2023-01-01T12:00:00Z',
        totalRepositories: 5,
        publicRepositories: 3,
        privateRepositories: 2,
        publishedRepositories: 1,
        giteaProfile: {
          bio: 'A test developer',
          website: 'https://testuser.dev',
          location: 'Test City',
        },
        syncErrors: null,
        createdAt: '2023-01-01T10:00:00Z',
        updatedAt: '2023-01-01T12:00:00Z',
      });

      const errors = await validate(dto as any);
      expect(errors).toHaveLength(0);
    });

    it('should validate email format', async () => {
      const GiteaProfileDto = require('./gitea-profile.dto').GiteaProfileDto;
      const dto = plainToClass(GiteaProfileDto, {
        id: 'profile-123',
        userId: 'user-456',
        giteaUserId: 789,
        giteaUsername: 'testuser',
        giteaEmail: 'invalid-email', // Should be valid email
        giteaFullName: 'Test User',
        isActive: true,
        isProvisioned: true,
        syncStatus: 'completed',
        totalRepositories: 5,
        publicRepositories: 3,
        privateRepositories: 2,
        publishedRepositories: 1,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const errors = await validate(dto as any);
      expect(errors.length).toBeGreaterThan(0);

      const emailError = errors.find(error => error.property === 'giteaEmail');
      expect(emailError).toBeDefined();
    });

    it('should create instance', () => {
      const GiteaProfileDto = require('./gitea-profile.dto').GiteaProfileDto;
      const dto = new GiteaProfileDto();
      expect(dto).toBeInstanceOf(GiteaProfileDto);
    });
  });
});
