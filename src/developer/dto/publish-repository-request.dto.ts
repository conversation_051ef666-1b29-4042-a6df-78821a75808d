import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class PublishRepositoryRequest {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  category: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiProperty()
  @IsNotEmpty()
  pricing: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  visibility?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  customDescription?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  customName?: string;

}
