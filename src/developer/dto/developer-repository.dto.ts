import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

export class DeveloperRepositoryDto {
  @ApiProperty({ description: 'Repository ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Gitea repository ID' })
  @IsNumber()
  giteaRepoId: number;

  @ApiProperty({ description: 'Repository name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Full repository name' })
  @IsString()
  fullName: string;

  @ApiProperty({ description: 'Repository description', required: false })
  @IsOptional()
  @IsString()
  description?: string | null;

  @ApiProperty({ description: 'Repository visibility' })
  @IsString()
  visibility: 'public' | 'private';

  @ApiProperty({ description: 'Is fork' })
  @IsBoolean()
  isFork: boolean;

  @ApiProperty({ description: 'Is template' })
  @IsBoolean()
  isTemplate: boolean;

  @ApiProperty({ description: 'Is archived' })
  @IsBoolean()
  isArchived: boolean;

  @ApiProperty({ description: 'Is empty' })
  @IsBoolean()
  isEmpty: boolean;

  @ApiProperty({ description: 'Repository size' })
  @IsNumber()
  size: number;

  @ApiProperty({ description: 'Stars count' })
  @IsNumber()
  starsCount: number;

  @ApiProperty({ description: 'Forks count' })
  @IsNumber()
  forksCount: number;

  @ApiProperty({ description: 'Watchers count' })
  @IsNumber()
  watchersCount: number;

  @ApiProperty({ description: 'Open issues count' })
  @IsNumber()
  openIssuesCount: number;

  @ApiProperty({ description: 'Default branch' })
  @IsString()
  defaultBranch: string;

  @ApiProperty({ description: 'Primary language', required: false })
  @IsOptional()
  @IsString()
  language?: string | null;

  @ApiProperty({ description: 'Repository topics' })
  @IsArray()
  topics: string[];

  @ApiProperty({ description: 'HTML URL' })
  @IsString()
  htmlUrl: string;

  @ApiProperty({ description: 'Clone URL' })
  @IsString()
  cloneUrl: string;

  @ApiProperty({ description: 'SSH URL' })
  @IsString()
  sshUrl: string;

  @ApiProperty({ description: 'Is published to marketplace' })
  @IsBoolean()
  isPublished: boolean;

  @ApiProperty({ description: 'Marketplace item ID', required: false })
  @IsOptional()
  @IsString()
  marketplaceItemId?: string | null;

  @ApiProperty({ description: 'Has marketplace metadata' })
  @IsBoolean()
  hasMarketplaceMetadata: boolean;

  @ApiProperty({ description: 'Marketplace metadata', required: false })
  @IsOptional()
  marketplaceMetadata?: any;

  @ApiProperty({ description: 'Sync status' })
  @IsString()
  syncStatus: string;

  @ApiProperty({ description: 'Last sync timestamp', required: false })
  @IsOptional()
  lastSyncAt?: Date | null;

  @ApiProperty({ description: 'Gitea created timestamp' })
  giteaCreatedAt: Date;

  @ApiProperty({ description: 'Gitea updated timestamp' })
  giteaUpdatedAt: Date;

  @ApiProperty({ description: 'Gitea pushed timestamp', required: false })
  @IsOptional()
  giteaPushedAt?: Date | null;

  @ApiProperty({ description: 'Created timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated timestamp' })
  updatedAt: Date;
}
