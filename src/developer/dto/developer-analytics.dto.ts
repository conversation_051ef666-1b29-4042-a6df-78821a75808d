import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class DeveloperAnalytics {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  period?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalRevenue?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalDownloads?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalViews?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  activeItems?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  topPerformingItems?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  revenueByPeriod?: object[];

  @ApiProperty({ required: false })
  @IsOptional()
  categoryBreakdown?: object;

}
