import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';

class PricingDto {
  @ApiProperty({ 
    enum: ['free', 'one_time', 'subscription'],
    description: 'Pricing model type'
  })
  @IsEnum(['free', 'one_time', 'subscription'])
  type: 'free' | 'one_time' | 'subscription';

  @ApiPropertyOptional({ 
    description: 'Base price for paid items',
    example: 9.99 
  })
  @IsOptional()
  @IsNumber()
  basePrice?: number;

  @ApiPropertyOptional({ 
    description: 'Currency code',
    example: 'USD' 
  })
  @IsOptional()
  @IsString()
  currency?: string;
}

class RequirementsDto {
  @ApiPropertyOptional({ 
    description: 'Required RSGlider version',
    example: '>=1.0.0' 
  })
  @IsOptional()
  @IsString()
  rsgliderVersion?: string;

  @ApiPropertyOptional({ 
    description: 'Required dependencies',
    example: ['nodejs', 'git'],
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  dependencies?: string[];

  @ApiPropertyOptional({ 
    description: 'Supported platforms',
    example: ['windows', 'mac', 'linux'],
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  platforms?: string[];
}

export class MarketplaceMetadataDto {
  @ApiPropertyOptional({ 
    description: 'Display name for the marketplace item',
    example: 'My Awesome Plugin' 
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ 
    description: 'Description of the marketplace item',
    example: 'A plugin that does amazing things' 
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ 
    description: 'Category classification',
    example: 'Development Tools' 
  })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ 
    description: 'Tags for discovery',
    example: ['productivity', 'development'],
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Version number',
    example: '1.0.0' 
  })
  @IsOptional()
  @IsString()
  version?: string;

  @ApiPropertyOptional({ 
    description: 'Author name',
    example: 'John Doe' 
  })
  @IsOptional()
  @IsString()
  author?: string;

  @ApiPropertyOptional({ 
    description: 'License type',
    example: 'MIT' 
  })
  @IsOptional()
  @IsString()
  license?: string;

  @ApiPropertyOptional({ 
    description: 'Homepage URL',
    example: 'https://example.com' 
  })
  @IsOptional()
  @IsString()
  homepage?: string;

  @ApiPropertyOptional({ 
    description: 'Documentation URL',
    example: 'https://docs.example.com' 
  })
  @IsOptional()
  @IsString()
  documentation?: string;

  @ApiPropertyOptional({ 
    description: 'Screenshot URLs',
    example: ['https://example.com/screenshot1.png'],
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  screenshots?: string[];

  @ApiPropertyOptional({ 
    description: 'Pricing information',
    type: PricingDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => PricingDto)
  pricing?: PricingDto;

  @ApiPropertyOptional({ 
    description: 'System requirements',
    type: RequirementsDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => RequirementsDto)
  requirements?: RequirementsDto;
} 