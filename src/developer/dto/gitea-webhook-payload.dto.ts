import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class GiteaWebhookPayload {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  action?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  repository?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  pusher?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  commits?: object[];

  @ApiProperty({ required: false })
  @IsOptional()
  release?: object;

}
