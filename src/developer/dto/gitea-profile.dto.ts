import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsDateString, IsEmail, IsNumber, IsOptional, IsString } from 'class-validator';

export class GiteaProfileDto {
  @ApiProperty({ description: 'Profile ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'User ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: 'Gitea user ID' })
  @IsNumber()
  giteaUserId: number;

  @ApiProperty({ description: 'Gitea username' })
  @IsString()
  giteaUsername: string;

  @ApiProperty({ description: 'Gitea email' })
  @IsEmail()
  giteaEmail: string;

  @ApiProperty({ description: 'Gitea full name' })
  @IsString()
  giteaFullName: string;

  @ApiProperty({ description: 'Gitea avatar URL', required: false })
  @IsOptional()
  @IsString()
  giteaAvatarUrl?: string | null;

  @ApiProperty({ description: 'Is profile active' })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({ description: 'Is Gitea account provisioned' })
  @IsBoolean()
  isProvisioned: boolean;

  @ApiProperty({ description: 'Sync status' })
  @IsString()
  syncStatus: string;

  @ApiProperty({ description: 'Last sync timestamp', required: false })
  @IsOptional()
  @IsDateString()
  lastSyncAt?: Date | null;

  @ApiProperty({ description: 'Total repositories count' })
  @IsNumber()
  totalRepositories: number;

  @ApiProperty({ description: 'Public repositories count' })
  @IsNumber()
  publicRepositories: number;

  @ApiProperty({ description: 'Private repositories count' })
  @IsNumber()
  privateRepositories: number;

  @ApiProperty({ description: 'Published repositories count' })
  @IsNumber()
  publishedRepositories: number;

  @ApiProperty({ description: 'Gitea profile data', required: false })
  @IsOptional()
  giteaProfile?: any;

  @ApiProperty({ description: 'Sync errors', required: false })
  @IsOptional()
  @IsArray()
  syncErrors?: string[] | null;

  @ApiProperty({ description: 'Created timestamp' })
  @IsDateString()
  createdAt: Date;

  @ApiProperty({ description: 'Updated timestamp' })
  @IsDateString()
  updatedAt: Date;
}
