import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class Pagination {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  page?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  limit?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  total?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalPages?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  hasNext?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  hasPrev?: boolean;

}
