import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class BTCPaySettings {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  serverUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  storeId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  webhookSecret?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  networkFeePolicy?: string;

}
