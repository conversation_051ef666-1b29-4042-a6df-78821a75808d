import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';

export class CreateDeviceRequestDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  deviceName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  deviceType: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiProperty({ required: false, type: Object })
  @IsOptional()
  @IsObject()
  deviceInfo?: object;
} 