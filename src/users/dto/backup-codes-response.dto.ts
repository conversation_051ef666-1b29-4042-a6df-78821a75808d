import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class BackupCodesResponse {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  codes?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  generatedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  usedCodes?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  remainingCodes?: number;

}
