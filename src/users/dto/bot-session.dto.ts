import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class BotSession {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sessionName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  botType?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  desktopSessionId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  deviceName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  configuration?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  statistics?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastActivityAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  canTransfer?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  subscriptionTier?: string;

}
