import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsDateString, IsObject, IsOptional, IsString, IsUUID } from 'class-validator';

export class DeviceDto {
  @ApiProperty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsUUID()
  userId: string;

  @ApiProperty()
  @IsString()
  deviceName: string;

  @ApiProperty()
  @IsString()
  deviceType: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiProperty({ required: false, type: Object })
  @IsOptional()
  @IsObject()
  deviceInfo?: object;

  @ApiProperty()
  @IsBoolean()
  isTrusted: boolean;

  @ApiProperty()
  @IsDateString()
  registeredAt: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  lastActivityAt?: string;

  @ApiProperty()
  @IsDateString()
  createdAt: string;

  @ApiProperty()
  @IsDateString()
  updatedAt: string;
} 