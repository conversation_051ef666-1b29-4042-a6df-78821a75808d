import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class DesktopSession {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  deviceInfo?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  registeredAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastActivityAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isCurrent?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  activeBotSessions?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxBotSessions?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  transferable?: boolean;

}
