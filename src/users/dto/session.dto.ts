import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsObject, IsOptional, IsString } from 'class-validator';

export class SessionDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  platform: string;

  @ApiProperty({ required: false, type: Object })
  @IsOptional()
  @IsObject()
  deviceInfo?: object;

  @ApiProperty({ required: false, type: Object })
  @IsOptional()
  @IsObject()
  location?: object;

  @ApiProperty()
  @IsString()
  expiresAt: string;

  @ApiProperty()
  @IsBoolean()
  isActive: boolean;

  @ApiProperty()
  @IsBoolean()
  isCurrent: boolean;

  @ApiProperty()
  @IsBoolean()
  requiresVerification: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastActivityAt?: string;

  @ApiProperty()
  @IsString()
  createdAt: string;

  @ApiProperty()
  @IsString()
  updatedAt: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  revokedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  revokedByIp?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  revokedReason?: string;
} 