import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class SessionLimits {
  @ApiProperty({ required: false })
  @IsOptional()
  webSessions?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  desktopSessions?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  botSessions?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  subscriptionTier?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  addons?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  upgradeRequired?: boolean;

}
