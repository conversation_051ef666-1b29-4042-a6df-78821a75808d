import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

import { BotSession } from './bot-session.dto';
import { OrderItem } from './order-item.dto';
import { DesktopSession } from './desktop-session.dto';

describe('Users DTOs Part 2', () => {
  describe('BotSession', () => {
    it('should be defined', () => {
      expect(BotSession).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(BotSession, {
        id: 'bot-session-123',
        sessionName: 'Test Bot Session',
        botType: 'automation',
        desktopSessionId: 'desktop-session-456',
        deviceName: 'Test Device',
        status: 'active',
        configuration: { setting1: 'value1', setting2: 'value2' },
        statistics: { runs: 10, success: 8, failures: 2 },
        lastActivityAt: '2023-01-01T12:00:00Z',
        canTransfer: true,
        subscriptionTier: 'premium',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal data (all fields optional)', async () => {
      const dto = plainToClass(BotSession, {
        sessionName: 'Minimal Bot Session',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object (all fields optional)', async () => {
      const dto = plainToClass(BotSession, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate string fields', async () => {
      const dto = plainToClass(BotSession, {
        id: 123,
        sessionName: 456,
        botType: 789,
        desktopSessionId: 101112,
        deviceName: 131415,
        status: 161718,
        lastActivityAt: 192021,
        subscriptionTier: 222324,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const stringFields = [
        'id', 'sessionName', 'botType', 'desktopSessionId', 
        'deviceName', 'status', 'lastActivityAt', 'subscriptionTier'
      ];
      
      const stringErrors = errors.filter(error => 
        stringFields.includes(error.property)
      );
      expect(stringErrors.length).toBeGreaterThan(0);
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(BotSession, {
        canTransfer: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const canTransferError = errors.find(error => error.property === 'canTransfer');
      expect(canTransferError).toBeDefined();
    });

    it('should create instance', () => {
      const dto = new BotSession();
      expect(dto).toBeInstanceOf(BotSession);
    });

    it('should handle object properties', () => {
      const dto = new BotSession();
      dto.configuration = { key: 'value' };
      dto.statistics = { count: 5 };
      
      expect(dto.configuration).toEqual({ key: 'value' });
      expect(dto.statistics).toEqual({ count: 5 });
    });
  });

  describe('OrderItem', () => {
    it('should be defined', () => {
      expect(OrderItem).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(OrderItem, {
        id: 'order-item-123',
        itemId: 'item-456',
        itemName: 'Test Software',
        developerId: 'dev-789',
        developerName: 'Test Developer',
        quantity: 2,
        unitPrice: 29.99,
        totalPrice: 59.98,
        currency: 'USD',
        downloadUrl: 'https://example.com/download/item-456',
        licenseKey: 'LICENSE-KEY-123-456-789',
        accessGranted: true,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal data (all fields optional)', async () => {
      const dto = plainToClass(OrderItem, {
        itemName: 'Minimal Item',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object (all fields optional)', async () => {
      const dto = plainToClass(OrderItem, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate string fields', async () => {
      const dto = plainToClass(OrderItem, {
        id: 123,
        itemId: 456,
        itemName: 789,
        developerId: 101112,
        developerName: 131415,
        currency: 161718,
        downloadUrl: 192021,
        licenseKey: 222324,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const stringFields = [
        'id', 'itemId', 'itemName', 'developerId', 'developerName',
        'currency', 'downloadUrl', 'licenseKey'
      ];
      
      const stringErrors = errors.filter(error => 
        stringFields.includes(error.property)
      );
      expect(stringErrors.length).toBeGreaterThan(0);
    });

    it('should validate number fields', async () => {
      const dto = plainToClass(OrderItem, {
        quantity: 'not-a-number',
        unitPrice: 'not-a-number',
        totalPrice: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const numberFields = ['quantity', 'unitPrice', 'totalPrice'];
      const numberErrors = errors.filter(error => 
        numberFields.includes(error.property)
      );
      expect(numberErrors.length).toBeGreaterThan(0);
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(OrderItem, {
        accessGranted: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const accessGrantedError = errors.find(error => error.property === 'accessGranted');
      expect(accessGrantedError).toBeDefined();
    });

    it('should create instance', () => {
      const dto = new OrderItem();
      expect(dto).toBeInstanceOf(OrderItem);
    });

    it('should handle numeric calculations', () => {
      const dto = new OrderItem();
      dto.quantity = 3;
      dto.unitPrice = 15.50;
      dto.totalPrice = dto.quantity * dto.unitPrice;
      
      expect(dto.totalPrice).toBe(46.5);
    });
  });

  describe('DesktopSession', () => {
    it('should be defined', () => {
      expect(DesktopSession).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(DesktopSession, {
        id: 'desktop-session-123',
        platform: 'Windows',
        deviceInfo: { 
          os: 'Windows 11', 
          version: '22H2', 
          architecture: 'x64',
          memory: '16GB'
        },
        registeredAt: '2023-01-01T10:00:00Z',
        lastActivityAt: '2023-01-01T12:00:00Z',
        isActive: true,
        isCurrent: false,
        activeBotSessions: 2,
        maxBotSessions: 5,
        transferable: true,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal data (all fields optional)', async () => {
      const dto = plainToClass(DesktopSession, {
        platform: 'macOS',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object (all fields optional)', async () => {
      const dto = plainToClass(DesktopSession, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate string fields', async () => {
      const dto = plainToClass(DesktopSession, {
        id: 123,
        platform: 456,
        registeredAt: 789,
        lastActivityAt: 101112,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const stringFields = ['id', 'platform', 'registeredAt', 'lastActivityAt'];
      const stringErrors = errors.filter(error => 
        stringFields.includes(error.property)
      );
      expect(stringErrors.length).toBeGreaterThan(0);
    });

    it('should validate number fields', async () => {
      const dto = plainToClass(DesktopSession, {
        activeBotSessions: 'not-a-number',
        maxBotSessions: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const numberFields = ['activeBotSessions', 'maxBotSessions'];
      const numberErrors = errors.filter(error => 
        numberFields.includes(error.property)
      );
      expect(numberErrors.length).toBeGreaterThan(0);
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(DesktopSession, {
        isActive: 'not-a-boolean',
        isCurrent: 'not-a-boolean',
        transferable: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const booleanFields = ['isActive', 'isCurrent', 'transferable'];
      const booleanErrors = errors.filter(error => 
        booleanFields.includes(error.property)
      );
      expect(booleanErrors.length).toBeGreaterThan(0);
    });

    it('should create instance', () => {
      const dto = new DesktopSession();
      expect(dto).toBeInstanceOf(DesktopSession);
    });

    it('should handle device info object', () => {
      const dto = new DesktopSession();
      dto.deviceInfo = { 
        cpu: 'Intel i7', 
        gpu: 'NVIDIA RTX 3080',
        ram: '32GB'
      };
      
      expect(dto.deviceInfo).toEqual({ 
        cpu: 'Intel i7', 
        gpu: 'NVIDIA RTX 3080',
        ram: '32GB'
      });
    });

    it('should handle session limits', () => {
      const dto = new DesktopSession();
      dto.activeBotSessions = 3;
      dto.maxBotSessions = 10;
      
      expect(dto.activeBotSessions).toBeLessThan(dto.maxBotSessions);
    });
  });
});
