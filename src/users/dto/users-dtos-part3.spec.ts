import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

import { RevenueSummary } from './revenue-summary.dto';
import { WebSession } from './web-session.dto';
import { FeatureAccessCheck } from './feature-access-check.dto';

describe('Users DTOs Part 3', () => {
  describe('RevenueSummary', () => {
    it('should be defined', () => {
      expect(RevenueSummary).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(RevenueSummary, {
        period: '2023-Q1',
        totalRevenue: 1500.00,
        platformFee: 150.00,
        netRevenue: 1350.00,
        pendingPayout: 500.00,
        nextPayoutDate: '2023-04-01',
        revenueShare: {
          developer: 0.9,
          platform: 0.1,
          breakdown: {
            sales: 1200.00,
            subscriptions: 300.00
          }
        },
        payoutThreshold: 100.00,
        currency: 'USD',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal data (all fields optional)', async () => {
      const dto = plainToClass(RevenueSummary, {
        totalRevenue: 100.00,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object (all fields optional)', async () => {
      const dto = plainToClass(RevenueSummary, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate string fields', async () => {
      const dto = plainToClass(RevenueSummary, {
        period: 123,
        nextPayoutDate: 456,
        currency: 789,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const stringFields = ['period', 'nextPayoutDate', 'currency'];
      const stringErrors = errors.filter(error => 
        stringFields.includes(error.property)
      );
      expect(stringErrors.length).toBeGreaterThan(0);
    });

    it('should validate number fields', async () => {
      const dto = plainToClass(RevenueSummary, {
        totalRevenue: 'not-a-number',
        platformFee: 'not-a-number',
        netRevenue: 'not-a-number',
        pendingPayout: 'not-a-number',
        payoutThreshold: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const numberFields = [
        'totalRevenue', 'platformFee', 'netRevenue', 
        'pendingPayout', 'payoutThreshold'
      ];
      const numberErrors = errors.filter(error => 
        numberFields.includes(error.property)
      );
      expect(numberErrors.length).toBeGreaterThan(0);
    });

    it('should create instance', () => {
      const dto = new RevenueSummary();
      expect(dto).toBeInstanceOf(RevenueSummary);
    });

    it('should handle revenue calculations', () => {
      const dto = new RevenueSummary();
      dto.totalRevenue = 1000.00;
      dto.platformFee = 100.00;
      dto.netRevenue = dto.totalRevenue - dto.platformFee;
      
      expect(dto.netRevenue).toBe(900.00);
    });

    it('should handle revenue share object', () => {
      const dto = new RevenueSummary();
      dto.revenueShare = {
        developer: 0.85,
        platform: 0.15,
        details: { tier: 'premium' }
      };
      
      expect(dto.revenueShare).toEqual({
        developer: 0.85,
        platform: 0.15,
        details: { tier: 'premium' }
      });
    });
  });

  describe('WebSession', () => {
    it('should be defined', () => {
      expect(WebSession).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(WebSession, {
        id: 'web-session-123',
        platform: 'Chrome/Windows',
        deviceInfo: {
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          screen: { width: 1920, height: 1080 },
          timezone: 'America/New_York'
        },
        location: {
          country: 'US',
          region: 'NY',
          city: 'New York',
          ip: '***********'
        },
        createdAt: '2023-01-01T10:00:00Z',
        lastActivityAt: '2023-01-01T12:00:00Z',
        expiresAt: '2023-01-02T10:00:00Z',
        isActive: true,
        isCurrent: true,
        requiresVerification: false,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal data (all fields optional)', async () => {
      const dto = plainToClass(WebSession, {
        platform: 'Firefox/macOS',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object (all fields optional)', async () => {
      const dto = plainToClass(WebSession, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate string fields', async () => {
      const dto = plainToClass(WebSession, {
        id: 123,
        platform: 456,
        createdAt: 789,
        lastActivityAt: 101112,
        expiresAt: 131415,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const stringFields = [
        'id', 'platform', 'createdAt', 'lastActivityAt', 'expiresAt'
      ];
      const stringErrors = errors.filter(error => 
        stringFields.includes(error.property)
      );
      expect(stringErrors.length).toBeGreaterThan(0);
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(WebSession, {
        isActive: 'not-a-boolean',
        isCurrent: 'not-a-boolean',
        requiresVerification: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const booleanFields = ['isActive', 'isCurrent', 'requiresVerification'];
      const booleanErrors = errors.filter(error => 
        booleanFields.includes(error.property)
      );
      expect(booleanErrors.length).toBeGreaterThan(0);
    });

    it('should create instance', () => {
      const dto = new WebSession();
      expect(dto).toBeInstanceOf(WebSession);
    });

    it('should handle device info object', () => {
      const dto = new WebSession();
      dto.deviceInfo = {
        browser: 'Chrome',
        version: '120.0.0.0',
        os: 'Windows 11'
      };
      
      expect(dto.deviceInfo).toEqual({
        browser: 'Chrome',
        version: '120.0.0.0',
        os: 'Windows 11'
      });
    });

    it('should handle location object', () => {
      const dto = new WebSession();
      dto.location = {
        country: 'CA',
        region: 'ON',
        city: 'Toronto',
        coordinates: { lat: 43.6532, lng: -79.3832 }
      };
      
      expect(dto.location).toEqual({
        country: 'CA',
        region: 'ON',
        city: 'Toronto',
        coordinates: { lat: 43.6532, lng: -79.3832 }
      });
    });
  });

  describe('FeatureAccessCheck', () => {
    it('should be defined', () => {
      expect(FeatureAccessCheck).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(FeatureAccessCheck, {
        featureKey: 'premium-automation',
        hasAccess: true,
        reason: 'Active premium subscription',
        maxInstances: 10,
        availableInstances: 7,
        nextAvailableAt: '2023-01-02T00:00:00Z',
        upgradeOptions: [
          { tier: 'enterprise', price: 99.99 },
          { tier: 'unlimited', price: 199.99 }
        ],
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal data (all fields optional)', async () => {
      const dto = plainToClass(FeatureAccessCheck, {
        featureKey: 'basic-feature',
        hasAccess: false,
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object (all fields optional)', async () => {
      const dto = plainToClass(FeatureAccessCheck, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate string fields', async () => {
      const dto = plainToClass(FeatureAccessCheck, {
        featureKey: 123,
        reason: 456,
        nextAvailableAt: 789,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const stringFields = ['featureKey', 'reason', 'nextAvailableAt'];
      const stringErrors = errors.filter(error => 
        stringFields.includes(error.property)
      );
      expect(stringErrors.length).toBeGreaterThan(0);
    });

    it('should validate number fields', async () => {
      const dto = plainToClass(FeatureAccessCheck, {
        maxInstances: 'not-a-number',
        availableInstances: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const numberFields = ['maxInstances', 'availableInstances'];
      const numberErrors = errors.filter(error => 
        numberFields.includes(error.property)
      );
      expect(numberErrors.length).toBeGreaterThan(0);
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(FeatureAccessCheck, {
        hasAccess: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const hasAccessError = errors.find(error => error.property === 'hasAccess');
      expect(hasAccessError).toBeDefined();
    });

    it('should validate array fields', async () => {
      const dto = plainToClass(FeatureAccessCheck, {
        upgradeOptions: 'not-an-array',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const upgradeOptionsError = errors.find(error => error.property === 'upgradeOptions');
      expect(upgradeOptionsError).toBeDefined();
    });

    it('should create instance', () => {
      const dto = new FeatureAccessCheck();
      expect(dto).toBeInstanceOf(FeatureAccessCheck);
    });

    it('should handle access logic', () => {
      const dto = new FeatureAccessCheck();
      dto.maxInstances = 5;
      dto.availableInstances = 2;
      dto.hasAccess = dto.availableInstances > 0;
      
      expect(dto.hasAccess).toBe(true);
    });

    it('should handle upgrade options array', () => {
      const dto = new FeatureAccessCheck();
      dto.upgradeOptions = [
        { name: 'Pro', price: 29.99 },
        { name: 'Enterprise', price: 99.99 }
      ];
      
      expect(dto.upgradeOptions).toHaveLength(2);
      expect(dto.upgradeOptions[0].name).toBe('Pro');
    });
  });
});
