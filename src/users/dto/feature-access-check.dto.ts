import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class FeatureAccessCheck {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  featureKey?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  hasAccess?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxInstances?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  availableInstances?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nextAvailableAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  upgradeOptions?: any[];

}
