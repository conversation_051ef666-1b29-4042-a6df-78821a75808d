import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class RevenueSummary {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  period?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalRevenue?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  platformFee?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  netRevenue?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  pendingPayout?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  nextPayoutDate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  revenueShare?: object;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  payoutThreshold?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currency?: string;

}
