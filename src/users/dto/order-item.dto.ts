import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class OrderItem {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  itemId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  itemName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  developerId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  developerName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  quantity?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  unitPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  downloadUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  licenseKey?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  accessGranted?: boolean;

}
