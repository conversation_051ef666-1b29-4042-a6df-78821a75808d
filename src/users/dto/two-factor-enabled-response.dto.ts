import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class TwoFactorEnabledResponse {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  enabled?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  backupCodes?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  message?: string;

}
