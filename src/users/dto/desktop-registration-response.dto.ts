import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class DesktopRegistrationResponse {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  registered?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxBotSessions?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  subscriptionLimits?: any;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  transfersRemaining?: number;

}
