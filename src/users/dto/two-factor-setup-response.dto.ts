import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class TwoFactorSetupResponse {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  secret?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  qrCodeUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  backupCodes?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  manualEntryKey?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  issuer?: string;

}
