import { BadRequestException, ConflictException, Inject, Injectable, Logger, NotFoundException, UnauthorizedException } from '@nestjs/common';

import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { and, arrayContains, eq, ilike, inArray, or, sql } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { Request } from 'express';
import * as qrcode from 'qrcode';
import * as speakeasy from 'speakeasy';
import { DeveloperManagementService } from '../common/services/developer-management.service.js';
import { PasswordSyncService } from '../common/services/password-sync.service.js';
import { devices } from '../database/schema/devices.schema.js';
import { roles, userRoles, users } from '../database/schema/index.js';
import { userSessions } from '../database/schema/user-sessions.schema.js';
import { EmailService } from '../email/email.service.js';
import { CreateDeviceRequestDto } from './dto/create-device-request.dto.js';
import { DeviceDto } from './dto/device.dto.js';
import { SessionDto } from './dto/session.dto.js';
import { TwoFactorEnabledResponse } from './dto/two-factor-enabled-response.dto.js';
import { TwoFactorSetupResponse } from './dto/two-factor-setup-response.dto.js';
import { UpdateUserRequest } from './dto/update-user-request.dto.js';
import { User } from './dto/user.dto.js';


@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @Inject('DB') private readonly db: PostgresJsDatabase<typeof import('../database/schema/index.js')>,
    private readonly emailService: EmailService,
    private readonly developerManagementService: DeveloperManagementService,
    private readonly passwordSyncService: PasswordSyncService,
  ) { }

  async findById(userId: string): Promise<User> {
    return this.getCurrentUser(userId);
  }

  async findByEmail(email: string): Promise<User | null> {
    const [user] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        roles: users.roles,
        twoFactorEnabled: users.twoFactorEnabled,
        emailVerified: users.emailVerified,
        lastLoginAt: users.lastLoginAt,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        isActive: users.isActive,
      })
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (!user) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.name?.split(' ')[0],
      lastName: user.name?.split(' ').slice(1).join(' '),
      roles: user.roles,
      twoFactorEnabled: user.twoFactorEnabled,
      emailVerified: user.emailVerified,
      status: user.isActive ? 'active' : 'inactive',
      lastLoginAt: user.lastLoginAt?.toISOString(),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    };
  }

  async getCurrentUser(userId: string): Promise<User> {
    const [user] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        roles: users.roles,
        twoFactorEnabled: users.twoFactorEnabled,
        emailVerified: users.emailVerified,
        lastLoginAt: users.lastLoginAt,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        isActive: users.isActive,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.name?.split(' ')[0],
      lastName: user.name?.split(' ').slice(1).join(' '),
      roles: user.roles,
      twoFactorEnabled: user.twoFactorEnabled,
      emailVerified: user.emailVerified,
      status: user.isActive ? 'active' : 'inactive',
      lastLoginAt: user.lastLoginAt?.toISOString(),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    };
  }

  async updateProfile(userId: string, updateData: { name?: string; email?: string; bio?: string }): Promise<User> {
    // Get current user data
    const [currentUser] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        bio: users.bio,
        roles: users.roles,
        twoFactorEnabled: users.twoFactorEnabled,
        emailVerified: users.emailVerified,
        lastLoginAt: users.lastLoginAt,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        isActive: users.isActive,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!currentUser) {
      throw new NotFoundException('User not found');
    }

    // SECURITY CHECK: User must verify their current email before changing it
    if (updateData.email && updateData.email !== currentUser.email) {
      if (!currentUser.emailVerified) {
        throw new BadRequestException('You must verify your current email address before changing it. Please check your inbox for a verification email.');
      }

      // Check if the new email already exists
      const [existingUser] = await this.db
        .select({ id: users.id })
        .from(users)
        .where(and(eq(users.email, updateData.email), sql`${users.id} != ${userId}`))
        .limit(1);

      if (existingUser) {
        throw new ConflictException('Email already exists');
      }

      // Proper email change verification flow
      // Store the new email as pending and generate verification token
      const verificationToken = crypto.randomBytes(32).toString('hex');
      const tokenExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Update user with pending email change
      await this.db
        .update(users)
        .set({
          pendingEmail: updateData.email,
          emailChangeToken: verificationToken,
          emailChangeExpires: tokenExpires,
          name: updateData.name ?? currentUser.name,
          bio: updateData.bio ?? currentUser.bio,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userId));

      // Send verification email to new address
      await this.emailService.sendEmailChangeVerification(
        updateData.email,
        currentUser.name || 'User',
        verificationToken
      );

      // Send notification to old email address
      await this.emailService.sendEmailChangeNotification(
        currentUser.email,
        currentUser.name || 'User',
        updateData.email
      );

      // Return current user data (email not changed yet)
      const fullName = updateData.name || currentUser.name;
      return {
        id: currentUser.id,
        email: currentUser.email, // Keep current email
        firstName: fullName?.split(' ')[0],
        lastName: fullName?.split(' ').slice(1).join(' '),
        bio: updateData.bio || currentUser.bio,
        roles: currentUser.roles,
        twoFactorEnabled: currentUser.twoFactorEnabled,
        emailVerified: currentUser.emailVerified,
        status: currentUser.isActive ? 'active' : 'inactive',
        lastLoginAt: currentUser.lastLoginAt?.toISOString(),
        createdAt: currentUser.createdAt.toISOString(),
        updatedAt: new Date().toISOString(), // Updated now since we modified pending email
      };
    }

    // If we reach here, email is not being changed, so update other fields normally
    const [updatedUser] = await this.db
      .update(users)
      .set({
        name: updateData.name,
        bio: updateData.bio,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning({
        id: users.id,
        email: users.email,
        name: users.name,
        bio: users.bio,
        roles: users.roles,
        twoFactorEnabled: users.twoFactorEnabled,
        emailVerified: users.emailVerified,
        lastLoginAt: users.lastLoginAt,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        isActive: users.isActive,
      });

    if (!updatedUser) {
      throw new NotFoundException('User not found');
    }

    return {
      id: updatedUser.id,
      email: updatedUser.email,
      firstName: updatedUser.name?.split(' ')[0],
      lastName: updatedUser.name?.split(' ').slice(1).join(' '),
      bio: updatedUser.bio,
      roles: updatedUser.roles,
      twoFactorEnabled: updatedUser.twoFactorEnabled,
      emailVerified: updatedUser.emailVerified,
      status: updatedUser.isActive ? 'active' : 'inactive',
      lastLoginAt: updatedUser.lastLoginAt?.toISOString(),
      createdAt: updatedUser.createdAt.toISOString(),
      updatedAt: updatedUser.updatedAt.toISOString(),
    };
  }

  async updateCurrentUser(userId: string, updateData: UpdateUserRequest): Promise<User> {
    const { firstName, lastName, avatar } = updateData;

    // Get current user to preserve existing name parts if not provided
    const [currentUser] = await this.db
      .select({ name: users.name })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!currentUser) {
      throw new NotFoundException('User not found');
    }

    // Parse current name parts
    const currentFirstName = currentUser.name?.split(' ')[0] || '';
    const currentLastName = currentUser.name?.split(' ').slice(1).join(' ') || '';

    // Use provided values only if they are meaningful (not empty/whitespace), otherwise preserve current values
    const finalFirstName = firstName && firstName.trim() ? firstName.trim() : currentFirstName;
    const finalLastName = lastName && lastName.trim() ? lastName.trim() : currentLastName;

    // Combine first and last name
    const name = [finalFirstName, finalLastName].filter(Boolean).join(' ') || undefined;

    const [updatedUser] = await this.db
      .update(users)
      .set({
        name,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning({
        id: users.id,
        email: users.email,
        name: users.name,
        roles: users.roles,
        twoFactorEnabled: users.twoFactorEnabled,
        emailVerified: users.emailVerified,
        lastLoginAt: users.lastLoginAt,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
        isActive: users.isActive,
      });

    if (!updatedUser) {
      throw new NotFoundException('User not found');
    }

    return {
      id: updatedUser.id,
      email: updatedUser.email,
      firstName: updatedUser.name?.split(' ')[0],
      lastName: updatedUser.name?.split(' ').slice(1).join(' '),
      avatar, // Note: avatar storage not implemented yet
      roles: updatedUser.roles,
      twoFactorEnabled: updatedUser.twoFactorEnabled,
      emailVerified: updatedUser.emailVerified,
      status: updatedUser.isActive ? 'active' : 'inactive',
      lastLoginAt: updatedUser.lastLoginAt?.toISOString(),
      createdAt: updatedUser.createdAt.toISOString(),
      updatedAt: updatedUser.updatedAt.toISOString(),
    };
  }

  async changePassword(userId: string, passwordData: { currentPassword: string; newPassword: string }, req?: Request): Promise<{ message: string }> {
    // Get user with current password and email
    try {
      const [user] = await this.db
        .select({
          id: users.id,
          email: users.email,
          name: users.name,
          password: users.password,
        })
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(passwordData.currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new BadRequestException('Current password is incorrect');
      }

      // Hash new password
      const hashedNewPassword = await bcrypt.hash(passwordData.newPassword, 12);

      // Update password
      await this.db
        .update(users)
        .set({
          password: hashedNewPassword,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userId));

      // Send password changed notification email
      const deviceInfo = {
        userAgent: req?.get('User-Agent') || 'Unknown device',
        ipAddress: req?.ip || 'Unknown IP',
      };

      await this.emailService.sendPasswordChanged(user.email, user.name || 'User', deviceInfo);

      // Sync password to Gitea (async, don't block the response)
      this.passwordSyncService.syncPasswordToGitea(userId, passwordData.newPassword)
        .then(result => {
          if (result.success) {
            this.logger.log(`Password synced to Gitea for user ${userId}: ${result.giteaUsername || 'no Gitea account'}`);
          } else {
            this.logger.warn(`Failed to sync password to Gitea for user ${userId}: ${result.error}`);
            // Handle sync failure (could send notification email)
            this.passwordSyncService.handlePasswordSyncFailure(result, user.email);
          }
        })
        .catch(error => {
          this.logger.error(`Unexpected error syncing password to Gitea for user ${userId}:`, error.message);
        });

      return { message: 'Password changed successfully' };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new NotFoundException('User not found');
    }

  }

  async deactivateUser(userId: string): Promise<{ message: string }> {
    try {
      const [updatedUser] = await this.db
        .update(users)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userId))
        .returning({ id: users.id });

      if (!updatedUser) {
        throw new NotFoundException('User not found');
      }

      return { message: 'User deactivated successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('User not found');
    }
  }

  async getUserRoles(userId: string): Promise<string[]> {
    try {
      const [user] = await this.db
        .select({ roles: users.roles })
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);

      if (!user) {
        throw new NotFoundException('User not found');
      }

      return user.roles;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('User not found');
    }
  }

  async assignRole(userId: string, roleId: string): Promise<{ message: string }> {
    try {
      // Check if user exists
      const [user] = await this.db
        .select({ id: users.id })
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if role is already assigned
      const [existingAssignment] = await this.db
        .select()
        .from(userRoles)
        .where(and(eq(userRoles.userId, userId), eq(userRoles.roleId, roleId)))
        .limit(1);

      if (existingAssignment) {
        throw new ConflictException('User already has this role');
      }

      // Assign role
      await this.db.insert(userRoles).values({
        userId,
        roleId,
      });

      return { message: 'Role assigned successfully' };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      throw new NotFoundException('User not found');
    }
  }

  async removeRole(userId: string, roleId: string): Promise<{ message: string }> {
    try {
      // Check if user exists
      const [user] = await this.db
        .select({ id: users.id })
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if role is assigned
      const [existingAssignment] = await this.db
        .select()
        .from(userRoles)
        .where(and(eq(userRoles.userId, userId), eq(userRoles.roleId, roleId)))
        .limit(1);

      if (!existingAssignment) {
        throw new BadRequestException('User does not have this role');
      }

      // Remove role
      await this.db
        .delete(userRoles)
        .where(and(eq(userRoles.userId, userId), eq(userRoles.roleId, roleId)));

      return { message: 'Role removed successfully' };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new NotFoundException('User not found');
    }
  }

  // 2FA Methods

  async setup2FA(userId: string): Promise<TwoFactorSetupResponse> {
    // Check if user exists and 2FA is not already enabled
    const [user] = await this.db
      .select({
        id: users.id,
        email: users.email,
        twoFactorEnabled: users.twoFactorEnabled
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.twoFactorEnabled) {
      throw new BadRequestException('Two-factor authentication is already enabled');
    }

    // Generate TOTP secret
    const secret = speakeasy.generateSecret({
      name: `RSGlider (${user.email})`,
      issuer: 'RSGlider',
      length: 32,
    });

    // Generate QR code
    const qrCodeUrl = await qrcode.toDataURL(secret.otpauth_url!);

    // Store the secret temporarily (not enabled yet)
    await this.db
      .update(users)
      .set({
        twoFactorSecret: secret.base32,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    return {
      secret: secret.base32,
      qrCodeUrl,
      manualEntryKey: secret.base32,
      issuer: 'RSGlider',
    };
  }

  async verifySetup2FA(userId: string, code: string): Promise<TwoFactorEnabledResponse> {
    // Get user with secret, email, and name
    const [user] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        twoFactorSecret: users.twoFactorSecret,
        twoFactorEnabled: users.twoFactorEnabled
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.twoFactorEnabled) {
      throw new BadRequestException('Two-factor authentication is already enabled');
    }

    if (!user.twoFactorSecret) {
      throw new BadRequestException('2FA setup not initiated. Please call setup endpoint first.');
    }

    // Verify the TOTP code
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token: code,
      window: 2, // Allow 2 time steps before/after for clock drift
    });

    if (!verified) {
      throw new BadRequestException('Invalid verification code');
    }

    // Generate backup codes
    const backupCodes = Array.from({ length: 8 }, () =>
      Math.random().toString(36).substring(2, 10).toUpperCase()
    );

    // Enable 2FA
    await this.db
      .update(users)
      .set({
        twoFactorEnabled: true,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    // Send 2FA enabled notification email with backup codes
    await this.emailService.send2FAEnabled(user.email, user.name || 'User', backupCodes);

    return {
      enabled: true,
      backupCodes,
      message: 'Two-factor authentication has been successfully enabled',
    };
  }

  async disable2FA(userId: string, password: string, code?: string, req?: Request): Promise<{ message: string }> {
    // Get user with password, 2FA info, email, and name
    const [user] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        password: users.password,
        twoFactorSecret: users.twoFactorSecret,
        twoFactorEnabled: users.twoFactorEnabled
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.twoFactorEnabled) {
      throw new BadRequestException('Two-factor authentication is not enabled');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid password');
    }

    // Verify 2FA code if provided
    if (code && user.twoFactorSecret) {
      const verified = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: 'base32',
        token: code,
        window: 2,
      });

      if (!verified) {
        throw new BadRequestException('Invalid verification code');
      }
    }

    // Disable 2FA
    await this.db
      .update(users)
      .set({
        twoFactorEnabled: false,
        twoFactorSecret: null,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    // Send 2FA disabled notification email
    const deviceInfo = {
      userAgent: req?.get('User-Agent') || 'Unknown device',
      ipAddress: req?.ip || 'Unknown IP',
    };

    await this.emailService.send2FADisabled(user.email, user.name || 'User', deviceInfo);

    return {
      message: 'Two-factor authentication has been successfully disabled',
    };
  }

  async getUserDevices(userId: string): Promise<DeviceDto[]> {
    const dbDevices = await this.db
      .select()
      .from(devices)
      .where(eq(devices.userId, userId));
    return dbDevices.map((d) => ({
      ...d,
      registeredAt: d.registeredAt?.toISOString(),
      lastActivityAt: d.lastActivityAt?.toISOString(),
      createdAt: d.createdAt?.toISOString(),
      updatedAt: d.updatedAt?.toISOString(),
    }));
  }

  async registerDevice(userId: string, dto: CreateDeviceRequestDto): Promise<DeviceDto> {
    const now = new Date();
    const [device] = await this.db
      .insert(devices)
      .values({
        userId,
        deviceName: dto.deviceName,
        deviceType: dto.deviceType,
        platform: dto.platform,
        deviceInfo: dto.deviceInfo,
        isTrusted: false,
        registeredAt: now,
        createdAt: now,
        updatedAt: now,
      })
      .returning();
    return {
      ...device,
      registeredAt: device.registeredAt?.toISOString(),
      lastActivityAt: device.lastActivityAt?.toISOString(),
      createdAt: device.createdAt?.toISOString(),
      updatedAt: device.updatedAt?.toISOString(),
    };
  }

  async removeDevice(userId: string, deviceId: string, req?: Request): Promise<{ message: string }> {
    // Get user info and device info for email notification
    const [user] = await this.db
      .select({
        email: users.email,
        name: users.name,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get device info before deletion
    const [device] = await this.db
      .select()
      .from(devices)
      .where(and(eq(devices.id, deviceId), eq(devices.userId, userId)))
      .limit(1);

    if (!device) {
      throw new NotFoundException('Device not found');
    }

    const result = await this.db
      .delete(devices)
      .where(and(eq(devices.id, deviceId), eq(devices.userId, userId)));

    if (result.count === 0) {
      throw new NotFoundException('Device not found');
    }

    // Send security alert email for device removal
    await this.emailService.sendSecurityAlert(
      user.email,
      user.name || 'User',
      'Device Removal',
      {
        action: 'A registered device was removed from your account',
        deviceName: device.deviceName,
        deviceType: device.deviceType,
        platform: device.platform,
        removedAt: new Date(),
        removedByIp: req?.ip || 'Unknown IP',
        userAgent: req?.get('User-Agent') || 'Unknown device',
      }
    );

    return { message: 'Device removed' };
  }

  async getUserSessions(userId: string): Promise<SessionDto[]> {
    const now = new Date();
    const sessions = await this.db
      .select()
      .from(userSessions)
      .where(and(eq(userSessions.userId, userId), eq(userSessions.isActive, true)));
    return sessions
      .filter((s) => !s.expiresAt || s.expiresAt > now)
      .map((s) => ({
        ...s,
        expiresAt: s.expiresAt?.toISOString(),
        lastActivityAt: s.lastActivityAt?.toISOString(),
        createdAt: s.createdAt?.toISOString(),
        updatedAt: s.updatedAt?.toISOString(),
        revokedAt: s.revokedAt?.toISOString(),
      }));
  }

  async removeUserSession(userId: string, sessionId: string, req?: Request): Promise<{ message: string }> {
    // Get user info for email notification
    const [user] = await this.db
      .select({
        email: users.email,
        name: users.name,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const [session] = await this.db
      .select()
      .from(userSessions)
      .where(and(eq(userSessions.id, sessionId), eq(userSessions.userId, userId)));
    if (!session) {
      throw new NotFoundException('Session not found');
    }
    if (session.isCurrent) {
      throw new BadRequestException('Cannot delete the current session');
    }
    if (!session.isActive) {
      throw new BadRequestException('Session already revoked');
    }
    const now = new Date();
    await this.db
      .update(userSessions)
      .set({
        isActive: false,
        revokedAt: now,
        revokedByIp: req?.ip || null,
        revokedReason: 'User-initiated',
        updatedAt: now,
      })
      .where(eq(userSessions.id, sessionId));

    // Send security alert email for single session revocation
    await this.emailService.sendSecurityAlert(
      user.email,
      user.name || 'User',
      'Single Session Revocation',
      {
        action: 'A session was revoked from your account',
        sessionId: sessionId,
        revokedAt: now,
        revokedByIp: req?.ip || 'Unknown IP',
        userAgent: req?.get('User-Agent') || 'Unknown device',
      }
    );

    return { message: 'Session revoked' };
  }

  async removeAllUserSessions(userId: string, req?: Request): Promise<{ message: string }> {
    // Get user info for email notification
    const [user] = await this.db
      .select({
        email: users.email,
        name: users.name,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Revoke all sessions except the current one
    const now = new Date();
    const result = await this.db
      .update(userSessions)
      .set({
        isActive: false,
        revokedAt: now,
        revokedByIp: req?.ip || null,
        revokedReason: 'User-initiated bulk revoke',
        updatedAt: now,
      })
      .where(and(eq(userSessions.userId, userId), eq(userSessions.isCurrent, false), eq(userSessions.isActive, true)));

    // Send security alert email for bulk session revocation
    if (result.count && result.count > 0) {
      await this.emailService.sendSecurityAlert(
        user.email,
        user.name || 'User',
        'Bulk Session Revocation',
        {
          action: 'All other sessions were revoked',
          sessionCount: result.count,
          revokedAt: now,
          revokedByIp: req?.ip || 'Unknown IP',
          userAgent: req?.get('User-Agent') || 'Unknown device',
        }
      );
    }

    return { message: 'All other sessions revoked' };
  }

  // --- ADMIN USER MANAGEMENT ---
  async adminListUsers({ page = 1, limit = 20, search = '', status, role }: { page?: number, limit?: number, search?: string, status?: string, role?: string }) {
    const offset = (page - 1) * limit;
    const whereClauses = [];
    if (search) {
      whereClauses.push(
        or(
          ilike(users.email, `%${search}%`),
          ilike(users.name, `%${search}%`),
          ilike(users.id, `%${search}%`)
        )
      );
    }
    if (status) {
      whereClauses.push(eq(users.isActive, status === 'active'));
    }
    if (role) {
      whereClauses.push(arrayContains(users.roles, [role]));
    }
    const where = whereClauses.length ? and(...whereClauses) : undefined;
    const [totalResult] = await this.db.select({ count: sql`count(*)::int` }).from(users).where(where);
    const total = totalResult?.count || 0;
    const usersList = await this.db.select().from(users).where(where).offset(offset).limit(limit);
    return {
      users: usersList.map((user) => ({
        id: user.id,
        email: user.email,
        firstName: user.name?.split(' ')[0],
        lastName: user.name?.split(' ').slice(1).join(' '),
        roles: user.roles,
        twoFactorEnabled: user.twoFactorEnabled,
        emailVerified: user.emailVerified,
        status: user.isActive ? 'active' : 'inactive',
        lastLoginAt: user.lastLoginAt?.toISOString(),
        createdAt: user.createdAt.toISOString(),
        updatedAt: user.updatedAt.toISOString(),
      })),
      pagination: { page, limit, total },
    };
  }

  async adminGetUser(userId: string) {
    return this.getCurrentUser(userId);
  }

  async adminCreateUser(data: Partial<User>) {
    // No password hashing or email sending yet
    const now = new Date();
    const [user] = await this.db.insert(users).values({
      email: data.email,
      password: (data as any).password || 'changeme', // Placeholder, must be set/reset later
      name: [data.firstName, data.lastName].filter(Boolean).join(' '),
      roles: data.roles || ['user'],
      isActive: true,
      emailVerified: !!data.emailVerified,
      twoFactorEnabled: !!data.twoFactorEnabled,
      createdAt: now,
      updatedAt: now,
    }).returning();
    return {
      id: user.id,
      email: user.email,
      firstName: user.name?.split(' ')[0],
      lastName: user.name?.split(' ').slice(1).join(' '),
      roles: user.roles,
      twoFactorEnabled: user.twoFactorEnabled,
      emailVerified: user.emailVerified,
      status: user.isActive ? 'active' : 'inactive',
      lastLoginAt: user.lastLoginAt?.toISOString(),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    };
  }

  async adminUpdateUser(userId: string, data: Partial<User>) {
    return this.updateCurrentUser(userId, data);
  }

  async adminDeleteUser(userId: string) {
    const [deleted] = await this.db.delete(users).where(eq(users.id, userId)).returning();
    if (!deleted) throw new NotFoundException('User not found');
    return { message: 'User deleted' };
  }

  // --- ADMIN USER-ROLE ASSIGNMENT ---
  async adminAssignRoleToUser(userId: string, roleId: string) {
    // Check if user exists
    const [user] = await this.db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user) throw new NotFoundException('User not found');

    // Check if role exists
    const [role] = await this.db.select().from(roles).where(eq(roles.id, roleId)).limit(1);
    if (!role) throw new NotFoundException('Role not found');

    // Check if already assigned
    const existing = await this.db.select().from(userRoles)
      .where(and(eq(userRoles.userId, userId), eq(userRoles.roleId, roleId))).limit(1);
    if (existing.length) throw new BadRequestException('Role already assigned to user');

    // Assign the role
    await this.db.insert(userRoles).values({ userId, roleId }).returning();

    // If this is the developer role, trigger developer onboarding
    if (role.name === 'developer') {
      await this.handleDeveloperRoleAssignment(userId, user);
    }

    return { message: 'Role assigned to user' };
  }

  private async handleDeveloperRoleAssignment(userId: string, user: any) {
    try {
      // Create developer profile and Gitea account
      const developerProfile = await this.developerManagementService.createDeveloper({
        userId,
        autoProvision: true
      });

      // Send developer welcome email with actual Gitea username
      const giteaUsername = developerProfile.giteaProfile?.giteaUsername ||
                           this.generateGiteaUsername(user.name || user.email);

      await this.emailService.sendDeveloperWelcome(
        user.email,
        user.name || 'Developer',
        giteaUsername
      );

      this.logger.log(`Developer onboarding completed for user ${userId}:`);
      this.logger.log(`  - Gitea account: ${giteaUsername}`);
      this.logger.log(`  - Provisioned: ${developerProfile.isProvisioned}`);
      this.logger.log(`  - Welcome email sent`);

    } catch (error) {
      this.logger.error(`Failed to complete developer onboarding for user ${userId}:`, error.message);

      // If Gitea account creation fails, still try to send email with generated username
      try {
        const fallbackUsername = this.generateGiteaUsername(user.name || user.email);
        await this.emailService.sendDeveloperWelcome(
          user.email,
          user.name || 'Developer',
          fallbackUsername
        );
        this.logger.log(`Developer welcome email sent with fallback username for user ${userId}`);
      } catch (emailError) {
        this.logger.error(`Failed to send fallback developer email for user ${userId}:`, emailError.message);
      }

      // Don't throw - role assignment should still succeed even if onboarding fails
    }
  }

  private generateGiteaUsername(nameOrEmail: string): string {
    // Extract username from email or clean up name
    const base = nameOrEmail.includes('@')
      ? nameOrEmail.split('@')[0]
      : nameOrEmail;

    // Clean up the username (remove special characters, spaces, etc.)
    const cleaned = base
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 20);

    // Add random suffix to avoid conflicts
    const suffix = Math.random().toString(36).substring(2, 6);

    return `${cleaned}${suffix}`;
  }

  async adminRemoveRoleFromUser(userId: string, roleId: string) {
    // Check if user exists
    const [user] = await this.db.select().from(users).where(eq(users.id, userId)).limit(1);
    if (!user) throw new NotFoundException('User not found');

    // Check if role exists
    const [role] = await this.db.select().from(roles).where(eq(roles.id, roleId)).limit(1);
    if (!role) throw new NotFoundException('Role not found');

    const [deleted] = await this.db.delete(userRoles)
      .where(and(eq(userRoles.userId, userId), eq(userRoles.roleId, roleId))).returning();
    if (!deleted) throw new NotFoundException('Role not assigned to user');
    return { message: 'Role removed from user' };
  }

  async adminListUserRoles(userId: string) {
    const userRoleRows = await this.db.select().from(userRoles).where(eq(userRoles.userId, userId));
    if (!userRoleRows.length) return [];
    const roleIds = userRoleRows.map((ur) => ur.roleId);
    const dbRoles = await this.db.select().from(roles).where(inArray(roles.id, roleIds));
    return dbRoles.map((r) => ({
      id: r.id,
      name: r.name,
      description: r.description,
      isSystemRole: r.isSystemRole,
      createdAt: r.createdAt?.toISOString(),
      updatedAt: r.updatedAt?.toISOString(),
    }));
  }
}
