import { Test, TestingModule } from '@nestjs/testing';
import { DatabaseModule } from '../database/database.module';
import { UsersController } from './users.controller';
import { UsersModule } from './users.module';
import { UsersService } from './users.service';

// Mock the dependencies to avoid complex setup
jest.mock('../database/database.module', () => ({
  DatabaseModule: class MockDatabaseModule { },
}));

jest.mock('../email/email.module', () => ({
  EmailModule: class MockEmailModule { },
}));

jest.mock('./users.controller', () => ({
  UsersController: class MockUsersController { },
}));

jest.mock('./users.service', () => ({
  UsersService: class MockUsersService { },
}));

describe('UsersModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [UsersModule],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should compile successfully', () => {
    expect(module).toBeInstanceOf(TestingModule);
  });

  it('should import DatabaseModule', () => {
    expect(() => module.get(DatabaseModule)).not.toThrow();
  });

  it('should provide UsersController', () => {
    expect(() => module.get(UsersController)).not.toThrow();
  });

  it('should provide UsersService', () => {
    expect(() => module.get(UsersService)).not.toThrow();
  });

  describe('module metadata', () => {
    it('should have correct imports', () => {
      const imports = Reflect.getMetadata('imports', UsersModule);
      expect(imports).toBeDefined();
      expect(imports).toContain(DatabaseModule);
    });

    it('should have correct controllers', () => {
      const controllers = Reflect.getMetadata('controllers', UsersModule);
      expect(controllers).toBeDefined();
      expect(controllers).toContain(UsersController);
    });

    it('should have correct providers', () => {
      const providers = Reflect.getMetadata('providers', UsersModule);
      expect(providers).toBeDefined();
      expect(providers).toContain(UsersService);
    });

    it('should export UsersService', () => {
      const exports = Reflect.getMetadata('exports', UsersModule);
      expect(exports).toBeDefined();
      expect(exports).toContain(UsersService);
    });
  });

  describe('dependency injection', () => {
    it('should properly wire up module dependencies', () => {
      expect(() => {
        module.get(UsersController);
        module.get(UsersService);
      }).not.toThrow();
    });
  });
});
