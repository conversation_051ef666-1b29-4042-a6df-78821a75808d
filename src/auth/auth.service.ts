import { BadRequestException, ConflictException, Inject, Injectable, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { randomUUID } from 'crypto';
import { and, eq, sql } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as speakeasy from 'speakeasy';
import { User, users } from '../database/schema/index.js';
import { userSessions } from '../database/schema/user-sessions.schema.js';
import { EmailService } from '../email/email.service.js';
import { AuthResponse } from './dto/auth-response.dto.js';
import { LoginRequest } from './dto/login-request.dto.js';
import { RegisterRequest } from './dto/register-request.dto.js';
import { TokenResponse } from './dto/token-response.dto.js';

@Injectable()
export class AuthService {
  constructor(
    @Inject('DB') private readonly db: PostgresJsDatabase<typeof import('../database/schema/index.js')>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService,
  ) { }

  async registerUser(registerData: RegisterRequest): Promise<AuthResponse> {
    const { email, password, name } = registerData;

    // Check if user already exists
    const existingUser = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
    if (existingUser.length > 0) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Generate email verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');

    // Create user
    const [savedUser] = await this.db.insert(users).values({
      email,
      password: hashedPassword,
      name,
      emailVerificationToken,
      roles: ['user'], // Default role
      isActive: true,
      emailVerified: false,
    }).returning();

    // Generate tokens
    const sessionId = randomUUID();
    const tokens = await this.generateTokens(savedUser, sessionId);

    // Send welcome email with verification link
    await this.emailService.sendWelcomeEmail(email, name || 'User', emailVerificationToken);

    return {
      user: {
        id: savedUser.id,
        email: savedUser.email,
        name: savedUser.name,
        roles: savedUser.roles,
        emailVerified: savedUser.emailVerified,
      },
      ...tokens,
    };
  }

  async loginUser(loginData: LoginRequest, req?: any): Promise<AuthResponse> {
    const { email, password, twoFactorCode, deviceInfo, location, platform } = loginData as any;

    // Find user
    const [user] = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new UnauthorizedException('Account is deactivated');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check 2FA if enabled
    if (user.twoFactorEnabled) {
      if (!twoFactorCode) {
        throw new UnauthorizedException('Two-factor authentication code is required');
      }

      if (!user.twoFactorSecret) {
        throw new UnauthorizedException('Two-factor authentication is not properly configured');
      }

      // Verify the TOTP code
      const verified = speakeasy.totp.verify({
        secret: user.twoFactorSecret,
        encoding: 'base32',
        token: twoFactorCode,
        window: 2, // Allow 2 time steps before/after for clock drift
      });

      if (!verified) {
        throw new UnauthorizedException('Invalid two-factor authentication code');
      }
    }

    // Check for new device detection
    const currentDeviceInfo = {
      userAgent: req?.get('User-Agent') || deviceInfo?.userAgent || 'Unknown',
      platform: platform || deviceInfo?.platform || 'Unknown',
      ipAddress: req?.ip || 'Unknown',
      ...deviceInfo,
    };

    // Check if this is a new device by looking at recent sessions
    const recentSessions = await this.db
      .select()
      .from(userSessions)
      .where(and(
        eq(userSessions.userId, user.id),
        eq(userSessions.isActive, true)
      ))
      .limit(10);

    // Simple device detection based on user agent and platform
    const isNewDevice = !recentSessions.some(session => {
      const sessionDeviceInfo = session.deviceInfo as any;
      return sessionDeviceInfo?.userAgent === currentDeviceInfo.userAgent ||
        sessionDeviceInfo?.platform === currentDeviceInfo.platform;
    });

    // Update last login
    await this.db.update(users)
      .set({
        lastLoginAt: new Date(),
        lastLoginIp: req?.ip || 'Unknown',
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id));

    // Set all other sessions to isCurrent=false
    await this.db.update(userSessions)
      .set({ isCurrent: false })
      .where(eq(userSessions.userId, user.id));

    // Generate a sessionId
    const sessionId = randomUUID();

    // Create session
    const now = new Date();
    const expiresIn = this.configService.get('JWT_EXPIRES_IN', '15m');
    const expiresAt = new Date(now.getTime() + this.parseExpirationToSeconds(expiresIn) * 1000);
    await this.db.insert(userSessions).values({
      id: sessionId,
      userId: user.id,
      platform: platform || 'web',
      deviceInfo: currentDeviceInfo,
      location: location || {},
      expiresAt,
      isActive: true,
      isCurrent: true,
      requiresVerification: false,
      lastActivityAt: now,
      createdAt: now,
      updatedAt: now,
    });

    // Send new device login notification if this is a new device
    if (isNewDevice) {
      const locationString = location?.city && location?.country
        ? `${location.city}, ${location.country}`
        : 'Unknown location';

      await this.emailService.sendNewDeviceLogin(
        user.email,
        user.name || 'User',
        currentDeviceInfo,
        locationString
      );
    }

    // Generate tokens with sessionId
    const tokens = await this.generateTokens(user, sessionId);

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        roles: user.roles,
        emailVerified: user.emailVerified,
      },
      ...tokens,
    };
  }

  async logoutUser(user: { id: string, sid?: string }, req: any): Promise<{ message: string }> {
    // Revoke only the session matching the sid in the JWT
    if (!user.sid) {
      throw new BadRequestException('No session ID found in token');
    }
    const [session] = await this.db
      .select()
      .from(userSessions)
      .where(and(eq(userSessions.userId, user.id), eq(userSessions.id, user.sid), eq(userSessions.isActive, true)))
      .limit(1);
    if (session) {
      const now = new Date();
      await this.db.update(userSessions)
        .set({
          isActive: false,
          revokedAt: now,
          revokedByIp: req?.ip || null,
          revokedReason: 'User logout',
          updatedAt: now,
        })
        .where(eq(userSessions.id, session.id));
    }
    return { message: 'Logged out successfully' };
  }

  async refreshToken(refreshTokenData: { refreshToken: string }): Promise<TokenResponse> {
    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshTokenData.refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
      });
      // Find user
      const [user] = await this.db.select().from(users).where(eq(users.id, payload.sub)).limit(1);
      if (!user || !user.isActive) {
        throw new UnauthorizedException('Invalid refresh token');
      }
      // Use sessionId from payload if present, else generate new
      const sessionId = payload.sid || randomUUID();
      // Generate new tokens
      return await this.generateTokens(user, sessionId);
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async forgotPassword(forgotPasswordData: { email: string }): Promise<{ message: string }> {
    const { email } = forgotPasswordData;

    const [user] = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
    if (!user) {
      // Don't reveal if email exists or not
      return { message: 'If the email exists, a password reset link has been sent' };
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetExpires = new Date(Date.now() + 3600000); // 1 hour

    await this.db.update(users)
      .set({
        passwordResetToken: resetToken,
        passwordResetExpires: resetExpires,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id));

    // Send password reset email
    await this.emailService.sendPasswordReset(email, user.name || 'User', resetToken);

    return { message: 'If the email exists, a password reset link has been sent' };
  }

  async resetPassword(resetData: { token: string; password: string }): Promise<{ message: string }> {
    const { token, password } = resetData;

    const [user] = await this.db.select().from(users).where(eq(users.passwordResetToken, token)).limit(1);

    if (!user || !user.passwordResetExpires || user.passwordResetExpires < new Date()) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Update user
    await this.db.update(users)
      .set({
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id));

    // Send password changed confirmation email
    await this.emailService.sendPasswordChanged(user.email, user.name || 'User', {
      userAgent: 'Password Reset',
      ipAddress: 'Unknown',
    });

    return { message: 'Password reset successfully' };
  }

  async verifyEmail(token: string): Promise<{ message: string }> {
    if (!token) {
      throw new BadRequestException('Verification token is required');
    }

    const [user] = await this.db
      .select()
      .from(users)
      .where(eq(users.emailVerificationToken, token))
      .limit(1);

    if (!user) {
      throw new BadRequestException('Invalid or expired verification token');
    }

    if (user.emailVerified) {
      throw new BadRequestException('Email is already verified');
    }

    // Mark email as verified and clear the token
    await this.db
      .update(users)
      .set({
        emailVerified: true,
        emailVerificationToken: null,
        updatedAt: new Date(),
      })
      .where(eq(users.id, user.id));

    // Send email verification confirmation
    await this.emailService.sendEmailVerified(user.email, user.name || 'User');

    return { message: 'Email verified successfully' };
  }

  async verifyEmailChange(token: string): Promise<{ message: string }> {
    if (!token) {
      throw new BadRequestException('Verification token is required');
    }

    // Find user with the email change token
    const [user] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        pendingEmail: users.pendingEmail,
        emailChangeToken: users.emailChangeToken,
        emailChangeExpires: users.emailChangeExpires,
      })
      .from(users)
      .where(eq(users.emailChangeToken, token))
      .limit(1);

    if (!user) {
      throw new BadRequestException('Invalid or expired verification token');
    }

    // Check if token has expired
    if (!user.emailChangeExpires || user.emailChangeExpires < new Date()) {
      throw new BadRequestException('Verification token has expired');
    }

    if (!user.pendingEmail) {
      throw new BadRequestException('No pending email change found');
    }

    // Check if the pending email is already taken by another user
    const [existingUser] = await this.db
      .select({ id: users.id })
      .from(users)
      .where(and(eq(users.email, user.pendingEmail), sql`${users.id} != ${user.id}`))
      .limit(1);

    if (existingUser) {
      throw new BadRequestException('Email address is already in use');
    }

    // Update user: change email, clear pending fields, mark as verified
    await this.db
      .update(users)
      .set({
        email: user.pendingEmail,
        emailVerified: true,
        pendingEmail: null,
        emailChangeToken: null,
        emailChangeExpires: null,
        updatedAt: new Date(),
      })
      .where(eq(users.id, user.id));

    // Send confirmation email to the new email address
    await this.emailService.sendEmailChanged(
      user.pendingEmail,
      user.name || 'User',
      user.email // old email
    );

    return { message: 'Email address changed successfully' };
  }

  async resendVerification(userId: string): Promise<{ message: string }> {
    const [user] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        emailVerified: users.emailVerified,
      })
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (user.emailVerified) {
      throw new BadRequestException('Email is already verified');
    }

    // Generate new verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');

    // Update user with new token
    await this.db
      .update(users)
      .set({
        emailVerificationToken,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId));

    // Send verification email
    await this.emailService.sendEmailVerification(
      user.email,
      user.name || 'User',
      emailVerificationToken
    );

    return { message: 'Verification email sent successfully' };
  }

  private async generateTokens(user: User, sessionId: string): Promise<TokenResponse> {
    const payload = {
      sub: user.id,
      email: user.email,
      roles: user.roles,
      sid: sessionId,
    };
    const expiresIn = this.configService.get('JWT_EXPIRES_IN', '15m');
    const refreshExpiresIn = this.configService.get('JWT_REFRESH_EXPIRES_IN', '7d');
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get('JWT_SECRET'),
        expiresIn,
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
        expiresIn: refreshExpiresIn,
      }),
    ]);
    // Convert expiration time to seconds
    const expiresInSeconds = this.parseExpirationToSeconds(expiresIn);
    return {
      accessToken,
      refreshToken,
      expiresIn: expiresInSeconds,
      tokenType: 'Bearer',
    };
  }

  private parseExpirationToSeconds(expiration: string): number {
    // Parse common time formats like '15m', '1h', '7d', etc.
    const match = expiration.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 900; // Default to 15 minutes
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 60 * 60 * 24;
      default: return 900;
    }
  }
}
