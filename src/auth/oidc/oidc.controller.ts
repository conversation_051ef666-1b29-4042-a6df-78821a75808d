import {
    Body,
    Controller,
    Get,
    HttpStatus,
    Post,
    Query,
    Req,
    Res,
    UnauthorizedException,
    UseGuards
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard.js';
import {
    OIDCAuthorizationRequestDto,
    OIDCDiscoveryResponseDto,
    OIDCTokenRequestDto,
    OIDCTokenResponseDto,
    OIDCUserInfoResponseDto
} from './dto/index.js';
import { OIDCProviderService } from './oidc-provider.service.js';

@ApiTags('OIDC Provider')
@Controller('auth/oidc')
export class OIDCController {
  constructor(private readonly oidcProviderService: OIDCProviderService) {}

  @Get('.well-known/openid-configuration')
  @ApiOperation({ summary: 'Get OIDC discovery document' })
  @ApiResponse({
    status: 200,
    description: 'OIDC discovery document',
    type: OIDCDiscoveryResponseDto,
  })
  getDiscoveryDocument(): OIDCDiscoveryResponseDto {
    return this.oidcProviderService.getDiscoveryDocument();
  }

  @Get('authorize')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'OIDC authorization endpoint' })
  @ApiQuery({ name: 'response_type', description: 'OAuth 2.0 response type', example: 'code' })
  @ApiQuery({ name: 'client_id', description: 'OAuth 2.0 client identifier', example: 'gitea' })
  @ApiQuery({ name: 'redirect_uri', description: 'OAuth 2.0 redirect URI' })
  @ApiQuery({ name: 'scope', description: 'OAuth 2.0 scope', example: 'openid profile email' })
  @ApiQuery({ name: 'state', description: 'OAuth 2.0 state parameter', required: false })
  @ApiQuery({ name: 'nonce', description: 'OpenID Connect nonce parameter', required: false })
  @ApiQuery({ name: 'code_challenge', description: 'PKCE code challenge', required: false })
  @ApiQuery({ name: 'code_challenge_method', description: 'PKCE code challenge method', required: false })
  @ApiResponse({
    status: 302,
    description: 'Redirect to client with authorization code',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  async authorize(
    @Query() authRequest: OIDCAuthorizationRequestDto,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    try {
      // Get user ID from JWT token
      const user = (req as any).user;
      if (!user || !user.sub) {
        throw new UnauthorizedException('User not authenticated');
      }

      // Generate authorization code
      const code = await this.oidcProviderService.handleAuthorizationRequest(user.sub, authRequest);

      // Build redirect URL with authorization code
      const redirectUrl = new URL(authRequest.redirect_uri);
      redirectUrl.searchParams.set('code', code);
      
      if (authRequest.state) {
        redirectUrl.searchParams.set('state', authRequest.state);
      }

      // Redirect to client
      res.redirect(HttpStatus.FOUND, redirectUrl.toString());
    } catch (error) {
      // Redirect to client with error
      const redirectUrl = new URL(authRequest.redirect_uri);
      redirectUrl.searchParams.set('error', 'server_error');
      redirectUrl.searchParams.set('error_description', error.message);
      
      if (authRequest.state) {
        redirectUrl.searchParams.set('state', authRequest.state);
      }

      res.redirect(HttpStatus.FOUND, redirectUrl.toString());
    }
  }

  @Post('token')
  @ApiOperation({ summary: 'OIDC token endpoint' })
  @ApiResponse({
    status: 200,
    description: 'Access token and ID token',
    type: OIDCTokenResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request',
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid client credentials',
  })
  async token(@Body() tokenRequest: OIDCTokenRequestDto): Promise<OIDCTokenResponseDto> {
    return this.oidcProviderService.exchangeCodeForTokens(tokenRequest);
  }

  @Get('userinfo')
  @ApiOperation({ summary: 'OIDC UserInfo endpoint' })
  @ApiResponse({
    status: 200,
    description: 'User information',
    type: OIDCUserInfoResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid access token',
  })
  async userinfo(@Req() req: Request): Promise<OIDCUserInfoResponseDto> {
    // Extract access token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('Missing or invalid Authorization header');
    }

    const accessToken = authHeader.substring(7); // Remove 'Bearer ' prefix
    return this.oidcProviderService.getUserInfo(accessToken);
  }

  @Get('jwks')
  @ApiOperation({ summary: 'OIDC JSON Web Key Set endpoint' })
  @ApiResponse({
    status: 200,
    description: 'JSON Web Key Set',
  })
  getJWKS(): any {
    return this.oidcProviderService.getJWKS();
  }

  @Get('login')
  @ApiOperation({ summary: 'OIDC login page (for testing)' })
  @ApiQuery({ name: 'client_id', description: 'OAuth 2.0 client identifier', example: 'gitea' })
  @ApiQuery({ name: 'redirect_uri', description: 'OAuth 2.0 redirect URI' })
  @ApiQuery({ name: 'scope', description: 'OAuth 2.0 scope', example: 'openid profile email' })
  @ApiQuery({ name: 'state', description: 'OAuth 2.0 state parameter', required: false })
  @ApiResponse({
    status: 200,
    description: 'Login page HTML',
  })
  getLoginPage(@Query() query: any, @Req() req: Request, @Res() res: Response): void {
    // Simple login page for testing
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>RSGlider OIDC Login</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 400px; margin: 100px auto; padding: 20px; }
          .form-group { margin-bottom: 15px; }
          label { display: block; margin-bottom: 5px; }
          input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
          button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
          button:hover { background: #0056b3; }
          .info { background: #f8f9fa; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        </style>
      </head>
      <body>
        <h2>RSGlider OIDC Login</h2>
        <div class="info">
          <strong>Client:</strong> ${query.client_id || 'Unknown'}<br>
          <strong>Scope:</strong> ${query.scope || 'Unknown'}<br>
          <strong>Redirect URI:</strong> ${query.redirect_uri || 'Unknown'}
        </div>
        <form action="/auth/login" method="post">
          <input type="hidden" name="oidc_redirect" value="${encodeURIComponent(req.url)}">
          <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
          </div>
          <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
          </div>
          <button type="submit">Login</button>
        </form>
        <p><small>This is a test login page. In production, users would be redirected to the main RSGlider login.</small></p>
      </body>
      </html>
    `;
    
    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  }
}
