import { BadRequestException, Inject, Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import * as crypto from 'crypto';
import { eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import * as schema from '../../database/schema/index.js';
import { users } from '../../database/schema/users.schema.js';
import {
  OIDCAuthorizationRequestDto,
  OIDCDiscoveryResponseDto,
  OIDCTokenRequestDto,
  OIDCTokenResponseDto,
  OIDCUserInfoResponseDto
} from './dto/index.js';

interface AuthorizationCodeData {
  userId: string;
  clientId: string;
  redirectUri: string;
  scope: string;
  nonce?: string;
  codeChallenge?: string;
  codeChallengeMethod?: string;
  expiresAt: Date;
}

@Injectable()
export class OIDCProviderService {
  private readonly logger = new Logger(OIDCProviderService.name);
  private readonly authorizationCodes = new Map<string, AuthorizationCodeData>();

  constructor(
    @Inject('DB') private readonly db: NodePgDatabase<typeof schema>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Get OIDC discovery document
   */
  getDiscoveryDocument(): OIDCDiscoveryResponseDto {
    const baseUrl = this.configService.get('BASE_URL', 'http://localhost:3000');

    return {
      issuer: baseUrl,
      authorization_endpoint: `${baseUrl}/auth/oidc/authorize`,
      token_endpoint: `${baseUrl}/auth/oidc/token`,
      userinfo_endpoint: `${baseUrl}/auth/oidc/userinfo`,
      jwks_uri: `${baseUrl}/auth/oidc/jwks`,
      scopes_supported: ['openid', 'profile', 'email'],
      response_types_supported: ['code'],
      grant_types_supported: ['authorization_code'],
      subject_types_supported: ['public'],
      id_token_signing_alg_values_supported: ['HS256'],
      token_endpoint_auth_methods_supported: ['client_secret_basic', 'client_secret_post'],
      claims_supported: [
        'sub',
        'name',
        'email',
        'email_verified',
        'preferred_username',
        'given_name',
        'family_name',
        'picture'
      ],
      code_challenge_methods_supported: ['plain', 'S256'],
    };
  }

  /**
   * Handle authorization request
   */
  async handleAuthorizationRequest(
    userId: string,
    authRequest: OIDCAuthorizationRequestDto
  ): Promise<string> {
    // Validate client
    if (!this.isValidClient(authRequest.client_id)) {
      throw new BadRequestException('Invalid client_id');
    }

    // Validate redirect URI
    if (!this.isValidRedirectUri(authRequest.client_id, authRequest.redirect_uri)) {
      throw new BadRequestException('Invalid redirect_uri');
    }

    // Generate authorization code
    const code = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store authorization code
    this.authorizationCodes.set(code, {
      userId,
      clientId: authRequest.client_id,
      redirectUri: authRequest.redirect_uri,
      scope: authRequest.scope,
      nonce: authRequest.nonce,
      codeChallenge: authRequest.code_challenge,
      codeChallengeMethod: authRequest.code_challenge_method,
      expiresAt,
    });

    // Clean up expired codes
    this.cleanupExpiredCodes();

    this.logger.log(`Generated authorization code for user ${userId} and client ${authRequest.client_id}`);

    return code;
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(tokenRequest: OIDCTokenRequestDto): Promise<OIDCTokenResponseDto> {
    // Validate client credentials
    if (!this.validateClientCredentials(tokenRequest.client_id, tokenRequest.client_secret)) {
      throw new UnauthorizedException('Invalid client credentials');
    }

    // Get authorization code data
    const codeData = this.authorizationCodes.get(tokenRequest.code);
    if (!codeData) {
      throw new BadRequestException('Invalid or expired authorization code');
    }

    // Validate code data
    if (codeData.clientId !== tokenRequest.client_id) {
      throw new BadRequestException('Client ID mismatch');
    }

    if (codeData.redirectUri !== tokenRequest.redirect_uri) {
      throw new BadRequestException('Redirect URI mismatch');
    }

    if (codeData.expiresAt < new Date()) {
      this.authorizationCodes.delete(tokenRequest.code);
      throw new BadRequestException('Authorization code expired');
    }

    // Validate PKCE if present
    if (codeData.codeChallenge && !this.validatePKCE(codeData, tokenRequest.code_verifier)) {
      throw new BadRequestException('Invalid code verifier');
    }

    // Get user data
    const [user] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        emailVerified: users.emailVerified,
      })
      .from(users)
      .where(eq(users.id, codeData.userId))
      .limit(1);

    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Generate tokens
    const accessToken = await this.generateAccessToken(user, codeData.scope);
    const idToken = await this.generateIdToken(user, tokenRequest.client_id, codeData.nonce);

    // Remove used authorization code
    this.authorizationCodes.delete(tokenRequest.code);

    this.logger.log(`Exchanged authorization code for tokens for user ${user.id}`);

    return {
      access_token: accessToken,
      token_type: 'Bearer',
      expires_in: 3600, // 1 hour
      id_token: idToken,
      scope: codeData.scope,
    };
  }

  /**
   * Get user info from access token
   */
  async getUserInfo(accessToken: string): Promise<OIDCUserInfoResponseDto> {
    try {
      const payload = await this.jwtService.verifyAsync(accessToken);

      const [user] = await this.db
        .select({
          id: users.id,
          email: users.email,
          name: users.name,
          emailVerified: users.emailVerified,
        })
        .from(users)
        .where(eq(users.id, payload.sub))
        .limit(1);

      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      return {
        sub: user.id,
        name: user.name || user.email,
        email: user.email,
        email_verified: user.emailVerified || false,
        preferred_username: user.email,
        given_name: user.name?.split(' ')[0],
        family_name: user.name?.split(' ').slice(1).join(' '),
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid access token');
    }
  }

  /**
   * Get JWKS (JSON Web Key Set)
   */
  getJWKS(): any {
    // In production, you should use proper RSA keys
    // For now, we'll use the JWT secret
    const secret = this.configService.get('JWT_SECRET');

    return {
      keys: [
        {
          kty: 'oct',
          use: 'sig',
          kid: 'rsglider-key-1',
          alg: 'HS256',
          k: Buffer.from(secret).toString('base64url'),
        }
      ]
    };
  }

  // Private helper methods

  private isValidClient(clientId: string): boolean {
    const validClients = this.configService.get('OIDC_CLIENTS', 'gitea').split(',');
    return validClients.includes(clientId);
  }

  private isValidRedirectUri(_clientId: string, redirectUri: string): boolean {
    // For Gitea, the redirect URI should be the Gitea base URL + /user/oauth2/{provider}/callback
    const giteaBaseUrl = this.configService.get('GITEA_BASE_URL', 'http://localhost:3001');
    const expectedRedirectUri = `${giteaBaseUrl}/user/oauth2/rsglider/callback`;

    return redirectUri === expectedRedirectUri;
  }

  private validateClientCredentials(clientId: string, clientSecret: string): boolean {
    const expectedSecret = this.configService.get('OIDC_CLIENT_SECRET', 'gitea-secret');
    return clientId === 'gitea' && clientSecret === expectedSecret;
  }

  private validatePKCE(codeData: AuthorizationCodeData, codeVerifier?: string): boolean {
    if (!codeVerifier || !codeData.codeChallenge) {
      return false;
    }

    if (codeData.codeChallengeMethod === 'S256') {
      const hash = crypto.createHash('sha256').update(codeVerifier).digest();
      const challenge = hash.toString('base64url');
      return challenge === codeData.codeChallenge;
    }

    return codeVerifier === codeData.codeChallenge;
  }

  private async generateAccessToken(user: any, scope: string): Promise<string> {
    const payload = {
      sub: user.id,
      email: user.email,
      scope,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    };

    return this.jwtService.signAsync(payload);
  }

  private async generateIdToken(user: any, clientId: string, nonce?: string): Promise<string> {
    const payload = {
      iss: this.configService.get('BASE_URL', 'http://localhost:3000'),
      sub: user.id,
      aud: clientId,
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
      iat: Math.floor(Date.now() / 1000),
      name: user.name || user.email,
      email: user.email,
      email_verified: user.emailVerified || false,
      preferred_username: user.email,
      ...(nonce && { nonce }),
    };

    return this.jwtService.signAsync(payload);
  }

  private cleanupExpiredCodes(): void {
    const now = new Date();
    for (const [code, data] of this.authorizationCodes.entries()) {
      if (data.expiresAt < now) {
        this.authorizationCodes.delete(code);
      }
    }
  }
}