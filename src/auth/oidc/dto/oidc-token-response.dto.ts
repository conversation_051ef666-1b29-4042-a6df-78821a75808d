import { ApiProperty } from '@nestjs/swagger';

export class OIDCTokenResponseDto {
  @ApiProperty({
    description: 'OAuth 2.0 access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  access_token: string;

  @ApiProperty({
    description: 'OAuth 2.0 token type',
    example: 'Bearer'
  })
  token_type: string;

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 3600
  })
  expires_in: number;

  @ApiProperty({
    description: 'OpenID Connect ID token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  id_token: string;

  @ApiProperty({
    description: 'OAuth 2.0 scope',
    example: 'openid profile email'
  })
  scope: string;

  @ApiProperty({
    description: 'OAuth 2.0 refresh token',
    example: 'def456ghi789abc123',
    required: false
  })
  refresh_token?: string;
}
