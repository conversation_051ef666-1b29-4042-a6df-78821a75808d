import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsIn, IsUrl } from 'class-validator';

export class OIDCAuthorizationRequestDto {
  @ApiProperty({
    description: 'OAuth 2.0 response type',
    example: 'code',
    enum: ['code']
  })
  @IsString()
  @IsIn(['code'])
  response_type: string;

  @ApiProperty({
    description: 'OAuth 2.0 client identifier',
    example: 'gitea'
  })
  @IsString()
  client_id: string;

  @ApiProperty({
    description: 'OAuth 2.0 redirect URI',
    example: 'http://localhost:3001/user/oauth2/rsglider/callback'
  })
  @IsString()
  @IsUrl()
  redirect_uri: string;

  @ApiProperty({
    description: 'OAuth 2.0 scope',
    example: 'openid profile email'
  })
  @IsString()
  scope: string;

  @ApiProperty({
    description: 'OAuth 2.0 state parameter for CSRF protection',
    example: 'random-state-string',
    required: false
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({
    description: 'OpenID Connect nonce parameter',
    example: 'random-nonce-string',
    required: false
  })
  @IsOptional()
  @IsString()
  nonce?: string;

  @ApiProperty({
    description: 'PKCE code challenge',
    example: 'dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk',
    required: false
  })
  @IsOptional()
  @IsString()
  code_challenge?: string;

  @ApiProperty({
    description: 'PKCE code challenge method',
    example: 'S256',
    enum: ['plain', 'S256'],
    required: false
  })
  @IsOptional()
  @IsString()
  @IsIn(['plain', 'S256'])
  code_challenge_method?: string;
}
