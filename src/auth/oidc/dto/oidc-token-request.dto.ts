import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsIn, IsUrl } from 'class-validator';

export class OIDCTokenRequestDto {
  @ApiProperty({
    description: 'OAuth 2.0 grant type',
    example: 'authorization_code',
    enum: ['authorization_code']
  })
  @IsString()
  @IsIn(['authorization_code'])
  grant_type: string;

  @ApiProperty({
    description: 'Authorization code received from authorization endpoint',
    example: 'abc123def456ghi789'
  })
  @IsString()
  code: string;

  @ApiProperty({
    description: 'OAuth 2.0 redirect URI (must match authorization request)',
    example: 'http://localhost:3001/user/oauth2/rsglider/callback'
  })
  @IsString()
  @IsUrl()
  redirect_uri: string;

  @ApiProperty({
    description: 'OAuth 2.0 client identifier',
    example: 'gitea'
  })
  @IsString()
  client_id: string;

  @ApiProperty({
    description: 'OAuth 2.0 client secret',
    example: 'gitea-secret'
  })
  @IsString()
  client_secret: string;

  @ApiProperty({
    description: 'PKCE code verifier',
    example: 'dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk',
    required: false
  })
  @IsOptional()
  @IsString()
  code_verifier?: string;
}
