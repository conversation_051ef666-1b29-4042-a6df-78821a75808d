import { ApiProperty } from '@nestjs/swagger';

export class OIDCUserInfoResponseDto {
  @ApiProperty({
    description: 'Subject identifier (user ID)',
    example: 'user-123-456-789'
  })
  sub: string;

  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>'
  })
  name: string;

  @ApiProperty({
    description: 'Email address of the user',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Whether the email address has been verified',
    example: true
  })
  email_verified: boolean;

  @ApiProperty({
    description: 'Preferred username',
    example: '<EMAIL>'
  })
  preferred_username: string;

  @ApiProperty({
    description: 'Given name (first name)',
    example: 'John',
    required: false
  })
  given_name?: string;

  @ApiProperty({
    description: 'Family name (last name)',
    example: '<PERSON><PERSON>',
    required: false
  })
  family_name?: string;

  @ApiProperty({
    description: 'Profile picture URL',
    example: 'https://example.com/avatar.jpg',
    required: false
  })
  picture?: string;

  @ApiProperty({
    description: 'Locale of the user',
    example: 'en-US',
    required: false
  })
  locale?: string;

  @ApiProperty({
    description: 'Time zone of the user',
    example: 'America/New_York',
    required: false
  })
  zoneinfo?: string;
}
