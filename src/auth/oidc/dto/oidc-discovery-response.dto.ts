import { ApiProperty } from '@nestjs/swagger';

export class OIDCDiscoveryResponseDto {
  @ApiProperty({
    description: 'Issuer identifier',
    example: 'http://localhost:3000'
  })
  issuer: string;

  @ApiProperty({
    description: 'Authorization endpoint URL',
    example: 'http://localhost:3000/auth/oidc/authorize'
  })
  authorization_endpoint: string;

  @ApiProperty({
    description: 'Token endpoint URL',
    example: 'http://localhost:3000/auth/oidc/token'
  })
  token_endpoint: string;

  @ApiProperty({
    description: 'UserInfo endpoint URL',
    example: 'http://localhost:3000/auth/oidc/userinfo'
  })
  userinfo_endpoint: string;

  @ApiProperty({
    description: 'JSON Web Key Set URI',
    example: 'http://localhost:3000/auth/oidc/jwks'
  })
  jwks_uri: string;

  @ApiProperty({
    description: 'Supported scopes',
    example: ['openid', 'profile', 'email'],
    type: [String]
  })
  scopes_supported: string[];

  @ApiProperty({
    description: 'Supported response types',
    example: ['code'],
    type: [String]
  })
  response_types_supported: string[];

  @ApiProperty({
    description: 'Supported grant types',
    example: ['authorization_code'],
    type: [String]
  })
  grant_types_supported: string[];

  @ApiProperty({
    description: 'Supported subject types',
    example: ['public'],
    type: [String]
  })
  subject_types_supported: string[];

  @ApiProperty({
    description: 'Supported ID token signing algorithms',
    example: ['RS256', 'HS256'],
    type: [String]
  })
  id_token_signing_alg_values_supported: string[];

  @ApiProperty({
    description: 'Supported token endpoint authentication methods',
    example: ['client_secret_basic', 'client_secret_post'],
    type: [String]
  })
  token_endpoint_auth_methods_supported: string[];

  @ApiProperty({
    description: 'Supported claims',
    example: ['sub', 'name', 'email', 'email_verified', 'preferred_username'],
    type: [String]
  })
  claims_supported: string[];

  @ApiProperty({
    description: 'Code challenge methods supported for PKCE',
    example: ['plain', 'S256'],
    type: [String],
    required: false
  })
  code_challenge_methods_supported?: string[];
}
