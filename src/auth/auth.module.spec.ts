import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { Test, TestingModule } from '@nestjs/testing';
import { CommonModule } from '../common/common.module';
import { DatabaseService } from '../common/services/database.service';

import { DatabaseModule } from '../database/database.module';
import { EmailManagementService } from '../email/email-management.service';
import { EmailService } from '../email/email.service';
import { AuthController } from './auth.controller';
import { AuthModule } from './auth.module';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';

// Mock EmailService to avoid loading native dependencies
const mockEmailService = {
  sendEmail: jest.fn().mockResolvedValue(true),
  sendWelcomeEmail: jest.fn().mockResolvedValue(true),
  sendEmailVerification: jest.fn().mockResolvedValue(true),
  sendPasswordReset: jest.fn().mockResolvedValue(true),
  sendPasswordChanged: jest.fn().mockResolvedValue(true),
  sendEmailChangeVerification: jest.fn().mockResolvedValue(true),
  sendEmailChangeNotification: jest.fn().mockResolvedValue(true),
  send2FAEnabled: jest.fn().mockResolvedValue(true),
  send2FADisabled: jest.fn().mockResolvedValue(true),
  sendNewDeviceLogin: jest.fn().mockResolvedValue(true),
  sendSecurityAlert: jest.fn().mockResolvedValue(true),
  sendTestEmail: jest.fn().mockResolvedValue(true),
};

const mockEmailManagementService = {
  createTemplate: jest.fn(),
  getTemplates: jest.fn(),
  getTemplate: jest.fn(),
  updateTemplate: jest.fn(),
  deleteTemplate: jest.fn(),
  createCampaign: jest.fn(),
  getCampaigns: jest.fn(),
  getCampaign: jest.fn(),
  updateCampaign: jest.fn(),
  deleteCampaign: jest.fn(),
  sendEmail: jest.fn(),
  getEmailLogs: jest.fn(),
  getAnalyticsSummary: jest.fn(),
  getTemplateStats: jest.fn(),
};

const mockDatabaseService = {
  db: jest.fn(),
};

// Mock database provider
const mockDatabase = {
  select: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  values: jest.fn().mockReturnThis(),
  returning: jest.fn().mockResolvedValue([]),
  update: jest.fn().mockReturnThis(),
  set: jest.fn().mockReturnThis(),
  delete: jest.fn().mockReturnThis(),
  execute: jest.fn().mockResolvedValue(undefined),
};



describe('AuthModule', () => {
  let module: TestingModule;
  let configService: ConfigService;
  let jwtService: JwtService;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
        }),
        AuthModule,
      ],
    })
      .overrideProvider(EmailService)
      .useValue(mockEmailService)
      .overrideProvider(EmailManagementService)
      .useValue(mockEmailManagementService)
      .overrideProvider(DatabaseService)
      .useValue(mockDatabaseService)
      .overrideProvider('DB')
      .useValue(mockDatabase)
      .compile();

    configService = module.get<ConfigService>(ConfigService);
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  describe('module imports', () => {
    it('should import DatabaseModule', () => {
      const imports = Reflect.getMetadata('imports', AuthModule);
      expect(imports).toContain(DatabaseModule);
    });

    it('should import PassportModule', () => {
      const imports = Reflect.getMetadata('imports', AuthModule);
      expect(imports).toContain(PassportModule);
    });

    it('should import CommonModule', () => {
      const imports = Reflect.getMetadata('imports', AuthModule);
      expect(imports).toContain(CommonModule);
    });

    it('should have JwtModule configured', () => {
      const imports = Reflect.getMetadata('imports', AuthModule);
      const jwtModuleConfig = imports.find((imp: any) =>
        imp && typeof imp === 'object' && imp.module === JwtModule
      );
      expect(jwtModuleConfig).toBeDefined();
    });
  });

  describe('JWT configuration', () => {
    it('should configure JWT with async factory', () => {
      const jwtService = module.get<JwtService>(JwtService);
      expect(jwtService).toBeDefined();
    });
  });

  describe('providers', () => {
    it('should provide AuthService', () => {
      const authService = module.get(AuthService);
      expect(authService).toBeDefined();
    });

    it('should provide JwtStrategy', () => {
      const jwtStrategy = module.get(JwtStrategy);
      expect(jwtStrategy).toBeDefined();
    });

    it('should have correct providers configuration', () => {
      const providers = Reflect.getMetadata('providers', AuthModule);
      expect(providers).toContain(AuthService);
      expect(providers).toContain(JwtStrategy);
    });
  });

  describe('controllers', () => {
    it('should provide AuthController', () => {
      const authController = module.get(AuthController);
      expect(authController).toBeDefined();
    });

    it('should have correct controllers configuration', () => {
      const controllers = Reflect.getMetadata('controllers', AuthModule);
      expect(controllers).toContain(AuthController);
    });
  });

  describe('exports', () => {
    it('should export AuthService', () => {
      const exports = Reflect.getMetadata('exports', AuthModule);
      expect(exports).toContain(AuthService);
    });

    it('should only export AuthService', () => {
      const exports = Reflect.getMetadata('exports', AuthModule);
      expect(exports).toHaveLength(1);
      expect(exports[0]).toBe(AuthService);
    });
  });

  describe('dependency injection', () => {
    it('should wire up all dependencies correctly', () => {
      expect(() => module.get(AuthService)).not.toThrow();
      expect(() => module.get(AuthController)).not.toThrow();
      expect(() => module.get(JwtStrategy)).not.toThrow();
      expect(() => module.get(JwtService)).not.toThrow();
    });

    it('should provide JwtService from JwtModule', () => {
      const jwtService = module.get<JwtService>(JwtService);
      expect(jwtService).toBeDefined();
      expect(jwtService.sign).toBeDefined();
      expect(jwtService.verify).toBeDefined();
    });

    it('should make AuthService available for other modules', () => {
      // Since AuthService is exported, it should be available for injection
      const authService = module.get(AuthService);
      expect(authService).toBeDefined();
    });
  });

  describe('module structure validation', () => {
    it('should have all required metadata', () => {
      const imports = Reflect.getMetadata('imports', AuthModule);
      const providers = Reflect.getMetadata('providers', AuthModule);
      const controllers = Reflect.getMetadata('controllers', AuthModule);
      const exports = Reflect.getMetadata('exports', AuthModule);

      expect(imports).toBeDefined();
      expect(providers).toBeDefined();
      expect(controllers).toBeDefined();
      expect(exports).toBeDefined();
    });

    it('should not be a global module', () => {
      const isGlobal = Reflect.getMetadata('__module:global__', AuthModule);
      expect(isGlobal).toBeFalsy();
    });

    it('should have correct number of imports', () => {
      const imports = Reflect.getMetadata('imports', AuthModule);
      // DatabaseModule, PassportModule, CommonModule, JwtModule
      expect(imports.length).toBeGreaterThanOrEqual(4);
    });
  });
});
