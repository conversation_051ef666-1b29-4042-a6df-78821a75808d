import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { TokenResponse } from './token-response.dto';

describe('TokenResponse', () => {
  it('should be defined', () => {
    expect(TokenResponse).toBeDefined();
  });

  it('should validate with valid data', async () => {
    const dto = plainToClass(TokenResponse, {
      accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
      refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
      expiresIn: 900,
      tokenType: 'Bearer',
    });

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });

  it('should validate with minimal required data', async () => {
    const dto = plainToClass(TokenResponse, {
      accessToken: 'access-token-123',
      refreshToken: 'refresh-token-456',
      expiresIn: 1800,
    });

    const errors = await validate(dto);
    expect(errors).toHaveLength(0);
  });

  it('should require accessToken', async () => {
    const dto = plainToClass(TokenResponse, {
      refreshToken: 'refresh-token-456',
      expiresIn: 900,
    });

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('accessToken');
  });

  it('should require refreshToken', async () => {
    const dto = plainToClass(TokenResponse, {
      accessToken: 'access-token-123',
      expiresIn: 900,
    });

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('refreshToken');
  });

  it('should require expiresIn', async () => {
    const dto = plainToClass(TokenResponse, {
      accessToken: 'access-token-123',
      refreshToken: 'refresh-token-456',
    });

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('expiresIn');
  });

  it('should validate accessToken as string', async () => {
    const dto = plainToClass(TokenResponse, {
      accessToken: 123,
      refreshToken: 'refresh-token-456',
      expiresIn: 900,
    });

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('accessToken');
  });

  it('should validate refreshToken as string', async () => {
    const dto = plainToClass(TokenResponse, {
      accessToken: 'access-token-123',
      refreshToken: 456,
      expiresIn: 900,
    });

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('refreshToken');
  });

  it('should validate expiresIn as number', async () => {
    const dto = plainToClass(TokenResponse, {
      accessToken: 'access-token-123',
      refreshToken: 'refresh-token-456',
      expiresIn: 'not-a-number',
    });

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('expiresIn');
  });

  it('should validate tokenType as string when provided', async () => {
    const dto = plainToClass(TokenResponse, {
      accessToken: 'access-token-123',
      refreshToken: 'refresh-token-456',
      expiresIn: 900,
      tokenType: 123,
    });

    const errors = await validate(dto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('tokenType');
  });

  it('should have default tokenType value', () => {
    const dto = new TokenResponse();
    expect(dto.tokenType).toBe('Bearer');
  });

  it('should create instance', () => {
    const dto = new TokenResponse();
    expect(dto).toBeInstanceOf(TokenResponse);
  });
});
