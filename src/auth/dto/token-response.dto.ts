import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class TokenResponse {
  @ApiProperty({
    description: 'JWT access token (typically expires in 15-30 minutes)',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @IsString()
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token for obtaining new access tokens (rotated on each use)',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @IsString()
  refreshToken: string;

  @ApiProperty({
    description: 'Access token expiration time in seconds',
    example: 900
  })
  @IsNumber()
  expiresIn: number;

  @ApiProperty({
    description: 'Token type',
    enum: ['Bearer'],
    default: 'Bearer',
    example: 'Bearer'
  })
  @IsOptional()
  @IsString()
  tokenType?: string = 'Bearer';
}
