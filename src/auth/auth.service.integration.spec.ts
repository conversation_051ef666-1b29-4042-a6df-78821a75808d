/**
 * AuthService Integration Tests
 * Tests the AuthService with real database operations
 */

import { BadRequestException, ConflictException, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Test, TestingModule } from '@nestjs/testing';
import { eq } from 'drizzle-orm';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase
} from '../../test/database-setup.js';
import { users, userSessions } from '../database/schema/index.js';
import { EmailService } from '../email/email.service.js';
import { AuthService } from './auth.service.js';

describe('AuthService Integration Tests', () => {
  let service: AuthService;
  let testDb: TestDatabase;
  let jwtService: JwtService;
  let configService: ConfigService;
  let testData: any;

  beforeAll(async () => {
    // Create test database
    testDb = await createTestDatabase();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: 'DB',
          useValue: testDb,
        },
        {
          provide: JwtService,
          useValue: {
            signAsync: jest.fn().mockImplementation((payload) =>
              Promise.resolve(`mock.jwt.token.${payload.sub}`)
            ),
            verify: jest.fn().mockImplementation((token) => {
              const parts = token.split('.');
              if (parts.length === 4 && parts[0] === 'mock') {
                return {
                  sub: parts[3],
                  email: '<EMAIL>',
                  roles: ['user'],
                  sid: 'test-session-id',
                };
              }
              throw new Error('Invalid token');
            }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key, defaultValue) => {
              const config = {
                'JWT_SECRET': 'test-secret',
                'JWT_EXPIRES_IN': '15m',
                'JWT_REFRESH_SECRET': 'test-refresh-secret',
                'JWT_REFRESH_EXPIRES_IN': '7d',
              };
              return config[key] || defaultValue;
            }),
          },
        },
        {
          provide: EmailService,
          useValue: {
            sendWelcomeEmail: jest.fn().mockResolvedValue(true),
            sendPasswordReset: jest.fn().mockResolvedValue(true),
            sendPasswordChanged: jest.fn().mockResolvedValue(true),
            sendNewDeviceLogin: jest.fn().mockResolvedValue(true),
            send2FAEnabled: jest.fn().mockResolvedValue(true),
            send2FADisabled: jest.fn().mockResolvedValue(true),
            sendEmailChangeVerification: jest.fn().mockResolvedValue(true),
            sendEmailChangeNotification: jest.fn().mockResolvedValue(true),
            sendSecurityAlert: jest.fn().mockResolvedValue(true),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    jwtService = module.get<JwtService>(JwtService);
    configService = module.get<ConfigService>(ConfigService);
  });

  beforeEach(async () => {
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
    jest.clearAllMocks(); // Reset mock call counts
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  describe('registerUser', () => {
    it('should register a new user successfully', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        name: 'New User',
      };

      const result = await service.registerUser(registerData);

      expect(result).toMatchObject({
        user: {
          email: '<EMAIL>',
          name: 'New User',
          roles: ['user'],
          emailVerified: false,
        },
        accessToken: expect.stringMatching(/^mock\.jwt\.token\./),
        refreshToken: expect.stringMatching(/^mock\.jwt\.token\./),
        expiresIn: 900,
      });

      // Verify user was created in database
      const [createdUser] = await testDb
        .select()
        .from(users)
        .where(eq(users.email, '<EMAIL>'))
        .limit(1);

      expect(createdUser).toBeDefined();
      expect(createdUser.email).toBe('<EMAIL>');
      expect(createdUser.name).toBe('New User');
      expect(createdUser.emailVerified).toBe(false);
      expect(createdUser.emailVerificationToken).toBeTruthy();
    });

    it('should throw ConflictException for existing email', async () => {
      const registerData = {
        email: testData.testUser.email, // Use existing user email
        password: 'SecurePassword123!',
        name: 'Duplicate User',
      };

      await expect(service.registerUser(registerData)).rejects.toThrow(ConflictException);
      await expect(service.registerUser(registerData)).rejects.toThrow(
        'User with this email already exists'
      );
    });

    it('should hash the password properly', async () => {
      const registerData = {
        email: '<EMAIL>',
        password: 'PlainTextPassword123!',
        name: 'Password Test User',
      };

      await service.registerUser(registerData);

      const [createdUser] = await testDb
        .select()
        .from(users)
        .where(eq(users.email, '<EMAIL>'))
        .limit(1);

      expect(createdUser.password).not.toBe('PlainTextPassword123!');
      expect(createdUser.password).toMatch(/^\$2b\$12\$/); // bcrypt hash format
    });
  });

  describe('loginUser', () => {
    it('should login user successfully with correct credentials', async () => {
      const loginData = {
        email: testData.testUser.email,
        password: 'password123', // This matches the seeded password
        deviceInfo: { browser: 'Chrome', os: 'Windows' },
        platform: 'web',
        location: { country: 'US', city: 'New York' },
      };

      const result = await service.loginUser(loginData);

      expect(result).toMatchObject({
        user: {
          id: testData.testUser.id,
          email: testData.testUser.email,
          name: testData.testUser.name,
          roles: ['user'],
          emailVerified: true,
        },
        accessToken: expect.stringMatching(/^mock\.jwt\.token\./),
        refreshToken: expect.stringMatching(/^mock\.jwt\.token\./),
        expiresIn: 900,
      });

      // Verify session was created
      const sessions = await testDb
        .select()
        .from(userSessions)
        .where(eq(userSessions.userId, testData.testUser.id));

      expect(sessions).toHaveLength(1);
      expect(sessions[0].platform).toBe('web');
      expect(sessions[0].isActive).toBe(true);
      expect(sessions[0].isCurrent).toBe(true);
    });

    it('should throw UnauthorizedException for invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      await expect(service.loginUser(loginData)).rejects.toThrow(UnauthorizedException);
      await expect(service.loginUser(loginData)).rejects.toThrow('Invalid credentials');
    });

    it('should throw UnauthorizedException for invalid password', async () => {
      const loginData = {
        email: testData.testUser.email,
        password: 'wrongpassword',
      };

      await expect(service.loginUser(loginData)).rejects.toThrow(UnauthorizedException);
      await expect(service.loginUser(loginData)).rejects.toThrow('Invalid credentials');
    });

    it('should throw UnauthorizedException for inactive user', async () => {
      // Deactivate the test user
      await testDb
        .update(users)
        .set({ isActive: false })
        .where(eq(users.id, testData.testUser.id));

      const loginData = {
        email: testData.testUser.email,
        password: 'password123',
      };

      await expect(service.loginUser(loginData)).rejects.toThrow(UnauthorizedException);
      await expect(service.loginUser(loginData)).rejects.toThrow('Account is deactivated');
    });

    it('should set previous sessions to not current', async () => {
      // Create an existing session with proper UUID
      const existingSessionId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'; // Valid UUID
      await testDb.insert(userSessions).values({
        id: existingSessionId,
        userId: testData.testUser.id,
        platform: 'web',
        deviceInfo: {},
        location: {},
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true,
        isCurrent: true,
        requiresVerification: false,
        lastActivityAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const loginData = {
        email: testData.testUser.email,
        password: 'password123',
      };

      await service.loginUser(loginData);

      // Check that the existing session is no longer current
      const [existingSession] = await testDb
        .select()
        .from(userSessions)
        .where(eq(userSessions.id, existingSessionId))
        .limit(1);

      expect(existingSession.isCurrent).toBe(false);

      // Check that there's exactly one current session
      const currentSessions = await testDb
        .select()
        .from(userSessions)
        .where(eq(userSessions.userId, testData.testUser.id));

      const currentSessionsCount = currentSessions.filter(s => s.isCurrent).length;
      expect(currentSessionsCount).toBe(1);
    });
  });

  describe('logoutUser', () => {
    let sessionId: string;

    beforeEach(async () => {
      // Create a test session with proper UUID
      sessionId = 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a22'; // Valid UUID
      await testDb.insert(userSessions).values({
        id: sessionId,
        userId: testData.testUser.id,
        platform: 'web',
        deviceInfo: {},
        location: {},
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true,
        isCurrent: true,
        requiresVerification: false,
        lastActivityAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    });

    it('should logout user successfully', async () => {
      const user = { id: testData.testUser.id, sid: sessionId };
      const req = { ip: '127.0.0.1' };

      const result = await service.logoutUser(user, req);

      expect(result).toEqual({ message: 'Logged out successfully' });

      // Verify session was deactivated
      const [session] = await testDb
        .select()
        .from(userSessions)
        .where(eq(userSessions.id, sessionId))
        .limit(1);

      expect(session.isActive).toBe(false);
      expect(session.revokedAt).toBeTruthy();
      expect(session.revokedByIp).toBe('127.0.0.1');
      expect(session.revokedReason).toBe('User logout');
    });

    it('should throw BadRequestException when no session ID provided', async () => {
      const user = { id: testData.testUser.id }; // No sid
      const req = { ip: '127.0.0.1' };

      await expect(service.logoutUser(user, req)).rejects.toThrow(BadRequestException);
      await expect(service.logoutUser(user, req)).rejects.toThrow('No session ID found in token');
    });

    it('should handle logout when session does not exist', async () => {
      const user = { id: testData.testUser.id, sid: 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a33' }; // Valid UUID that doesn't exist
      const req = { ip: '127.0.0.1' };

      const result = await service.logoutUser(user, req);

      expect(result).toEqual({ message: 'Logged out successfully' });
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully with valid refresh token', async () => {
      const refreshTokenData = {
        refreshToken: `mock.jwt.token.${testData.testUser.id}`,
      };

      const result = await service.refreshToken(refreshTokenData);

      expect(result).toMatchObject({
        accessToken: expect.stringMatching(/^mock\.jwt\.token\./),
        refreshToken: expect.stringMatching(/^mock\.jwt\.token\./),
        expiresIn: 900,
        tokenType: 'Bearer',
      });
    });

    it('should throw UnauthorizedException for invalid refresh token', async () => {
      const refreshTokenData = {
        refreshToken: 'invalid.token.format',
      };

      await expect(service.refreshToken(refreshTokenData)).rejects.toThrow(UnauthorizedException);
      await expect(service.refreshToken(refreshTokenData)).rejects.toThrow('Invalid refresh token');
    });

    it('should throw UnauthorizedException for inactive user', async () => {
      // Deactivate the user
      await testDb
        .update(users)
        .set({ isActive: false })
        .where(eq(users.id, testData.testUser.id));

      const refreshTokenData = {
        refreshToken: `mock.jwt.token.${testData.testUser.id}`,
      };

      await expect(service.refreshToken(refreshTokenData)).rejects.toThrow(UnauthorizedException);
      await expect(service.refreshToken(refreshTokenData)).rejects.toThrow('Invalid refresh token');
    });
  });

  describe('forgotPassword', () => {
    it('should handle forgot password for existing user', async () => {
      const forgotPasswordData = {
        email: testData.testUser.email,
      };

      const result = await service.forgotPassword(forgotPasswordData);

      expect(result).toEqual({
        message: 'If the email exists, a password reset link has been sent',
      });

      // Verify reset token was set in database
      const [user] = await testDb
        .select()
        .from(users)
        .where(eq(users.id, testData.testUser.id))
        .limit(1);

      expect(user.passwordResetToken).toBeTruthy();
      expect(user.passwordResetExpires).toBeTruthy();
      expect(user.passwordResetExpires.getTime()).toBeGreaterThan(Date.now());
    });

    it('should return same message for non-existent email (security)', async () => {
      const forgotPasswordData = {
        email: '<EMAIL>',
      };

      const result = await service.forgotPassword(forgotPasswordData);

      expect(result).toEqual({
        message: 'If the email exists, a password reset link has been sent',
      });
    });
  });

  describe('resetPassword', () => {
    let resetToken: string;

    beforeEach(async () => {
      // Set up a reset token for the test user
      resetToken = 'test-reset-token-123';
      const resetExpires = new Date(Date.now() + 3600000); // 1 hour from now

      await testDb
        .update(users)
        .set({
          passwordResetToken: resetToken,
          passwordResetExpires: resetExpires,
        })
        .where(eq(users.id, testData.testUser.id));
    });

    it('should reset password successfully with valid token', async () => {
      const resetData = {
        token: resetToken,
        password: 'NewSecurePassword123!',
      };

      const result = await service.resetPassword(resetData);

      expect(result).toEqual({ message: 'Password reset successfully' });

      // Verify password was changed and reset token was cleared
      const [user] = await testDb
        .select()
        .from(users)
        .where(eq(users.id, testData.testUser.id))
        .limit(1);

      expect(user.password).not.toBe(testData.testUser.password);
      expect(user.password).toMatch(/^\$2b\$12\$/); // bcrypt hash format
      expect(user.passwordResetToken).toBeNull();
      expect(user.passwordResetExpires).toBeNull();
    });

    it('should throw BadRequestException for invalid token', async () => {
      const resetData = {
        token: 'invalid-token',
        password: 'NewSecurePassword123!',
      };

      await expect(service.resetPassword(resetData)).rejects.toThrow(BadRequestException);
      await expect(service.resetPassword(resetData)).rejects.toThrow(
        'Invalid or expired reset token'
      );
    });

    it('should throw BadRequestException for expired token', async () => {
      // Set expired reset token
      const expiredDate = new Date(Date.now() - 3600000); // 1 hour ago
      await testDb
        .update(users)
        .set({
          passwordResetToken: resetToken,
          passwordResetExpires: expiredDate,
        })
        .where(eq(users.id, testData.testUser.id));

      const resetData = {
        token: resetToken,
        password: 'NewSecurePassword123!',
      };

      await expect(service.resetPassword(resetData)).rejects.toThrow(BadRequestException);
      await expect(service.resetPassword(resetData)).rejects.toThrow(
        'Invalid or expired reset token'
      );
    });
  });

  describe('generateTokens (private method integration)', () => {
    it('should generate tokens with correct expiration times', async () => {
      const loginData = {
        email: testData.testUser.email,
        password: 'password123',
      };

      const result = await service.loginUser(loginData);

      expect(result.expiresIn).toBe(900); // 15 minutes in seconds
      expect(jwtService.signAsync).toHaveBeenCalledTimes(2); // access + refresh token
    });
  });

  describe('parseExpirationToSeconds (private method integration)', () => {
    it('should handle different time formats through config', async () => {
      // Test different expiration formats by mocking config values
      const configMock = configService.get as jest.Mock;

      // Test seconds format
      configMock.mockImplementation((key, defaultValue) => {
        if (key === 'JWT_EXPIRES_IN') return '30s';
        return defaultValue;
      });

      const loginData = {
        email: testData.testUser.email,
        password: 'password123',
      };

      let result = await service.loginUser(loginData);
      expect(result.expiresIn).toBe(30);

      // Reset for next test
      await cleanTestDatabase(testDb);
      testData = await seedTestDatabase(testDb);

      // Test hours format
      configMock.mockImplementation((key, defaultValue) => {
        if (key === 'JWT_EXPIRES_IN') return '2h';
        return defaultValue;
      });

      result = await service.loginUser(loginData);
      expect(result.expiresIn).toBe(7200); // 2 hours in seconds

      // Reset for next test
      await cleanTestDatabase(testDb);
      testData = await seedTestDatabase(testDb);

      // Test days format
      configMock.mockImplementation((key, defaultValue) => {
        if (key === 'JWT_EXPIRES_IN') return '1d';
        return defaultValue;
      });

      result = await service.loginUser(loginData);
      expect(result.expiresIn).toBe(86400); // 1 day in seconds

      // Reset for next test
      await cleanTestDatabase(testDb);
      testData = await seedTestDatabase(testDb);

      // Test invalid format (should default to 15 minutes)
      configMock.mockImplementation((key, defaultValue) => {
        if (key === 'JWT_EXPIRES_IN') return 'invalid';
        return defaultValue;
      });

      result = await service.loginUser(loginData);
      expect(result.expiresIn).toBe(900); // Default 15 minutes

      // Restore original mock
      configMock.mockImplementation((key, defaultValue) => {
        const config = {
          'JWT_SECRET': 'test-secret',
          'JWT_EXPIRES_IN': '15m',
          'JWT_REFRESH_SECRET': 'test-refresh-secret',
          'JWT_REFRESH_EXPIRES_IN': '7d',
        };
        return config[key] || defaultValue;
      });
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle refresh token without session ID', async () => {
      // Mock JWT service to return payload without sid
      const jwtMock = jwtService.verify as jest.Mock;
      jwtMock.mockImplementationOnce(() => ({
        sub: testData.testUser.id,
        email: testData.testUser.email,
        roles: ['user'],
        // No sid property
      }));

      const refreshTokenData = {
        refreshToken: `mock.jwt.token.${testData.testUser.id}`,
      };

      const result = await service.refreshToken(refreshTokenData);

      expect(result).toMatchObject({
        accessToken: expect.stringMatching(/^mock\.jwt\.token\./),
        refreshToken: expect.stringMatching(/^mock\.jwt\.token\./),
        expiresIn: 900,
        tokenType: 'Bearer',
      });
    });

    it('should handle login with session expiration calculation', async () => {
      // Test that session expiration is calculated correctly
      const loginData = {
        email: testData.testUser.email,
        password: 'password123',
        platform: 'desktop',
      };

      const beforeLogin = Date.now();
      await service.loginUser(loginData);
      const afterLogin = Date.now();

      // Verify session was created with correct expiration
      const sessions = await testDb
        .select()
        .from(userSessions)
        .where(eq(userSessions.userId, testData.testUser.id));

      expect(sessions).toHaveLength(1);
      const session = sessions[0];

      // Session should expire in approximately 15 minutes (900 seconds)
      const expectedExpiration = beforeLogin + 900000; // 15 minutes in ms
      const actualExpiration = session.expiresAt.getTime();

      expect(actualExpiration).toBeGreaterThanOrEqual(expectedExpiration - 1000); // Allow 1 second tolerance
      expect(actualExpiration).toBeLessThanOrEqual(afterLogin + 900000);
    });

    it('should handle logout with missing request object', async () => {
      // Create a test session
      const sessionId = 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a44';
      await testDb.insert(userSessions).values({
        id: sessionId,
        userId: testData.testUser.id,
        platform: 'web',
        deviceInfo: {},
        location: {},
        expiresAt: new Date(Date.now() + 3600000),
        isActive: true,
        isCurrent: true,
        requiresVerification: false,
        lastActivityAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      const user = { id: testData.testUser.id, sid: sessionId };
      const req = null; // Missing request object

      const result = await service.logoutUser(user, req);

      expect(result).toEqual({ message: 'Logged out successfully' });

      // Verify session was deactivated with null IP
      const [session] = await testDb
        .select()
        .from(userSessions)
        .where(eq(userSessions.id, sessionId))
        .limit(1);

      expect(session.isActive).toBe(false);
      expect(session.revokedAt).toBeTruthy();
      expect(session.revokedByIp).toBeNull();
      expect(session.revokedReason).toBe('User logout');
    });

    it('should handle user not found in refresh token', async () => {
      const nonExistentUserId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a99';

      // Mock JWT service to return payload with non-existent user ID
      const jwtMock = jwtService.verify as jest.Mock;
      jwtMock.mockImplementationOnce(() => ({
        sub: nonExistentUserId,
        email: '<EMAIL>',
        roles: ['user'],
        sid: 'test-session-id',
      }));

      const refreshTokenData = {
        refreshToken: `mock.jwt.token.${nonExistentUserId}`,
      };

      await expect(service.refreshToken(refreshTokenData)).rejects.toThrow(UnauthorizedException);
      await expect(service.refreshToken(refreshTokenData)).rejects.toThrow('Invalid refresh token');
    });
  });
});
