import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class PricingStructure {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  basePrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isFree?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  subscriptionPlans?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  instancePricing?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  volumeDiscounts?: any[];

}
