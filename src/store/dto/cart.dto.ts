import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class Cart {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  items?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  subtotal?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  discounts?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  taxes?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  total?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  appliedCoupons?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  estimatedSavings?: number;

}
