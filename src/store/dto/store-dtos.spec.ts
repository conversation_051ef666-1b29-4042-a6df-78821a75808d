import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

// Import all store DTOs
import { AddToCartRequest } from './add-to-cart-request.dto';
import { CartItem } from './cart-item.dto';
import { Cart } from './cart.dto';
import { InstancePricingTier } from './instance-pricing-tier.dto';
import { PricingOption } from './pricing-option.dto';
import { PricingResponse } from './pricing-response.dto';
import { PricingStructure } from './pricing-structure.dto';
import { RepositoryPricing } from './repository-pricing.dto';
import { StoreFilters } from './store-filters.dto';
import { StoreItemDetailed } from './store-item-detailed.dto';
import { StoreItem } from './store-item.dto';
import { SubscriptionPlan } from './subscription-plan.dto';
import { UpdateCartItemRequest } from './update-cart-item-request.dto';

describe('Store DTOs', () => {
  describe('AddToCartRequest', () => {
    it('should be defined', () => {
      expect(AddToCartRequest).toBeDefined();
    });

    it('should validate valid data', async () => {
      const dto = plainToClass(AddToCartRequest, {
        itemId: 'item-123',
        planId: 'plan-456',
        instances: 2,
        duration: '1month',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require itemId', async () => {
      const dto = plainToClass(AddToCartRequest, {
        planId: 'plan-456',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('itemId');
    });

    it('should require planId', async () => {
      const dto = plainToClass(AddToCartRequest, {
        itemId: 'item-123',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('planId');
    });

    it('should allow optional fields', async () => {
      const dto = plainToClass(AddToCartRequest, {
        itemId: 'item-123',
        planId: 'plan-456',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('CartItem', () => {
    it('should be defined', () => {
      expect(CartItem).toBeDefined();
    });

    it('should validate with all optional fields', async () => {
      const dto = plainToClass(CartItem, {
        id: 'cart-item-123',
        itemId: 'item-456',
        itemName: 'Test Item',
        planId: 'plan-789',
        planName: 'Premium Plan',
        instances: 3,
        duration: '6months',
        unitPrice: 99.99,
        totalPrice: 299.97,
        discounts: [{ type: 'volume', amount: 10 }],
        addedAt: '2023-01-01T00:00:00Z',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with empty object', async () => {
      const dto = plainToClass(CartItem, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });
  });

  describe('Cart', () => {
    it('should be defined', () => {
      expect(Cart).toBeDefined();
    });

    it('should create instance', () => {
      const cart = new Cart();
      expect(cart).toBeInstanceOf(Cart);
    });
  });

  describe('InstancePricingTier', () => {
    it('should be defined', () => {
      expect(InstancePricingTier).toBeDefined();
    });

    it('should create instance', () => {
      const tier = new InstancePricingTier();
      expect(tier).toBeInstanceOf(InstancePricingTier);
    });
  });

  describe('PricingOption', () => {
    it('should be defined', () => {
      expect(PricingOption).toBeDefined();
    });

    it('should create instance', () => {
      const option = new PricingOption();
      expect(option).toBeInstanceOf(PricingOption);
    });
  });

  describe('PricingResponse', () => {
    it('should be defined', () => {
      expect(PricingResponse).toBeDefined();
    });

    it('should create instance', () => {
      const response = new PricingResponse();
      expect(response).toBeInstanceOf(PricingResponse);
    });
  });

  describe('PricingStructure', () => {
    it('should be defined', () => {
      expect(PricingStructure).toBeDefined();
    });

    it('should create instance', () => {
      const structure = new PricingStructure();
      expect(structure).toBeInstanceOf(PricingStructure);
    });
  });

  describe('RepositoryPricing', () => {
    it('should be defined', () => {
      expect(RepositoryPricing).toBeDefined();
    });

    it('should create instance', () => {
      const pricing = new RepositoryPricing();
      expect(pricing).toBeInstanceOf(RepositoryPricing);
    });
  });

  describe('StoreFilters', () => {
    it('should be defined', () => {
      expect(StoreFilters).toBeDefined();
    });

    it('should create instance', () => {
      const filters = new StoreFilters();
      expect(filters).toBeInstanceOf(StoreFilters);
    });
  });

  describe('StoreItemDetailed', () => {
    it('should be defined', () => {
      expect(StoreItemDetailed).toBeDefined();
    });

    it('should create instance', () => {
      const item = new StoreItemDetailed();
      expect(item).toBeInstanceOf(StoreItemDetailed);
    });
  });

  describe('StoreItem', () => {
    it('should be defined', () => {
      expect(StoreItem).toBeDefined();
    });

    it('should create instance', () => {
      const item = new StoreItem();
      expect(item).toBeInstanceOf(StoreItem);
    });
  });

  describe('SubscriptionPlan', () => {
    it('should be defined', () => {
      expect(SubscriptionPlan).toBeDefined();
    });

    it('should create instance', () => {
      const plan = new SubscriptionPlan();
      expect(plan).toBeInstanceOf(SubscriptionPlan);
    });
  });

  describe('UpdateCartItemRequest', () => {
    it('should be defined', () => {
      expect(UpdateCartItemRequest).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(UpdateCartItemRequest, {
        instances: 5,
        duration: '3months',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with empty object', async () => {
      const dto = plainToClass(UpdateCartItemRequest, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate instances as number', async () => {
      const dto = plainToClass(UpdateCartItemRequest, {
        instances: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('instances');
    });

    it('should validate duration as string', async () => {
      const dto = plainToClass(UpdateCartItemRequest, {
        duration: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('duration');
    });
  });
});
