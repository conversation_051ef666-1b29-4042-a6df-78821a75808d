import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class StoreFilters {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  categories?: object[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  priceRanges?: object[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  tags?: object[];

}
