import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class PricingResponse {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  itemId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  requestedInstances?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  requestedDuration?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  options?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  appliedDiscounts?: any[];

}
