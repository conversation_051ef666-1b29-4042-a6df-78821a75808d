import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class InstancePricingTier {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  minInstances?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  maxInstances?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  pricePerInstance?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  discountPercentage?: number;

}
