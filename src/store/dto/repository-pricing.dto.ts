import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class RepositoryPricing {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  basePrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  subscriptionPlans?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  minimumPrice?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  suggestedPrice?: number;

}
