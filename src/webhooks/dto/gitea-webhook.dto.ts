import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsNumber, IsOptional, IsString, ValidateNested } from 'class-validator';

export class GiteaUserDto {
  @ApiProperty({ description: 'User ID' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: 'Username' })
  @IsString()
  login: string;

  @ApiProperty({ description: 'Full name' })
  @IsString()
  full_name: string;

  @ApiProperty({ description: 'Email address' })
  @IsString()
  email: string;

  @ApiProperty({ description: 'Avatar URL' })
  @IsString()
  avatar_url: string;

  @ApiProperty({ description: 'Language preference' })
  @IsString()
  language: string;

  @ApiProperty({ description: 'Is admin user' })
  @IsBoolean()
  is_admin: boolean;

  @ApiProperty({ description: 'Last login timestamp' })
  @IsString()
  last_login: string;

  @ApiProperty({ description: 'Created timestamp' })
  @IsString()
  created: string;

  @ApiProperty({ description: 'Is restricted user' })
  @IsBoolean()
  restricted: boolean;

  @ApiProperty({ description: 'Is active user' })
  @IsBoolean()
  active: boolean;

  @ApiProperty({ description: 'Login prohibited' })
  @IsBoolean()
  prohibit_login: boolean;

  @ApiProperty({ description: 'User location' })
  @IsString()
  location: string;

  @ApiProperty({ description: 'User website' })
  @IsString()
  website: string;

  @ApiProperty({ description: 'User description' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Profile visibility' })
  @IsString()
  visibility: string;

  @ApiProperty({ description: 'Followers count' })
  @IsNumber()
  followers_count: number;

  @ApiProperty({ description: 'Following count' })
  @IsNumber()
  following_count: number;

  @ApiProperty({ description: 'Starred repositories count' })
  @IsNumber()
  starred_repos_count: number;

  @ApiProperty({ description: 'Username (alias for login)' })
  @IsString()
  username: string;
}

export class GiteaRepositoryDto {
  @ApiProperty({ description: 'Repository ID' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: 'Repository name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Full repository name (owner/repo)' })
  @IsString()
  full_name: string;

  @ApiProperty({ description: 'Repository owner', type: GiteaUserDto })
  @ValidateNested()
  @Type(() => GiteaUserDto)
  owner: GiteaUserDto;

  @ApiProperty({ description: 'Is private repository' })
  @IsBoolean()
  private: boolean;

  @ApiProperty({ description: 'HTML URL' })
  @IsString()
  html_url: string;

  @ApiProperty({ description: 'Clone URL' })
  @IsString()
  clone_url: string;

  @ApiProperty({ description: 'SSH URL' })
  @IsString()
  ssh_url: string;

  @ApiProperty({ description: 'Repository description' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Repository website' })
  @IsString()
  website: string;

  @ApiProperty({ description: 'Stars count' })
  @IsNumber()
  stars_count: number;

  @ApiProperty({ description: 'Forks count' })
  @IsNumber()
  forks_count: number;

  @ApiProperty({ description: 'Watchers count' })
  @IsNumber()
  watchers_count: number;

  @ApiProperty({ description: 'Open issues count' })
  @IsNumber()
  open_issues_count: number;

  @ApiProperty({ description: 'Default branch' })
  @IsString()
  default_branch: string;

  @ApiProperty({ description: 'Created timestamp' })
  @IsString()
  created_at: string;

  @ApiProperty({ description: 'Updated timestamp' })
  @IsString()
  updated_at: string;

  @ApiProperty({ description: 'Last push timestamp' })
  @IsString()
  pushed_at: string;
}

export class GiteaWebhookDto {
  @ApiProperty({ description: 'Webhook action' })
  @IsString()
  action: string;

  @ApiProperty({ description: 'Issue/PR number', required: false })
  @IsOptional()
  @IsNumber()
  number?: number;

  @ApiProperty({ description: 'Repository information', type: GiteaRepositoryDto })
  @ValidateNested()
  @Type(() => GiteaRepositoryDto)
  repository: GiteaRepositoryDto;

  @ApiProperty({ description: 'Event sender', type: GiteaUserDto })
  @ValidateNested()
  @Type(() => GiteaUserDto)
  sender: GiteaUserDto;
}

export class GiteaCommitDto {
  @ApiProperty({ description: 'Commit ID' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'Commit message' })
  @IsString()
  message: string;

  @ApiProperty({ description: 'Commit URL' })
  @IsString()
  url: string;

  @ApiProperty({ description: 'Added files', type: [String] })
  @IsArray()
  @IsString({ each: true })
  added: string[];

  @ApiProperty({ description: 'Modified files', type: [String] })
  @IsArray()
  @IsString({ each: true })
  modified: string[];

  @ApiProperty({ description: 'Removed files', type: [String] })
  @IsArray()
  @IsString({ each: true })
  removed: string[];
}

export class GiteaPushWebhookDto {
  @ApiProperty({ description: 'Repository information', type: GiteaRepositoryDto })
  @ValidateNested()
  @Type(() => GiteaRepositoryDto)
  repository: GiteaRepositoryDto;

  @ApiProperty({ description: 'Push author', type: GiteaUserDto })
  @ValidateNested()
  @Type(() => GiteaUserDto)
  pusher: GiteaUserDto;

  @ApiProperty({ description: 'Commits in push', type: [GiteaCommitDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GiteaCommitDto)
  commits: GiteaCommitDto[];

  @ApiProperty({ description: 'Reference (branch/tag)' })
  @IsString()
  ref: string;

  @ApiProperty({ description: 'Before commit SHA' })
  @IsString()
  before: string;

  @ApiProperty({ description: 'After commit SHA' })
  @IsString()
  after: string;
}

export class GiteaReleaseDto {
  @ApiProperty({ description: 'Release ID' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: 'Release tag name' })
  @IsString()
  tag_name: string;

  @ApiProperty({ description: 'Release name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Release body/description' })
  @IsString()
  body: string;

  @ApiProperty({ description: 'Is draft release' })
  @IsBoolean()
  draft: boolean;

  @ApiProperty({ description: 'Is prerelease' })
  @IsBoolean()
  prerelease: boolean;

  @ApiProperty({ description: 'Created timestamp' })
  @IsString()
  created_at: string;

  @ApiProperty({ description: 'Published timestamp' })
  @IsString()
  published_at: string;
}

export class GiteaReleaseWebhookDto {
  @ApiProperty({ description: 'Webhook action' })
  @IsString()
  action: string;

  @ApiProperty({ description: 'Release information', type: GiteaReleaseDto })
  @ValidateNested()
  @Type(() => GiteaReleaseDto)
  release: GiteaReleaseDto;

  @ApiProperty({ description: 'Repository information', type: GiteaRepositoryDto })
  @ValidateNested()
  @Type(() => GiteaRepositoryDto)
  repository: GiteaRepositoryDto;

  @ApiProperty({ description: 'Event sender', type: GiteaUserDto })
  @ValidateNested()
  @Type(() => GiteaUserDto)
  sender: GiteaUserDto;
}
