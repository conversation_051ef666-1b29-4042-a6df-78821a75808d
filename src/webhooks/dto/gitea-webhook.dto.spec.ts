import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

import {
  GiteaCommitDto,
  GiteaPushWebhookDto,
  GiteaReleaseDto,
  GiteaReleaseWebhookDto,
  GiteaRepositoryDto,
  GiteaUserDto,
  GiteaWebhookDto,
} from './gitea-webhook.dto';

describe('Gitea Webhook DTOs', () => {
  describe('GiteaUserDto', () => {
    it('should be defined', () => {
      expect(GiteaUserDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(GiteaUserDto, {
        id: 123,
        login: 'testuser',
        full_name: 'Test User',
        email: '<EMAIL>',
        avatar_url: 'https://example.com/avatar.png',
        language: 'en-US',
        is_admin: false,
        last_login: '2023-01-01T00:00:00Z',
        created: '2022-01-01T00:00:00Z',
        restricted: false,
        active: true,
        prohibit_login: false,
        location: 'Test City',
        website: 'https://example.com',
        description: 'A test user',
        visibility: 'public',
        followers_count: 10,
        following_count: 5,
        starred_repos_count: 20,
        username: 'testuser',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require id as number', async () => {
      const dto = plainToClass(GiteaUserDto, {
        id: 'not-a-number',
        login: 'testuser',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('id');
    });

    it('should require login as string', async () => {
      const dto = plainToClass(GiteaUserDto, {
        id: 123,
        login: 456,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('login');
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(GiteaUserDto, {
        id: 123,
        login: 'testuser',
        full_name: 'Test User',
        email: '<EMAIL>',
        avatar_url: 'https://example.com/avatar.png',
        language: 'en-US',
        is_admin: 'not-a-boolean',
        last_login: '2023-01-01T00:00:00Z',
        created: '2022-01-01T00:00:00Z',
        restricted: false,
        active: true,
        prohibit_login: false,
        location: 'Test City',
        website: 'https://example.com',
        description: 'A test user',
        visibility: 'public',
        followers_count: 10,
        following_count: 5,
        starred_repos_count: 20,
        username: 'testuser',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const isAdminError = errors.find(error => error.property === 'is_admin');
      expect(isAdminError).toBeDefined();
    });
  });

  describe('GiteaRepositoryDto', () => {
    it('should be defined', () => {
      expect(GiteaRepositoryDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(GiteaRepositoryDto, {
        id: 456,
        name: 'test-repo',
        full_name: 'testuser/test-repo',
        owner: {
          id: 123,
          login: 'testuser',
          full_name: 'Test User',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar.png',
          language: 'en-US',
          is_admin: false,
          last_login: '2023-01-01T00:00:00Z',
          created: '2022-01-01T00:00:00Z',
          restricted: false,
          active: true,
          prohibit_login: false,
          location: 'Test City',
          website: 'https://example.com',
          description: 'A test user',
          visibility: 'public',
          followers_count: 10,
          following_count: 5,
          starred_repos_count: 20,
          username: 'testuser',
        },
        private: false,
        html_url: 'https://git.example.com/testuser/test-repo',
        clone_url: 'https://git.example.com/testuser/test-repo.git',
        ssh_url: '*******************:testuser/test-repo.git',
        description: 'A test repository',
        website: 'https://example.com',
        stars_count: 5,
        forks_count: 2,
        watchers_count: 3,
        open_issues_count: 1,
        default_branch: 'main',
        created_at: '2022-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        pushed_at: '2023-01-01T12:00:00Z',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require id as number', async () => {
      const dto = plainToClass(GiteaRepositoryDto, {
        id: 'not-a-number',
        name: 'test-repo',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('id');
    });

    it('should validate nested owner', async () => {
      const dto = plainToClass(GiteaRepositoryDto, {
        id: 456,
        name: 'test-repo',
        owner: {
          id: 'invalid-id',
          login: 'testuser',
        },
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
    });
  });

  describe('GiteaWebhookDto', () => {
    it('should be defined', () => {
      expect(GiteaWebhookDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(GiteaWebhookDto, {
        action: 'opened',
        number: 1,
        repository: {
          id: 456,
          name: 'test-repo',
          full_name: 'testuser/test-repo',
          owner: {
            id: 123,
            login: 'testuser',
            full_name: 'Test User',
            email: '<EMAIL>',
            avatar_url: 'https://example.com/avatar.png',
            language: 'en-US',
            is_admin: false,
            last_login: '2023-01-01T00:00:00Z',
            created: '2022-01-01T00:00:00Z',
            restricted: false,
            active: true,
            prohibit_login: false,
            location: 'Test City',
            website: 'https://example.com',
            description: 'A test user',
            visibility: 'public',
            followers_count: 10,
            following_count: 5,
            starred_repos_count: 20,
            username: 'testuser',
          },
          private: false,
          html_url: 'https://git.example.com/testuser/test-repo',
          clone_url: 'https://git.example.com/testuser/test-repo.git',
          ssh_url: '*******************:testuser/test-repo.git',
          description: 'A test repository',
          website: 'https://example.com',
          stars_count: 5,
          forks_count: 2,
          watchers_count: 3,
          open_issues_count: 1,
          default_branch: 'main',
          created_at: '2022-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          pushed_at: '2023-01-01T12:00:00Z',
        },
        sender: {
          id: 123,
          login: 'testuser',
          full_name: 'Test User',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar.png',
          language: 'en-US',
          is_admin: false,
          last_login: '2023-01-01T00:00:00Z',
          created: '2022-01-01T00:00:00Z',
          restricted: false,
          active: true,
          prohibit_login: false,
          location: 'Test City',
          website: 'https://example.com',
          description: 'A test user',
          visibility: 'public',
          followers_count: 10,
          following_count: 5,
          starred_repos_count: 20,
          username: 'testuser',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require action as string', async () => {
      const dto = plainToClass(GiteaWebhookDto, {
        action: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('action');
    });

    it('should validate optional number field', async () => {
      const dto = plainToClass(GiteaWebhookDto, {
        action: 'opened',
        number: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('number');
    });
  });

  describe('GiteaCommitDto', () => {
    it('should be defined', () => {
      expect(GiteaCommitDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(GiteaCommitDto, {
        id: 'abc123def456',
        message: 'Fix bug in authentication',
        url: 'https://git.example.com/testuser/test-repo/commit/abc123def456',
        added: ['src/new-file.ts'],
        modified: ['src/auth.ts', 'README.md'],
        removed: ['src/old-file.ts'],
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require id as string', async () => {
      const dto = plainToClass(GiteaCommitDto, {
        id: 123,
        message: 'Test commit',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('id');
    });

    it('should validate arrays', async () => {
      const dto = plainToClass(GiteaCommitDto, {
        id: 'abc123',
        message: 'Test commit',
        url: 'https://example.com',
        added: 'not-an-array',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('added');
    });
  });

  describe('GiteaPushWebhookDto', () => {
    it('should be defined', () => {
      expect(GiteaPushWebhookDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(GiteaPushWebhookDto, {
        repository: {
          id: 456,
          name: 'test-repo',
          full_name: 'testuser/test-repo',
          owner: {
            id: 123,
            login: 'testuser',
            full_name: 'Test User',
            email: '<EMAIL>',
            avatar_url: 'https://example.com/avatar.png',
            language: 'en-US',
            is_admin: false,
            last_login: '2023-01-01T00:00:00Z',
            created: '2022-01-01T00:00:00Z',
            restricted: false,
            active: true,
            prohibit_login: false,
            location: 'Test City',
            website: 'https://example.com',
            description: 'A test user',
            visibility: 'public',
            followers_count: 10,
            following_count: 5,
            starred_repos_count: 20,
            username: 'testuser',
          },
          private: false,
          html_url: 'https://git.example.com/testuser/test-repo',
          clone_url: 'https://git.example.com/testuser/test-repo.git',
          ssh_url: '*******************:testuser/test-repo.git',
          description: 'A test repository',
          website: 'https://example.com',
          stars_count: 5,
          forks_count: 2,
          watchers_count: 3,
          open_issues_count: 1,
          default_branch: 'main',
          created_at: '2022-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          pushed_at: '2023-01-01T12:00:00Z',
        },
        pusher: {
          id: 123,
          login: 'testuser',
          full_name: 'Test User',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar.png',
          language: 'en-US',
          is_admin: false,
          last_login: '2023-01-01T00:00:00Z',
          created: '2022-01-01T00:00:00Z',
          restricted: false,
          active: true,
          prohibit_login: false,
          location: 'Test City',
          website: 'https://example.com',
          description: 'A test user',
          visibility: 'public',
          followers_count: 10,
          following_count: 5,
          starred_repos_count: 20,
          username: 'testuser',
        },
        commits: [
          {
            id: 'abc123def456',
            message: 'Fix bug in authentication',
            url: 'https://git.example.com/testuser/test-repo/commit/abc123def456',
            added: ['src/new-file.ts'],
            modified: ['src/auth.ts'],
            removed: [],
          },
        ],
        ref: 'refs/heads/main',
        before: 'def456abc789',
        after: 'abc123def456',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate commits array', async () => {
      const dto = plainToClass(GiteaPushWebhookDto, {
        commits: 'not-an-array',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('commits');
    });
  });

  describe('GiteaReleaseDto', () => {
    it('should be defined', () => {
      expect(GiteaReleaseDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(GiteaReleaseDto, {
        id: 789,
        tag_name: 'v1.0.0',
        name: 'Version 1.0.0',
        body: 'Initial release with basic features',
        draft: false,
        prerelease: false,
        created_at: '2023-01-01T00:00:00Z',
        published_at: '2023-01-01T12:00:00Z',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require id as number', async () => {
      const dto = plainToClass(GiteaReleaseDto, {
        id: 'not-a-number',
        tag_name: 'v1.0.0',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('id');
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(GiteaReleaseDto, {
        id: 789,
        tag_name: 'v1.0.0',
        name: 'Version 1.0.0',
        body: 'Initial release',
        draft: 'not-a-boolean',
        prerelease: false,
        created_at: '2023-01-01T00:00:00Z',
        published_at: '2023-01-01T12:00:00Z',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const draftError = errors.find(error => error.property === 'draft');
      expect(draftError).toBeDefined();
    });
  });

  describe('GiteaReleaseWebhookDto', () => {
    it('should be defined', () => {
      expect(GiteaReleaseWebhookDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(GiteaReleaseWebhookDto, {
        action: 'published',
        release: {
          id: 789,
          tag_name: 'v1.0.0',
          name: 'Version 1.0.0',
          body: 'Initial release',
          draft: false,
          prerelease: false,
          created_at: '2023-01-01T00:00:00Z',
          published_at: '2023-01-01T12:00:00Z',
        },
        repository: {
          id: 456,
          name: 'test-repo',
          full_name: 'testuser/test-repo',
          owner: {
            id: 123,
            login: 'testuser',
            full_name: 'Test User',
            email: '<EMAIL>',
            avatar_url: 'https://example.com/avatar.png',
            language: 'en-US',
            is_admin: false,
            last_login: '2023-01-01T00:00:00Z',
            created: '2022-01-01T00:00:00Z',
            restricted: false,
            active: true,
            prohibit_login: false,
            location: 'Test City',
            website: 'https://example.com',
            description: 'A test user',
            visibility: 'public',
            followers_count: 10,
            following_count: 5,
            starred_repos_count: 20,
            username: 'testuser',
          },
          private: false,
          html_url: 'https://git.example.com/testuser/test-repo',
          clone_url: 'https://git.example.com/testuser/test-repo.git',
          ssh_url: '*******************:testuser/test-repo.git',
          description: 'A test repository',
          website: 'https://example.com',
          stars_count: 5,
          forks_count: 2,
          watchers_count: 3,
          open_issues_count: 1,
          default_branch: 'main',
          created_at: '2022-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          pushed_at: '2023-01-01T12:00:00Z',
        },
        sender: {
          id: 123,
          login: 'testuser',
          full_name: 'Test User',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar.png',
          language: 'en-US',
          is_admin: false,
          last_login: '2023-01-01T00:00:00Z',
          created: '2022-01-01T00:00:00Z',
          restricted: false,
          active: true,
          prohibit_login: false,
          location: 'Test City',
          website: 'https://example.com',
          description: 'A test user',
          visibility: 'public',
          followers_count: 10,
          following_count: 5,
          starred_repos_count: 20,
          username: 'testuser',
        },
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require action as string', async () => {
      const dto = plainToClass(GiteaReleaseWebhookDto, {
        action: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('action');
    });
  });
});
