import { ApiProperty } from '@nestjs/swagger';

export class WebhookProcessingResultDto {
  @ApiProperty({
    description: 'Whether the webhook was successfully processed',
    example: true
  })
  processed: boolean;

  @ApiProperty({
    description: 'The webhook action that was processed',
    example: 'created'
  })
  action: string;

  @ApiProperty({
    description: 'Repository name if applicable',
    example: 'owner/repository-name',
    required: false
  })
  repository?: string;

  @ApiProperty({
    description: 'Processing result message',
    example: 'Repository created and synced successfully'
  })
  message: string;
}
