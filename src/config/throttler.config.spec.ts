import { throttlerConfig } from './throttler.config';

describe('throttlerConfig', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  it('should return default configuration when no environment variables are set', () => {
    // Arrange
    delete process.env.RATE_LIMIT_WINDOW_MS;
    delete process.env.RATE_LIMIT_MAX_REQUESTS;

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 900000, // 15 minutes
      maxRequests: 100,
    });
  });

  it('should use environment variables when provided', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '600000'; // 10 minutes
    process.env.RATE_LIMIT_MAX_REQUESTS = '50';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 600000,
      maxRequests: 50,
    });
  });

  it('should handle partial environment variable configuration', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '1800000'; // 30 minutes
    // Leave RATE_LIMIT_MAX_REQUESTS as default

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 1800000,
      maxRequests: 100,
    });
  });

  it('should handle only RATE_LIMIT_MAX_REQUESTS being set', () => {
    // Arrange
    process.env.RATE_LIMIT_MAX_REQUESTS = '200';
    // Leave RATE_LIMIT_WINDOW_MS as default

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 900000,
      maxRequests: 200,
    });
  });

  it('should handle invalid RATE_LIMIT_WINDOW_MS gracefully', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = 'invalid-number';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config.windowMs).toBe(900000); // Should fall back to default due to NaN
  });

  it('should handle invalid RATE_LIMIT_MAX_REQUESTS gracefully', () => {
    // Arrange
    process.env.RATE_LIMIT_MAX_REQUESTS = 'not-a-number';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config.maxRequests).toBe(100); // Should fall back to default due to NaN
  });

  it('should handle empty string environment variables', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '';
    process.env.RATE_LIMIT_MAX_REQUESTS = '';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 900000,
      maxRequests: 100,
    });
  });

  it('should handle zero values', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '0';
    process.env.RATE_LIMIT_MAX_REQUESTS = '0';

    // Act
    const config = throttlerConfig();

    // Assert
    // Note: parseInt('0') || default evaluates to default because 0 is falsy
    expect(config).toEqual({
      windowMs: 900000,
      maxRequests: 100,
    });
  });

  it('should handle very small window values', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '1000'; // 1 second
    process.env.RATE_LIMIT_MAX_REQUESTS = '1';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 1000,
      maxRequests: 1,
    });
  });

  it('should handle very large window values', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '3600000'; // 1 hour
    process.env.RATE_LIMIT_MAX_REQUESTS = '1000';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 3600000,
      maxRequests: 1000,
    });
  });

  it('should handle production-like strict configuration', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '300000'; // 5 minutes
    process.env.RATE_LIMIT_MAX_REQUESTS = '20';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 300000,
      maxRequests: 20,
    });
  });

  it('should handle development-like lenient configuration', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '60000'; // 1 minute
    process.env.RATE_LIMIT_MAX_REQUESTS = '1000';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 60000,
      maxRequests: 1000,
    });
  });

  it('should handle negative values gracefully', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '-1000';
    process.env.RATE_LIMIT_MAX_REQUESTS = '-50';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: -1000, // parseInt will parse negative numbers
      maxRequests: -50,
    });
  });

  it('should handle floating point numbers by truncating', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '900000.5';
    process.env.RATE_LIMIT_MAX_REQUESTS = '100.9';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 900000, // parseInt truncates decimal
      maxRequests: 100,
    });
  });

  it('should handle hexadecimal numbers', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '0x1388'; // 5000 in hex
    process.env.RATE_LIMIT_MAX_REQUESTS = '0x64'; // 100 in hex

    // Act
    const config = throttlerConfig();

    // Assert
    // Note: parseInt('0x1388', 10) with base 10 returns 0, so falls back to default
    expect(config).toEqual({
      windowMs: 900000, // Falls back to default
      maxRequests: 100, // parseInt('0x64', 10) = 0, falls back to default
    });
  });

  it('should handle numbers with leading/trailing whitespace', () => {
    // Arrange
    process.env.RATE_LIMIT_WINDOW_MS = '  600000  ';
    process.env.RATE_LIMIT_MAX_REQUESTS = '  75  ';

    // Act
    const config = throttlerConfig();

    // Assert
    expect(config).toEqual({
      windowMs: 600000,
      maxRequests: 75,
    });
  });
});
