import { databaseConfig } from './database.config';

describe('databaseConfig', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  it('should return default configuration when no environment variables are set', () => {
    // Arrange
    delete process.env.DATABASE_HOST;
    delete process.env.DATABASE_PORT;
    delete process.env.DATABASE_USER;
    delete process.env.DATABASE_PASSWORD;
    delete process.env.DATABASE_NAME;
    delete process.env.NODE_ENV;
    delete process.env.DEBUG_SQL;

    // Act
    const config = databaseConfig();

    // Assert
    expect(config).toEqual({
      host: 'localhost',
      port: 5432,
      username: 'rsglider',
      password: 'rsglider_dev_password',
      database: 'rsglider',
      synchronize: false,
      logging: false,
      ssl: false,
    });
  });

  it('should use environment variables when provided', () => {
    // Arrange
    process.env.DATABASE_HOST = 'custom-host';
    process.env.DATABASE_PORT = '3306';
    process.env.DATABASE_USER = 'custom-user';
    process.env.DATABASE_PASSWORD = 'custom-password';
    process.env.DATABASE_NAME = 'custom-database';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config).toEqual({
      host: 'custom-host',
      port: 3306,
      username: 'custom-user',
      password: 'custom-password',
      database: 'custom-database',
      synchronize: false,
      logging: false,
      ssl: false,
    });
  });

  it('should enable synchronize when NODE_ENV is development', () => {
    // Arrange
    process.env.NODE_ENV = 'development';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.synchronize).toBe(true);
  });

  it('should disable synchronize when NODE_ENV is production', () => {
    // Arrange
    process.env.NODE_ENV = 'production';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.synchronize).toBe(false);
  });

  it('should disable synchronize when NODE_ENV is test', () => {
    // Arrange
    process.env.NODE_ENV = 'test';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.synchronize).toBe(false);
  });

  it('should enable logging when DEBUG_SQL is true', () => {
    // Arrange
    process.env.DEBUG_SQL = 'true';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.logging).toBe(true);
  });

  it('should disable logging when DEBUG_SQL is false', () => {
    // Arrange
    process.env.DEBUG_SQL = 'false';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.logging).toBe(false);
  });

  it('should disable logging when DEBUG_SQL is not set', () => {
    // Arrange
    delete process.env.DEBUG_SQL;

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.logging).toBe(false);
  });

  it('should enable SSL with rejectUnauthorized false in production', () => {
    // Arrange
    process.env.NODE_ENV = 'production';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.ssl).toEqual({ rejectUnauthorized: false });
  });

  it('should disable SSL in development', () => {
    // Arrange
    process.env.NODE_ENV = 'development';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.ssl).toBe(false);
  });

  it('should disable SSL in test environment', () => {
    // Arrange
    process.env.NODE_ENV = 'test';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.ssl).toBe(false);
  });

  it('should handle invalid DATABASE_PORT gracefully', () => {
    // Arrange
    process.env.DATABASE_PORT = 'invalid-port';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.port).toBe(5432); // Should fall back to default due to NaN
  });

  it('should handle empty DATABASE_PORT', () => {
    // Arrange
    process.env.DATABASE_PORT = '';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.port).toBe(5432); // Should fall back to default
  });

  it('should parse valid DATABASE_PORT correctly', () => {
    // Arrange
    process.env.DATABASE_PORT = '8080';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.port).toBe(8080);
  });

  it('should handle zero DATABASE_PORT', () => {
    // Arrange
    process.env.DATABASE_PORT = '0';

    // Act
    const config = databaseConfig();

    // Assert
    // Note: parseInt('0') || 5432 evaluates to 5432 because 0 is falsy
    expect(config.port).toBe(5432);
  });

  it('should handle production environment with all custom settings', () => {
    // Arrange
    process.env.NODE_ENV = 'production';
    process.env.DATABASE_HOST = 'prod-db.example.com';
    process.env.DATABASE_PORT = '5432';
    process.env.DATABASE_USER = 'prod_user';
    process.env.DATABASE_PASSWORD = 'secure_prod_password';
    process.env.DATABASE_NAME = 'prod_rsglider';
    process.env.DEBUG_SQL = 'false';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config).toEqual({
      host: 'prod-db.example.com',
      port: 5432,
      username: 'prod_user',
      password: 'secure_prod_password',
      database: 'prod_rsglider',
      synchronize: false,
      logging: false,
      ssl: { rejectUnauthorized: false },
    });
  });

  it('should handle development environment with debug enabled', () => {
    // Arrange
    process.env.NODE_ENV = 'development';
    process.env.DEBUG_SQL = 'true';

    // Act
    const config = databaseConfig();

    // Assert
    expect(config.synchronize).toBe(true);
    expect(config.logging).toBe(true);
    expect(config.ssl).toBe(false);
  });
});
