import { jwtConfig } from './jwt.config';

describe('jwtConfig', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  it('should return default configuration when no environment variables are set', () => {
    // Arrange
    delete process.env.JWT_SECRET;
    delete process.env.JWT_EXPIRES_IN;
    delete process.env.REFRESH_TOKEN_EXPIRES_IN;

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'dev_jwt_secret_change_in_production',
      expiresIn: '15m',
      refreshExpiresIn: '7d',
    });
  });

  it('should use environment variables when provided', () => {
    // Arrange
    process.env.JWT_SECRET = 'custom_jwt_secret_for_production';
    process.env.JWT_EXPIRES_IN = '30m';
    process.env.REFRESH_TOKEN_EXPIRES_IN = '14d';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'custom_jwt_secret_for_production',
      expiresIn: '30m',
      refreshExpiresIn: '14d',
    });
  });

  it('should handle partial environment variable configuration', () => {
    // Arrange
    process.env.JWT_SECRET = 'production_secret';
    // Leave JWT_EXPIRES_IN and REFRESH_TOKEN_EXPIRES_IN as defaults

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'production_secret',
      expiresIn: '15m',
      refreshExpiresIn: '7d',
    });
  });

  it('should handle only JWT_EXPIRES_IN being set', () => {
    // Arrange
    delete process.env.JWT_SECRET;
    delete process.env.REFRESH_TOKEN_EXPIRES_IN;
    process.env.JWT_EXPIRES_IN = '1h';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'dev_jwt_secret_change_in_production',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    });
  });

  it('should handle only REFRESH_TOKEN_EXPIRES_IN being set', () => {
    // Arrange
    delete process.env.JWT_SECRET;
    delete process.env.JWT_EXPIRES_IN;
    process.env.REFRESH_TOKEN_EXPIRES_IN = '30d';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'dev_jwt_secret_change_in_production',
      expiresIn: '15m',
      refreshExpiresIn: '30d',
    });
  });

  it('should handle empty string environment variables', () => {
    // Arrange
    process.env.JWT_SECRET = '';
    process.env.JWT_EXPIRES_IN = '';
    process.env.REFRESH_TOKEN_EXPIRES_IN = '';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'dev_jwt_secret_change_in_production',
      expiresIn: '15m',
      refreshExpiresIn: '7d',
    });
  });

  it('should handle various time formats for JWT_EXPIRES_IN', () => {
    // Arrange
    process.env.JWT_EXPIRES_IN = '2h';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config.expiresIn).toBe('2h');
  });

  it('should handle various time formats for REFRESH_TOKEN_EXPIRES_IN', () => {
    // Arrange
    process.env.REFRESH_TOKEN_EXPIRES_IN = '90d';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config.refreshExpiresIn).toBe('90d');
  });

  it('should handle numeric time formats', () => {
    // Arrange
    delete process.env.JWT_SECRET;
    process.env.JWT_EXPIRES_IN = '3600'; // 1 hour in seconds
    process.env.REFRESH_TOKEN_EXPIRES_IN = '604800'; // 1 week in seconds

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'dev_jwt_secret_change_in_production',
      expiresIn: '3600',
      refreshExpiresIn: '604800',
    });
  });

  it('should handle production-like configuration', () => {
    // Arrange
    process.env.JWT_SECRET = 'super_secure_production_jwt_secret_with_256_bits_minimum';
    process.env.JWT_EXPIRES_IN = '5m';
    process.env.REFRESH_TOKEN_EXPIRES_IN = '1d';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'super_secure_production_jwt_secret_with_256_bits_minimum',
      expiresIn: '5m',
      refreshExpiresIn: '1d',
    });
  });

  it('should handle development configuration with longer expiry', () => {
    // Arrange
    process.env.JWT_SECRET = 'dev_secret_for_testing';
    process.env.JWT_EXPIRES_IN = '24h';
    process.env.REFRESH_TOKEN_EXPIRES_IN = '30d';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'dev_secret_for_testing',
      expiresIn: '24h',
      refreshExpiresIn: '30d',
    });
  });

  it('should handle special characters in JWT_SECRET', () => {
    // Arrange
    process.env.JWT_SECRET = 'secret!@#$%^&*()_+-=[]{}|;:,.<>?';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config.secret).toBe('secret!@#$%^&*()_+-=[]{}|;:,.<>?');
  });

  it('should handle very short expiry times', () => {
    // Arrange
    delete process.env.JWT_SECRET;
    process.env.JWT_EXPIRES_IN = '1s';
    process.env.REFRESH_TOKEN_EXPIRES_IN = '1m';

    // Act
    const config = jwtConfig();

    // Assert
    expect(config).toEqual({
      secret: 'dev_jwt_secret_change_in_production',
      expiresIn: '1s',
      refreshExpiresIn: '1m',
    });
  });
});
