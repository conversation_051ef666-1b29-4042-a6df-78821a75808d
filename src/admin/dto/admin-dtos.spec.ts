import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

import { AdminDeveloperView } from './admin-developer-view.dto';
import { AdminUpdateUserRequest } from './admin-update-user-request.dto';
import { AdminUserView } from './admin-user-view.dto';

describe('Admin DTOs', () => {
  describe('AdminDeveloperView', () => {
    it('should be defined', () => {
      expect(AdminDeveloperView).toBeDefined();
    });

    it('should create instance', () => {
      const dto = new AdminDeveloperView();
      expect(dto).toBeInstanceOf(AdminDeveloperView);
    });

    it('should create instance with plainToClass', () => {
      const dto = plainToClass(AdminDeveloperView, {});
      expect(dto).toBeInstanceOf(AdminDeveloperView);
    });
  });

  describe('AdminUserView', () => {
    it('should be defined', () => {
      expect(AdminUserView).toBeDefined();
    });

    it('should create instance', () => {
      const dto = new AdminUserView();
      expect(dto).toBeInstanceOf(AdminUserView);
    });

    it('should create instance with plainToClass', () => {
      const dto = plainToClass(AdminUserView, {});
      expect(dto).toBeInstanceOf(AdminUserView);
    });
  });

  describe('AdminUpdateUserRequest', () => {
    it('should be defined', () => {
      expect(AdminUpdateUserRequest).toBeDefined();
    });

    it('should create instance', () => {
      const dto = new AdminUpdateUserRequest();
      expect(dto).toBeInstanceOf(AdminUpdateUserRequest);
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        status: 'active',
        emailVerified: true,
        twoFactorEnabled: false,
        notes: 'Test user notes',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with partial data', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        firstName: 'John',
        email: '<EMAIL>',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object (all fields optional)', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate firstName as string', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        firstName: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('firstName');
    });

    it('should validate lastName as string', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        lastName: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('lastName');
    });

    it('should validate email format', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        email: 'invalid-email',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('email');
    });

    it('should validate email as string', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        email: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('email');
    });

    it('should validate status as string', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        status: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('status');
    });

    it('should validate emailVerified as boolean', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        emailVerified: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('emailVerified');
    });

    it('should validate twoFactorEnabled as boolean', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        twoFactorEnabled: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('twoFactorEnabled');
    });

    it('should validate notes as string', async () => {
      const dto = plainToClass(AdminUpdateUserRequest, {
        notes: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('notes');
    });

    it('should accept valid email formats', async () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      for (const email of validEmails) {
        const dto = plainToClass(AdminUpdateUserRequest, { email });
        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should reject invalid email formats', async () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        '<EMAIL>',
      ];

      for (const email of invalidEmails) {
        const dto = plainToClass(AdminUpdateUserRequest, { email });
        const errors = await validate(dto);
        expect(errors.length).toBeGreaterThan(0);
      }
    });
  });
});
