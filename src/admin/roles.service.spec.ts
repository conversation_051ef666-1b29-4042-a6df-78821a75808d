import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { RolesService } from './roles.service';

describe('RolesService Unit Tests - Coverage Lines 41, 87-92, 105-109', () => {
    async function createTestService(mockDb: any): Promise<RolesService> {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                RolesService,
                {
                    provide: 'DB',
                    useValue: mockDb,
                },
            ],
        }).compile();

        return module.get<RolesService>(RolesService);
    }

    describe('createRole - Line 41 Coverage', () => {
        it('should throw BadRequestException when role name already exists - COVERS LINE 41', async () => {
            const mockDb = {
                select: jest.fn(),
                insert: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
            };

            // Mock the database chain for existing role check
            const queryBuilder = {
                from: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue([{ id: 'existing-role', name: 'admin' }]),
            };
            mockDb.select.mockReturnValue(queryBuilder);

            const service = await createTestService(mockDb);
            const createRequest = {
                name: 'admin',
                description: 'Admin role',
            };

            await expect(service.createRole(createRequest))
                .rejects.toThrow(BadRequestException);

            await expect(service.createRole(createRequest))
                .rejects.toThrow('Role name already exists');
        });
    });

    describe('assignPermissionToRole - Lines 87, 90, 92 Coverage', () => {
        it('should throw NotFoundException when role does not exist - COVERS LINE 87', async () => {
            const mockDb = {
                select: jest.fn(),
                insert: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
            };

            // Mock role not found
            const queryBuilder = {
                from: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue([]), // Empty array = role not found
            };
            mockDb.select.mockReturnValue(queryBuilder);

            const service = await createTestService(mockDb);

            await expect(service.assignPermissionToRole('non-existent-role', 'permission-123'))
                .rejects.toThrow(NotFoundException);

            await expect(service.assignPermissionToRole('non-existent-role', 'permission-123'))
                .rejects.toThrow('Role not found');
        });

        it('should throw NotFoundException when permission does not exist - COVERS LINE 90', async () => {
            const mockDb = {
                select: jest.fn(),
                insert: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
            };

            // We need to mock two sequential database calls:
            // 1. Check if role exists (should return role)
            // 2. Check if permission exists (should return empty)

            let callCount = 0;
            mockDb.select.mockImplementation(() => {
                callCount++;
                const queryBuilder = {
                    from: jest.fn().mockReturnThis(),
                    where: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockImplementation(() => {
                        if (callCount === 1) {
                            // First call: role exists
                            return Promise.resolve([{ id: 'role-123', name: 'test-role' }]);
                        } else {
                            // Second call: permission does not exist
                            return Promise.resolve([]);
                        }
                    }),
                };
                return queryBuilder;
            });

            const service = await createTestService(mockDb);

            await expect(service.assignPermissionToRole('role-123', 'non-existent-permission'))
                .rejects.toThrow(NotFoundException);

            // Reset callCount for second test
            callCount = 0;
            await expect(service.assignPermissionToRole('role-123', 'non-existent-permission'))
                .rejects.toThrow('Permission not found');
        });

        it('should throw BadRequestException when permission already assigned - COVERS LINE 92', async () => {
            const mockDb = {
                select: jest.fn(),
                insert: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
            };

            // We need to mock three sequential database calls:
            // 1. Check if role exists (should return role)
            // 2. Check if permission exists (should return permission)
            // 3. Check if assignment exists (should return existing assignment)

            let callCount = 0;
            mockDb.select.mockImplementation(() => {
                callCount++;
                const queryBuilder = {
                    from: jest.fn().mockReturnThis(),
                    where: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockImplementation(() => {
                        if (callCount === 1) {
                            // First call: role exists
                            return Promise.resolve([{ id: 'role-123', name: 'test-role' }]);
                        } else if (callCount === 2) {
                            // Second call: permission exists
                            return Promise.resolve([{ id: 'permission-123', name: 'test-permission' }]);
                        } else {
                            // Third call: assignment already exists
                            return Promise.resolve([{ roleId: 'role-123', permissionId: 'permission-123' }]);
                        }
                    }),
                };
                return queryBuilder;
            });

            const service = await createTestService(mockDb);

            await expect(service.assignPermissionToRole('role-123', 'permission-123'))
                .rejects.toThrow(BadRequestException);

            // Reset callCount for second test
            callCount = 0;
            await expect(service.assignPermissionToRole('role-123', 'permission-123'))
                .rejects.toThrow('Permission already assigned to role');
        });
    });

    describe('removePermissionFromRole - Lines 105, 108, 109 Coverage', () => {
        it('should throw NotFoundException when role does not exist - COVERS LINE 105', async () => {
            const mockDb = {
                select: jest.fn(),
                insert: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
            };

            // Mock role not found
            const queryBuilder = {
                from: jest.fn().mockReturnThis(),
                where: jest.fn().mockReturnThis(),
                limit: jest.fn().mockResolvedValue([]), // Empty array = role not found
            };
            mockDb.select.mockReturnValue(queryBuilder);

            const service = await createTestService(mockDb);

            await expect(service.removePermissionFromRole('non-existent-role', 'permission-123'))
                .rejects.toThrow(NotFoundException);

            await expect(service.removePermissionFromRole('non-existent-role', 'permission-123'))
                .rejects.toThrow('Role not found');
        });

        it('should throw NotFoundException when permission does not exist - COVERS LINE 108', async () => {
            const mockDb = {
                select: jest.fn(),
                insert: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
            };

            // We need to mock two sequential database calls:
            // 1. Check if role exists (should return role)
            // 2. Check if permission exists (should return empty)

            let callCount = 0;
            mockDb.select.mockImplementation(() => {
                callCount++;
                const queryBuilder = {
                    from: jest.fn().mockReturnThis(),
                    where: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockImplementation(() => {
                        if (callCount === 1) {
                            // First call: role exists
                            return Promise.resolve([{ id: 'role-123', name: 'test-role' }]);
                        } else {
                            // Second call: permission does not exist
                            return Promise.resolve([]);
                        }
                    }),
                };
                return queryBuilder;
            });

            const service = await createTestService(mockDb);

            await expect(service.removePermissionFromRole('role-123', 'non-existent-permission'))
                .rejects.toThrow(NotFoundException);

            // Reset callCount for second test
            callCount = 0;
            await expect(service.removePermissionFromRole('role-123', 'non-existent-permission'))
                .rejects.toThrow('Permission not found');
        });

        it('should throw NotFoundException when permission not assigned to role - COVERS LINE 109', async () => {
            const mockDb = {
                select: jest.fn(),
                insert: jest.fn(),
                update: jest.fn(),
                delete: jest.fn(),
            };

            // We need to mock two select calls and one delete call:
            // 1. Check if role exists (should return role)
            // 2. Check if permission exists (should return permission)
            // 3. Delete assignment (should return empty - no assignment found)

            let selectCallCount = 0;
            mockDb.select.mockImplementation(() => {
                selectCallCount++;
                const queryBuilder = {
                    from: jest.fn().mockReturnThis(),
                    where: jest.fn().mockReturnThis(),
                    limit: jest.fn().mockImplementation(() => {
                        if (selectCallCount === 1) {
                            // First call: role exists
                            return Promise.resolve([{ id: 'role-123', name: 'test-role' }]);
                        } else {
                            // Second call: permission exists
                            return Promise.resolve([{ id: 'permission-123', name: 'test-permission' }]);
                        }
                    }),
                };
                return queryBuilder;
            });

            // Mock delete operation returning empty array (no assignment deleted)
            const mockDeleteQuery = {
                where: jest.fn().mockReturnThis(),
                returning: jest.fn().mockResolvedValue([]), // Empty array = no assignment found
            };
            mockDb.delete.mockReturnValue(mockDeleteQuery);

            const service = await createTestService(mockDb);

            await expect(service.removePermissionFromRole('role-123', 'permission-123'))
                .rejects.toThrow(NotFoundException);

            // Reset selectCallCount for second test
            selectCallCount = 0;
            await expect(service.removePermissionFromRole('role-123', 'permission-123'))
                .rejects.toThrow('Permission not assigned to role');
        });
    });
}); 