import { Test, TestingModule } from '@nestjs/testing';
import {
    CreateEmailCampaignDto,
    CreateEmailTemplateDto,
    EmailCampaignQueryDto,
    EmailLogQueryDto,
    EmailTemplateQueryDto,
    SendEmailDto,
    UpdateEmailCampaignDto,
    UpdateEmailTemplateDto
} from '../email/dto/index';
import { EmailManagementService } from '../email/email-management.service';
import { UsersService } from '../users/users.service';
import { AdminController } from './admin.controller';
import { PermissionsService } from './permissions.service';
import { RolesService } from './roles.service';

describe('AdminController', () => {
  let controller: AdminController;
  let usersService: jest.Mocked<UsersService>;
  let rolesService: jest.Mocked<RolesService>;
  let permissionsService: jest.Mocked<PermissionsService>;
  let emailManagementService: jest.Mocked<EmailManagementService>;

  beforeEach(async () => {
    const mockUsersService = {
      getUserSessions: jest.fn(),
      removeUserSession: jest.fn(),
      adminListUsers: jest.fn(),
      adminCreateUser: jest.fn(),
      adminGetUser: jest.fn(),
      adminUpdateUser: jest.fn(),
      adminDeleteUser: jest.fn(),
      adminListUserRoles: jest.fn(),
      adminAssignRoleToUser: jest.fn(),
      adminRemoveRoleFromUser: jest.fn(),
    };

    const mockRolesService = {
      listRoles: jest.fn(),
      createRole: jest.fn(),
      getRole: jest.fn(),
      updateRole: jest.fn(),
      deleteRole: jest.fn(),
      assignPermissionToRole: jest.fn(),
      removePermissionFromRole: jest.fn(),
    };

    const mockPermissionsService = {
      listPermissions: jest.fn(),
      createPermission: jest.fn(),
      getPermission: jest.fn(),
      updatePermission: jest.fn(),
      deletePermission: jest.fn(),
    };

    const mockEmailManagementService = {
      createTemplate: jest.fn(),
      getTemplates: jest.fn(),
      getTemplate: jest.fn(),
      updateTemplate: jest.fn(),
      deleteTemplate: jest.fn(),
      createCampaign: jest.fn(),
      getCampaigns: jest.fn(),
      getCampaign: jest.fn(),
      updateCampaign: jest.fn(),
      executeCampaign: jest.fn(),
      sendEmail: jest.fn(),
      getEmailLogs: jest.fn(),
      getAnalyticsSummary: jest.fn(),
      getTemplateStats: jest.fn(),
      getCampaignStats: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: RolesService,
          useValue: mockRolesService,
        },
        {
          provide: PermissionsService,
          useValue: mockPermissionsService,
        },
        {
          provide: EmailManagementService,
          useValue: mockEmailManagementService,
        },
      ],
    }).compile();

    controller = module.get<AdminController>(AdminController);
    usersService = module.get(UsersService);
    rolesService = module.get(RolesService);
    permissionsService = module.get(PermissionsService);
    emailManagementService = module.get(EmailManagementService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('User Management', () => {
    describe('getUserSessions', () => {
      it('should get user sessions', async () => {
        const userId = 'user-123';
        const mockSessions = [{
          id: 'session-1',
          userId,
          platform: 'web',
          expiresAt: new Date().toISOString(),
          isActive: true,
          isCurrent: false,
          requiresVerification: false,
          ipAddress: '127.0.0.1',
          userAgent: 'test-agent',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }];
        usersService.getUserSessions.mockResolvedValue(mockSessions);

        const result = await controller.getUserSessions(userId);

        expect(usersService.getUserSessions).toHaveBeenCalledWith(userId);
        expect(result).toEqual(mockSessions);
      });
    });

    describe('revokeUserSession', () => {
      it('should revoke user session', async () => {
        const userId = 'user-123';
        const sessionId = 'session-123';
        const mockResponse = { message: 'Session revoked' };
        usersService.removeUserSession.mockResolvedValue(mockResponse);

        const result = await controller.revokeUserSession(userId, sessionId);

        expect(usersService.removeUserSession).toHaveBeenCalledWith(userId, sessionId);
        expect(result).toEqual(mockResponse);
      });
    });

    describe('listUsers', () => {
      it('should list users with default parameters', async () => {
        const mockUsers = {
          users: [{
            id: 'user-1',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            roles: ['user'],
            twoFactorEnabled: false,
            emailVerified: true,
            status: 'active',
            lastLoginAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }],
          pagination: { page: 1, limit: 20, total: 1, totalPages: 1 }
        };
        usersService.adminListUsers.mockResolvedValue(mockUsers);

        const result = await controller.listUsers();

        expect(usersService.adminListUsers).toHaveBeenCalledWith({
          page: 1,
          limit: 20,
          search: '',
          status: '',
          role: '',
        });
        expect(result).toEqual(mockUsers);
      });

      it('should list users with custom parameters', async () => {
        const mockUsers = {
          users: [{
            id: 'user-1',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            roles: ['admin'],
            twoFactorEnabled: false,
            emailVerified: true,
            status: 'active',
            lastLoginAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }],
          pagination: { page: 2, limit: 10, total: 1, totalPages: 1 }
        };
        usersService.adminListUsers.mockResolvedValue(mockUsers);

        const result = await controller.listUsers(2, 10, 'test', 'active', 'admin');

        expect(usersService.adminListUsers).toHaveBeenCalledWith({
          page: 2,
          limit: 10,
          search: 'test',
          status: 'active',
          role: 'admin',
        });
        expect(result).toEqual(mockUsers);
      });
    });

    describe('createUser', () => {
      it('should create a new user', async () => {
        const userData = { email: '<EMAIL>', name: 'New User' };
        const mockUser = {
          id: 'user-123',
          email: '<EMAIL>',
          firstName: 'New',
          lastName: 'User',
          roles: ['user'],
          twoFactorEnabled: false,
          emailVerified: false,
          status: 'active',
          lastLoginAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        usersService.adminCreateUser.mockResolvedValue(mockUser);

        const result = await controller.createUser(userData);

        expect(usersService.adminCreateUser).toHaveBeenCalledWith(userData);
        expect(result).toEqual(mockUser);
      });
    });

    describe('getUser', () => {
      it('should get user details', async () => {
        const userId = 'user-123';
        const mockUser = {
          id: userId,
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          roles: ['user'],
          twoFactorEnabled: false,
          emailVerified: true,
          status: 'active',
          lastLoginAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        usersService.adminGetUser.mockResolvedValue(mockUser);

        const result = await controller.getUser(userId);

        expect(usersService.adminGetUser).toHaveBeenCalledWith(userId);
        expect(result).toEqual(mockUser);
      });
    });

    describe('updateUser', () => {
      it('should update user', async () => {
        const userId = 'user-123';
        const updateData = { name: 'Updated Name' };
        const mockUser = {
          id: userId,
          email: '<EMAIL>',
          firstName: 'Updated',
          lastName: 'Name',
          roles: ['user'],
          twoFactorEnabled: false,
          emailVerified: true,
          status: 'active',
          lastLoginAt: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        usersService.adminUpdateUser.mockResolvedValue(mockUser);

        const result = await controller.updateUser(userId, updateData);

        expect(usersService.adminUpdateUser).toHaveBeenCalledWith(userId, updateData);
        expect(result).toEqual(mockUser);
      });
    });

    describe('deleteUser', () => {
      it('should delete user', async () => {
        const userId = 'user-123';
        usersService.adminDeleteUser.mockResolvedValue(undefined);

        await controller.deleteUser(userId);

        expect(usersService.adminDeleteUser).toHaveBeenCalledWith(userId);
      });
    });
  });

  describe('Role Management', () => {
    describe('listRoles', () => {
      it('should list all roles', async () => {
        const mockRoles = [{ id: 'role-1', name: 'admin' }];
        rolesService.listRoles.mockResolvedValue(mockRoles);

        const result = await controller.listRoles();

        expect(rolesService.listRoles).toHaveBeenCalled();
        expect(result).toEqual(mockRoles);
      });
    });

    describe('createRole', () => {
      it('should create a new role', async () => {
        const roleData = { name: 'moderator', description: 'Moderator role' };
        const mockRole = { id: 'role-123', ...roleData };
        rolesService.createRole.mockResolvedValue(mockRole);

        const result = await controller.createRole(roleData);

        expect(rolesService.createRole).toHaveBeenCalledWith(roleData);
        expect(result).toEqual(mockRole);
      });
    });

    describe('getRole', () => {
      it('should get role details', async () => {
        const roleId = 'role-123';
        const mockRole = { id: roleId, name: 'admin' };
        rolesService.getRole.mockResolvedValue(mockRole);

        const result = await controller.getRole(roleId);

        expect(rolesService.getRole).toHaveBeenCalledWith(roleId);
        expect(result).toEqual(mockRole);
      });
    });

    describe('updateRole', () => {
      it('should update role', async () => {
        const roleId = 'role-123';
        const updateData = { name: 'updated-role' };
        const mockRole = { id: roleId, ...updateData };
        rolesService.updateRole.mockResolvedValue(mockRole);

        const result = await controller.updateRole(roleId, updateData);

        expect(rolesService.updateRole).toHaveBeenCalledWith(roleId, updateData);
        expect(result).toEqual(mockRole);
      });
    });

    describe('deleteRole', () => {
      it('should delete role', async () => {
        const roleId = 'role-123';
        rolesService.deleteRole.mockResolvedValue(undefined);

        await controller.deleteRole(roleId);

        expect(rolesService.deleteRole).toHaveBeenCalledWith(roleId);
      });
    });

    describe('getUserRoles', () => {
      it('should get user roles', async () => {
        const userId = 'user-123';
        const mockRoles = [{
          id: 'role-1',
          name: 'user',
          description: 'Standard user role',
          isSystemRole: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }];
        usersService.adminListUserRoles.mockResolvedValue(mockRoles);

        const result = await controller.getUserRoles(userId);

        expect(usersService.adminListUserRoles).toHaveBeenCalledWith(userId);
        expect(result).toEqual(mockRoles);
      });
    });

    describe('assignRoleToUser', () => {
      it('should assign role to user', async () => {
        const userId = 'user-123';
        const body = { roleId: 'role-123' };
        const mockResponse = { message: 'Role assigned' };
        usersService.adminAssignRoleToUser.mockResolvedValue(mockResponse);

        const result = await controller.assignRoleToUser(userId, body);

        expect(usersService.adminAssignRoleToUser).toHaveBeenCalledWith(userId, body.roleId);
        expect(result).toEqual(mockResponse);
      });
    });

    describe('removeRoleFromUser', () => {
      it('should remove role from user', async () => {
        const userId = 'user-123';
        const roleId = 'role-123';
        usersService.adminRemoveRoleFromUser.mockResolvedValue(undefined);

        await controller.removeRoleFromUser(userId, roleId);

        expect(usersService.adminRemoveRoleFromUser).toHaveBeenCalledWith(userId, roleId);
      });
    });
  });

  describe('Permissions Management', () => {
    describe('listPermissions', () => {
      it('should list all permissions', async () => {
        const mockPermissions = [{
          id: 'perm-1',
          name: 'read',
          resource: 'users',
          action: 'read',
          isSystemPermission: true
        }];
        permissionsService.listPermissions.mockResolvedValue(mockPermissions);

        const result = await controller.listPermissions();

        expect(permissionsService.listPermissions).toHaveBeenCalled();
        expect(result).toEqual(mockPermissions);
      });
    });

    describe('createPermission', () => {
      it('should create a new permission', async () => {
        const permissionData = { name: 'write', description: 'Write permission' };
        const mockPermission = {
          id: 'perm-123',
          name: 'write',
          resource: 'users',
          action: 'write',
          isSystemPermission: false
        };
        permissionsService.createPermission.mockResolvedValue(mockPermission);

        const result = await controller.createPermission(permissionData);

        expect(permissionsService.createPermission).toHaveBeenCalledWith(permissionData);
        expect(result).toEqual(mockPermission);
      });
    });

    describe('getPermission', () => {
      it('should get permission details', async () => {
        const permissionId = 'perm-123';
        const mockPermission = {
          id: permissionId,
          name: 'read',
          resource: 'users',
          action: 'read',
          isSystemPermission: true
        };
        permissionsService.getPermission.mockResolvedValue(mockPermission);

        const result = await controller.getPermission(permissionId);

        expect(permissionsService.getPermission).toHaveBeenCalledWith(permissionId);
        expect(result).toEqual(mockPermission);
      });
    });

    describe('updatePermission', () => {
      it('should update permission', async () => {
        const permissionId = 'perm-123';
        const updateData = { name: 'updated-permission' };
        const mockPermission = {
          id: permissionId,
          name: 'updated-permission',
          resource: 'users',
          action: 'read',
          isSystemPermission: false
        };
        permissionsService.updatePermission.mockResolvedValue(mockPermission);

        const result = await controller.updatePermission(permissionId, updateData);

        expect(permissionsService.updatePermission).toHaveBeenCalledWith(permissionId, updateData);
        expect(result).toEqual(mockPermission);
      });
    });

    describe('deletePermission', () => {
      it('should delete permission', async () => {
        const permissionId = 'perm-123';
        permissionsService.deletePermission.mockResolvedValue(undefined);

        await controller.deletePermission(permissionId);

        expect(permissionsService.deletePermission).toHaveBeenCalledWith(permissionId);
      });
    });

    describe('assignPermissionToRole', () => {
      it('should assign permission to role', async () => {
        const roleId = 'role-123';
        const body = { permissionId: 'perm-123' };
        const mockResponse = { message: 'Permission assigned' };
        rolesService.assignPermissionToRole.mockResolvedValue(mockResponse);

        const result = await controller.assignPermissionToRole(roleId, body);

        expect(rolesService.assignPermissionToRole).toHaveBeenCalledWith(roleId, body.permissionId);
        expect(result).toEqual(mockResponse);
      });
    });

    describe('removePermissionFromRole', () => {
      it('should remove permission from role', async () => {
        const roleId = 'role-123';
        const permissionId = 'perm-123';
        rolesService.removePermissionFromRole.mockResolvedValue(undefined);

        await controller.removePermissionFromRole(roleId, permissionId);

        expect(rolesService.removePermissionFromRole).toHaveBeenCalledWith(roleId, permissionId);
      });
    });
  });

  describe('Email Management', () => {
    describe('createEmailTemplate', () => {
      it('should create a new email template', async () => {
        const createDto: CreateEmailTemplateDto = {
          name: 'Welcome Template',
          subject: 'Welcome!',
          htmlContent: '<h1>Welcome</h1>',
          textContent: 'Welcome',
        };
        const mockTemplate = {
          id: 'template-123',
          name: 'Welcome Template',
          subject: 'Welcome!',
          htmlContent: '<h1>Welcome</h1>',
          textContent: 'Welcome',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        emailManagementService.createTemplate.mockResolvedValue(mockTemplate);

        const result = await controller.createEmailTemplate(createDto);

        expect(emailManagementService.createTemplate).toHaveBeenCalledWith(createDto);
        expect(result).toEqual(mockTemplate);
      });
    });

    describe('getEmailTemplates', () => {
      it('should get all email templates', async () => {
        const query: EmailTemplateQueryDto = { page: 1, limit: 10 };
        const mockTemplates = {
          data: [{
            id: 'template-1',
            name: 'Template 1',
            subject: 'Test Subject',
            htmlContent: '<p>Test</p>',
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }],
          pagination: { page: 1, limit: 10, total: 1, totalPages: 1 }
        };
        emailManagementService.getTemplates.mockResolvedValue(mockTemplates);

        const result = await controller.getEmailTemplates(query);

        expect(emailManagementService.getTemplates).toHaveBeenCalledWith(query);
        expect(result).toEqual(mockTemplates);
      });
    });

    describe('getEmailTemplate', () => {
      it('should get a specific email template', async () => {
        const templateId = 'template-123';
        const mockTemplate = {
          id: templateId,
          name: 'Test Template',
          subject: 'Test Subject',
          htmlContent: '<p>Test</p>',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        emailManagementService.getTemplate.mockResolvedValue(mockTemplate);

        const result = await controller.getEmailTemplate(templateId);

        expect(emailManagementService.getTemplate).toHaveBeenCalledWith(templateId);
        expect(result).toEqual(mockTemplate);
      });
    });

    describe('updateEmailTemplate', () => {
      it('should update an email template', async () => {
        const templateId = 'template-123';
        const updateDto: UpdateEmailTemplateDto = { name: 'Updated Template' };
        const mockTemplate = {
          id: templateId,
          name: 'Updated Template',
          subject: 'Test Subject',
          htmlContent: '<p>Test</p>',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        emailManagementService.updateTemplate.mockResolvedValue(mockTemplate);

        const result = await controller.updateEmailTemplate(templateId, updateDto);

        expect(emailManagementService.updateTemplate).toHaveBeenCalledWith(templateId, updateDto);
        expect(result).toEqual(mockTemplate);
      });
    });

    describe('deleteEmailTemplate', () => {
      it('should delete an email template', async () => {
        const templateId = 'template-123';
        emailManagementService.deleteTemplate.mockResolvedValue(undefined);

        await controller.deleteEmailTemplate(templateId);

        expect(emailManagementService.deleteTemplate).toHaveBeenCalledWith(templateId);
      });
    });

    describe('createEmailCampaign', () => {
      it('should create a new email campaign', async () => {
        const createDto: CreateEmailCampaignDto = {
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'users',
        };
        const mockCampaign = {
          id: 'campaign-123',
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'users',
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        emailManagementService.createCampaign.mockResolvedValue(mockCampaign);

        const result = await controller.createEmailCampaign(createDto);

        expect(emailManagementService.createCampaign).toHaveBeenCalledWith(createDto);
        expect(result).toEqual(mockCampaign);
      });
    });

    describe('getEmailCampaigns', () => {
      it('should get all email campaigns', async () => {
        const query: EmailCampaignQueryDto = { page: 1, limit: 10 };
        const mockCampaigns = {
          data: [{
            id: 'campaign-1',
            name: 'Campaign 1',
            templateId: 'template-123',
            targetAudience: 'users',
            status: 'sent',
            totalRecipients: 100,
            sentCount: 95,
            failedCount: 5,
            createdAt: new Date(),
            updatedAt: new Date()
          }],
          pagination: { page: 1, limit: 10, total: 1, totalPages: 1 }
        };
        emailManagementService.getCampaigns.mockResolvedValue(mockCampaigns);

        const result = await controller.getEmailCampaigns(query);

        expect(emailManagementService.getCampaigns).toHaveBeenCalledWith(query);
        expect(result).toEqual(mockCampaigns);
      });
    });

    describe('getEmailCampaign', () => {
      it('should get a specific email campaign', async () => {
        const campaignId = 'campaign-123';
        const mockCampaign = {
          id: campaignId,
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'users',
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        emailManagementService.getCampaign.mockResolvedValue(mockCampaign);

        const result = await controller.getEmailCampaign(campaignId);

        expect(emailManagementService.getCampaign).toHaveBeenCalledWith(campaignId);
        expect(result).toEqual(mockCampaign);
      });
    });

    describe('updateEmailCampaign', () => {
      it('should update an email campaign', async () => {
        const campaignId = 'campaign-123';
        const updateDto: UpdateEmailCampaignDto = { name: 'Updated Campaign' };
        const mockCampaign = {
          id: campaignId,
          name: 'Updated Campaign',
          templateId: 'template-123',
          targetAudience: 'users',
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        emailManagementService.updateCampaign.mockResolvedValue(mockCampaign);

        const result = await controller.updateEmailCampaign(campaignId, updateDto);

        expect(emailManagementService.updateCampaign).toHaveBeenCalledWith(campaignId, updateDto);
        expect(result).toEqual(mockCampaign);
      });
    });

    describe('executeEmailCampaign', () => {
      it('should execute an email campaign', async () => {
        const campaignId = 'campaign-123';
        emailManagementService.executeCampaign.mockResolvedValue(undefined);

        const result = await controller.executeEmailCampaign(campaignId);

        expect(emailManagementService.executeCampaign).toHaveBeenCalledWith(campaignId);
        expect(result).toEqual({ message: 'Campaign execution started successfully' });
      });
    });

    describe('sendEmail', () => {
      it('should send email directly', async () => {
        const sendDto: SendEmailDto = {
          recipients: ['<EMAIL>'],
          subject: 'Test Email',
          htmlContent: '<p>Test</p>',
        };
        const mockResponse = { success: true, logId: 'log-123' };
        emailManagementService.sendEmail.mockResolvedValue(mockResponse);

        const result = await controller.sendEmail(sendDto);

        expect(emailManagementService.sendEmail).toHaveBeenCalledWith(sendDto);
        expect(result).toEqual(mockResponse);
      });
    });

    describe('getEmailLogs', () => {
      it('should get email logs', async () => {
        const query: EmailLogQueryDto = { page: 1, limit: 10 };
        const mockLogs = {
          data: [{
            id: 'log-1',
            recipientEmail: '<EMAIL>',
            subject: 'Test Email',
            status: 'sent',
            sentAt: new Date(),
            templateId: 'template-123',
            campaignId: 'campaign-123',
            createdAt: new Date()
          }],
          pagination: { page: 1, limit: 10, total: 1, totalPages: 1 }
        };
        emailManagementService.getEmailLogs.mockResolvedValue(mockLogs);

        const result = await controller.getEmailLogs(query);

        expect(emailManagementService.getEmailLogs).toHaveBeenCalledWith(query);
        expect(result).toEqual(mockLogs);
      });
    });

    describe('getEmailAnalyticsSummary', () => {
      it('should get email analytics summary', async () => {
        const mockSummary = {
          totalEmailsSent: 100,
          totalEmailsFailed: 5,
          totalTemplates: 10,
          totalCampaigns: 5,
          recentActivity: [{
            type: 'email_sent',
            description: 'Email sent successfully',
            timestamp: new Date()
          }]
        };
        emailManagementService.getAnalyticsSummary.mockResolvedValue(mockSummary);

        const result = await controller.getEmailAnalyticsSummary();

        expect(emailManagementService.getAnalyticsSummary).toHaveBeenCalled();
        expect(result).toEqual(mockSummary);
      });
    });

    describe('getEmailTemplateStats', () => {
      it('should get statistics for a specific template', async () => {
        const templateId = 'template-123';
        const mockStats = {
          templateId: 'template-123',
          totalSent: 50,
          totalFailed: 5,
          successRate: 90,
          lastUsed: new Date(),
          template: {
            id: 'template-123',
            name: 'Test Template',
            subject: 'Test Subject',
            htmlContent: '<p>Test</p>',
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        };
        emailManagementService.getTemplateStats.mockResolvedValue(mockStats);

        const result = await controller.getEmailTemplateStats(templateId);

        expect(emailManagementService.getTemplateStats).toHaveBeenCalledWith(templateId);
        expect(result).toEqual(mockStats);
      });
    });

    describe('getEmailCampaignStats', () => {
      it('should get statistics for a specific campaign', async () => {
        const campaignId = 'campaign-123';
        const mockStats = {
          campaignId: 'campaign-123',
          totalRecipients: 100,
          totalSent: 95,
          totalFailed: 5,
          successRate: 95,
          executionTime: 300,
          campaign: {
            id: 'campaign-123',
            name: 'Test Campaign',
            templateId: 'template-123',
            targetAudience: 'users',
            status: 'sent',
            totalRecipients: 100,
            sentCount: 95,
            failedCount: 5,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        };
        emailManagementService.getCampaignStats.mockResolvedValue(mockStats);

        const result = await controller.getEmailCampaignStats(campaignId);

        expect(emailManagementService.getCampaignStats).toHaveBeenCalledWith(campaignId);
        expect(result).toEqual(mockStats);
      });
    });
  });
});
