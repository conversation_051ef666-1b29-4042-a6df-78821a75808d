import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { PermissionsService } from './permissions.service';

describe('PermissionsService', () => {
  let service: PermissionsService;
  let mockDb: any;

  beforeEach(async () => {
    // Mock database with chainable methods
    mockDb = {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      returning: jest.fn().mockResolvedValue([]),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PermissionsService,
        {
          provide: 'DB',
          useValue: mockDb,
        },
      ],
    }).compile();

    service = module.get<PermissionsService>(PermissionsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('listPermissions', () => {
    it('should return all permissions', async () => {
      const mockPermissions = [
        {
          id: 'perm-1',
          name: 'read_users',
          resource: 'users',
          action: 'read',
          description: 'Read users permission',
          isSystemPermission: true,
          createdAt: new Date('2023-01-01'),
        },
        {
          id: 'perm-2',
          name: 'write_users',
          resource: 'users',
          action: 'write',
          description: 'Write users permission',
          isSystemPermission: false,
          createdAt: new Date('2023-01-02'),
        },
      ];

      // Mock the database query chain
      mockDb.select.mockReturnThis();
      mockDb.from.mockResolvedValue(mockPermissions);

      const result = await service.listPermissions();

      expect(mockDb.select).toHaveBeenCalled();
      expect(mockDb.from).toHaveBeenCalled();
      expect(result).toEqual([
        {
          id: 'perm-1',
          name: 'read_users',
          resource: 'users',
          action: 'read',
          description: 'Read users permission',
          isSystemPermission: true,
          createdAt: '2023-01-01T00:00:00.000Z',
        },
        {
          id: 'perm-2',
          name: 'write_users',
          resource: 'users',
          action: 'write',
          description: 'Write users permission',
          isSystemPermission: false,
          createdAt: '2023-01-02T00:00:00.000Z',
        },
      ]);
    });

    it('should handle permissions with null createdAt', async () => {
      const mockPermissions = [
        {
          id: 'perm-1',
          name: 'read_users',
          resource: 'users',
          action: 'read',
          description: 'Read users permission',
          isSystemPermission: true,
          createdAt: null,
        },
      ];

      mockDb.select.mockReturnThis();
      mockDb.from.mockResolvedValue(mockPermissions);

      const result = await service.listPermissions();

      expect(result[0].createdAt).toBeUndefined();
    });
  });

  describe('getPermission', () => {
    it('should return a permission when found', async () => {
      const mockPermission = {
        id: 'perm-1',
        name: 'read_users',
        resource: 'users',
        action: 'read',
        description: 'Read users permission',
        isSystemPermission: true,
        createdAt: new Date('2023-01-01'),
      };

      mockDb.select.mockReturnThis();
      mockDb.from.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.limit.mockResolvedValue([mockPermission]);

      const result = await service.getPermission('perm-1');

      expect(mockDb.select).toHaveBeenCalled();
      expect(mockDb.from).toHaveBeenCalled();
      expect(mockDb.where).toHaveBeenCalled();
      expect(mockDb.limit).toHaveBeenCalledWith(1);
      expect(result).toEqual({
        id: 'perm-1',
        name: 'read_users',
        resource: 'users',
        action: 'read',
        description: 'Read users permission',
        isSystemPermission: true,
        createdAt: '2023-01-01T00:00:00.000Z',
      });
    });

    it('should throw NotFoundException when permission not found', async () => {
      mockDb.select.mockReturnThis();
      mockDb.from.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.limit.mockResolvedValue([]); // Empty array = not found

      await expect(service.getPermission('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.getPermission('nonexistent')).rejects.toThrow('Permission not found');
    });

    it('should handle permission with null createdAt', async () => {
      const mockPermission = {
        id: 'perm-1',
        name: 'read_users',
        resource: 'users',
        action: 'read',
        description: 'Read users permission',
        isSystemPermission: true,
        createdAt: null,
      };

      mockDb.select.mockReturnThis();
      mockDb.from.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.limit.mockResolvedValue([mockPermission]);

      const result = await service.getPermission('perm-1');

      expect(result.createdAt).toBeUndefined();
    });
  });

  describe('createPermission', () => {
    const createData = {
      name: 'new_permission',
      resource: 'posts',
      action: 'create',
      description: 'Create posts permission',
    };

    it('should create a new permission successfully', async () => {
      const mockCreatedPermission = {
        id: 'perm-new',
        name: 'new_permission',
        resource: 'posts',
        action: 'create',
        description: 'Create posts permission',
        isSystemPermission: false,
        createdAt: new Date('2023-01-01'),
      };

      // Mock name uniqueness check (no existing permission)
      mockDb.select.mockReturnThis();
      mockDb.from.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.limit.mockResolvedValueOnce([]); // No existing permission

      // Mock insert operation
      mockDb.insert.mockReturnThis();
      mockDb.values.mockReturnThis();
      mockDb.returning.mockResolvedValue([mockCreatedPermission]);

      const result = await service.createPermission(createData);

      expect(mockDb.select).toHaveBeenCalled();
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalledWith({
        name: 'new_permission',
        resource: 'posts',
        action: 'create',
        description: 'Create posts permission',
        isSystemPermission: false,
      });
      expect(result).toEqual({
        id: 'perm-new',
        name: 'new_permission',
        resource: 'posts',
        action: 'create',
        description: 'Create posts permission',
        isSystemPermission: false,
        createdAt: '2023-01-01T00:00:00.000Z',
      });
    });

    it('should throw BadRequestException when permission name already exists', async () => {
      const existingPermission = {
        id: 'existing-perm',
        name: 'new_permission',
        resource: 'users',
        action: 'read',
      };

      // Mock name uniqueness check (existing permission found)
      mockDb.select.mockReturnThis();
      mockDb.from.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.limit.mockResolvedValue([existingPermission]); // Existing permission found

      await expect(service.createPermission(createData)).rejects.toThrow(BadRequestException);
      await expect(service.createPermission(createData)).rejects.toThrow('Permission name already exists');

      // Should not call insert if name already exists
      expect(mockDb.insert).not.toHaveBeenCalled();
    });

    it('should handle permission with null createdAt', async () => {
      const mockCreatedPermission = {
        id: 'perm-new',
        name: 'new_permission',
        resource: 'posts',
        action: 'create',
        description: 'Create posts permission',
        isSystemPermission: false,
        createdAt: null,
      };

      mockDb.select.mockReturnThis();
      mockDb.from.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.limit.mockResolvedValueOnce([]);

      mockDb.insert.mockReturnThis();
      mockDb.values.mockReturnThis();
      mockDb.returning.mockResolvedValue([mockCreatedPermission]);

      const result = await service.createPermission(createData);

      expect(result.createdAt).toBeUndefined();
    });
  });

  describe('updatePermission', () => {
    const updateData = {
      name: 'updated_permission',
      resource: 'posts',
      action: 'update',
      description: 'Updated description',
    };

    it('should update a permission successfully', async () => {
      const mockUpdatedPermission = {
        id: 'perm-1',
        name: 'updated_permission',
        resource: 'posts',
        action: 'update',
        description: 'Updated description',
        isSystemPermission: false,
        createdAt: new Date('2023-01-01'),
      };

      mockDb.update.mockReturnThis();
      mockDb.set.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.returning.mockResolvedValue([mockUpdatedPermission]);

      const result = await service.updatePermission('perm-1', updateData);

      expect(mockDb.update).toHaveBeenCalled();
      expect(mockDb.set).toHaveBeenCalledWith({
        name: 'updated_permission',
        resource: 'posts',
        action: 'update',
        description: 'Updated description',
      });
      expect(mockDb.where).toHaveBeenCalled();
      expect(mockDb.returning).toHaveBeenCalled();
      expect(result).toEqual({
        id: 'perm-1',
        name: 'updated_permission',
        resource: 'posts',
        action: 'update',
        description: 'Updated description',
        isSystemPermission: false,
        createdAt: '2023-01-01T00:00:00.000Z',
      });
    });

    it('should throw NotFoundException when permission to update not found', async () => {
      mockDb.update.mockReturnThis();
      mockDb.set.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.returning.mockResolvedValue([]); // No permission updated = not found

      await expect(service.updatePermission('nonexistent', updateData)).rejects.toThrow(NotFoundException);
      await expect(service.updatePermission('nonexistent', updateData)).rejects.toThrow('Permission not found');
    });

    it('should handle partial updates', async () => {
      const partialUpdateData = {
        name: 'partially_updated',
      };

      const mockUpdatedPermission = {
        id: 'perm-1',
        name: 'partially_updated',
        resource: 'users',
        action: 'read',
        description: 'Original description',
        isSystemPermission: true,
        createdAt: new Date('2023-01-01'),
      };

      mockDb.update.mockReturnThis();
      mockDb.set.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.returning.mockResolvedValue([mockUpdatedPermission]);

      const result = await service.updatePermission('perm-1', partialUpdateData);

      expect(mockDb.set).toHaveBeenCalledWith({
        name: 'partially_updated',
        resource: undefined,
        action: undefined,
        description: undefined,
      });
      expect(result.name).toBe('partially_updated');
    });

    it('should handle permission with null createdAt', async () => {
      const mockUpdatedPermission = {
        id: 'perm-1',
        name: 'updated_permission',
        resource: 'posts',
        action: 'update',
        description: 'Updated description',
        isSystemPermission: false,
        createdAt: null,
      };

      mockDb.update.mockReturnThis();
      mockDb.set.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.returning.mockResolvedValue([mockUpdatedPermission]);

      const result = await service.updatePermission('perm-1', updateData);

      expect(result.createdAt).toBeUndefined();
    });
  });

  describe('deletePermission', () => {
    it('should delete a permission successfully', async () => {
      const mockDeletedPermission = {
        id: 'perm-1',
        name: 'deleted_permission',
        resource: 'posts',
        action: 'delete',
        description: 'Permission to delete',
        isSystemPermission: false,
        createdAt: new Date('2023-01-01'),
      };

      mockDb.delete.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.returning.mockResolvedValue([mockDeletedPermission]);

      const result = await service.deletePermission('perm-1');

      expect(mockDb.delete).toHaveBeenCalled();
      expect(mockDb.where).toHaveBeenCalled();
      expect(mockDb.returning).toHaveBeenCalled();
      expect(result).toEqual({ message: 'Permission deleted' });
    });

    it('should throw NotFoundException when permission to delete not found', async () => {
      mockDb.delete.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.returning.mockResolvedValue([]); // No permission deleted = not found

      await expect(service.deletePermission('nonexistent')).rejects.toThrow(NotFoundException);
      await expect(service.deletePermission('nonexistent')).rejects.toThrow('Permission not found');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockDb.select.mockReturnThis();
      mockDb.from.mockRejectedValue(new Error('Database connection failed'));

      await expect(service.listPermissions()).rejects.toThrow('Database connection failed');
    });

    it('should handle empty permission list', async () => {
      mockDb.select.mockReturnThis();
      mockDb.from.mockResolvedValue([]);

      const result = await service.listPermissions();

      expect(result).toEqual([]);
    });

    it('should handle undefined description in create', async () => {
      const createDataWithoutDescription = {
        name: 'no_desc_permission',
        resource: 'posts',
        action: 'create',
      };

      const mockCreatedPermission = {
        id: 'perm-new',
        name: 'no_desc_permission',
        resource: 'posts',
        action: 'create',
        description: undefined,
        isSystemPermission: false,
        createdAt: new Date('2023-01-01'),
      };

      mockDb.select.mockReturnThis();
      mockDb.from.mockReturnThis();
      mockDb.where.mockReturnThis();
      mockDb.limit.mockResolvedValueOnce([]);

      mockDb.insert.mockReturnThis();
      mockDb.values.mockReturnThis();
      mockDb.returning.mockResolvedValue([mockCreatedPermission]);

      const result = await service.createPermission(createDataWithoutDescription);

      expect(result.description).toBeUndefined();
    });
  });
});
