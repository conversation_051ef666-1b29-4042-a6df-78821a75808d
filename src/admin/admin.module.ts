import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule } from '../database/database.module.js';
import { EmailManagementModule } from '../email/email-management.module.js';
import { UsersModule } from '../users/users.module.js';
import { AdminController } from './admin.controller.js';
import { PermissionsService } from './permissions.service.js';
import { RolesService } from './roles.service.js';

@Module({
  imports: [UsersModule, DatabaseModule, EmailManagementModule],
  controllers: [AdminController],
  providers: [RolesService, PermissionsService],
})
export class AdminModule { }