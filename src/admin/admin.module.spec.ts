import { Test, TestingModule } from '@nestjs/testing';
import { DatabaseModule } from '../database/database.module';
import { EmailManagementModule } from '../email/email-management.module';
import { UsersModule } from '../users/users.module';
import { AdminController } from './admin.controller';
import { AdminModule } from './admin.module';
import { PermissionsService } from './permissions.service';
import { RolesService } from './roles.service';

// Mock the dependencies to avoid complex setup
jest.mock('../database/database.module', () => ({
  DatabaseModule: class MockDatabaseModule { },
}));

jest.mock('../users/users.module', () => ({
  UsersModule: class MockUsersModule { },
}));

jest.mock('../email/email-management.module', () => ({
  EmailManagementModule: class MockEmailManagementModule { },
}));

jest.mock('./admin.controller', () => ({
  AdminController: class MockAdminController { },
}));

jest.mock('./permissions.service', () => ({
  PermissionsService: class MockPermissionsService { },
}));

jest.mock('./roles.service', () => ({
  RolesService: class MockRolesService { },
}));

describe('AdminModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [AdminModule],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should compile successfully', () => {
    expect(module).toBeInstanceOf(TestingModule);
  });

  it('should import required modules', () => {
    expect(() => module.get(DatabaseModule)).not.toThrow();
    expect(() => module.get(UsersModule)).not.toThrow();
    expect(() => module.get(EmailManagementModule)).not.toThrow();
  });

  it('should provide AdminController', () => {
    expect(() => module.get(AdminController)).not.toThrow();
  });

  it('should provide RolesService', () => {
    expect(() => module.get(RolesService)).not.toThrow();
  });

  it('should provide PermissionsService', () => {
    expect(() => module.get(PermissionsService)).not.toThrow();
  });

  describe('module metadata', () => {
    it('should have correct imports', () => {
      const imports = Reflect.getMetadata('imports', AdminModule);
      expect(imports).toBeDefined();
      expect(imports).toContain(UsersModule);
      expect(imports).toContain(DatabaseModule);
      expect(imports).toContain(EmailManagementModule);
    });

    it('should have correct controllers', () => {
      const controllers = Reflect.getMetadata('controllers', AdminModule);
      expect(controllers).toBeDefined();
      expect(controllers).toContain(AdminController);
    });

    it('should have correct providers', () => {
      const providers = Reflect.getMetadata('providers', AdminModule);
      expect(providers).toBeDefined();
      expect(providers).toContain(RolesService);
      expect(providers).toContain(PermissionsService);
    });
  });

  describe('dependency injection', () => {
    it('should properly wire up module dependencies', () => {
      expect(() => {
        module.get(AdminController);
        module.get(RolesService);
        module.get(PermissionsService);
      }).not.toThrow();
    });
  });
});
