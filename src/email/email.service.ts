import { MailerService } from '@nestjs-modules/mailer';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface EmailContext {
  [key: string]: any;
}

export interface SendEmailOptions {
  to: string | string[];
  subject: string;
  template: string;
  context?: EmailContext;
  attachments?: any[];
  cc?: string | string[];
  bcc?: string | string[];
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
  ) { }

  /**
   * Format date for email templates
   */
  private formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short',
    }).format(date);
  }

  /**
   * Format device info for email templates
   */
  private formatDeviceInfo(deviceInfo?: any): string {
    if (!deviceInfo) return 'Unknown device';

    if (typeof deviceInfo === 'string') {
      return deviceInfo;
    }

    // Format object as readable string
    const parts = [];

    // Use explicit platform/browser if available
    if (deviceInfo.platform) parts.push(deviceInfo.platform);
    if (deviceInfo.browser) parts.push(deviceInfo.browser);

    // If no explicit platform/browser, parse from userAgent
    if (deviceInfo.userAgent && parts.length === 0) {
      const ua = deviceInfo.userAgent;

      // Extract browser
      if (ua.includes('Chrome') && !ua.includes('Chromium')) parts.push('Chrome');
      else if (ua.includes('Firefox')) parts.push('Firefox');
      else if (ua.includes('Safari') && !ua.includes('Chrome')) parts.push('Safari');
      else if (ua.includes('Edge')) parts.push('Edge');
      else if (ua.includes('Opera')) parts.push('Opera');

      // Extract platform/OS
      if (ua.includes('Windows NT')) parts.push('Windows');
      else if (ua.includes('Mac OS X') || ua.includes('Macintosh')) parts.push('macOS');
      else if (ua.includes('Linux') && !ua.includes('Android')) parts.push('Linux');
      else if (ua.includes('Android')) parts.push('Android');
      else if (ua.includes('iPhone') || ua.includes('iPad')) parts.push('iOS');
    }

    return parts.length > 0 ? parts.join(' - ') : 'Unknown device';
  }

  /**
   * Send an email using a template
   */
  async sendEmail(options: SendEmailOptions): Promise<boolean> {
    try {
      const baseUrl = this.configService.get('EMAIL_BASE_URL', 'http://localhost:3000');
      const siteName = this.configService.get('EMAIL_FROM_NAME', 'RSGlider');

      // Add common context variables
      const context = {
        ...options.context,
        baseUrl,
        siteName,
        currentYear: new Date().getFullYear(),
        supportEmail: this.configService.get('SUPPORT_EMAIL', '<EMAIL>'),
      };

      // For now, we'll use the template system as-is
      // The MIME warning is just a best practice notice, not a functional issue
      const mailOptions: any = {
        to: options.to,
        subject: options.subject,
        template: options.template,
        context,
        attachments: options.attachments,
        cc: options.cc,
        bcc: options.bcc,
      };

      await this.mailerService.sendMail(mailOptions);

      this.logger.log(`Email sent successfully to ${Array.isArray(options.to) ? options.to.join(', ') : options.to} using template ${options.template}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send email to ${Array.isArray(options.to) ? options.to.join(', ') : options.to}:`, error);
      return false;
    }
  }

  /**
   * Send welcome email with email verification
   */
  async sendWelcomeEmail(email: string, name: string, verificationToken: string): Promise<boolean> {
    const verificationUrl = `${this.configService.get('EMAIL_BASE_URL')}/auth/verify-email?token=${verificationToken}`;

    return this.sendEmail({
      to: email,
      subject: 'Welcome to RSGlider - Verify Your Email',
      template: 'welcome',
      context: {
        name,
        verificationUrl,
        verificationToken,
      },
    });
  }

  /**
   * Send email verification
   */
  async sendEmailVerification(email: string, name: string, verificationToken: string): Promise<boolean> {
    const verificationUrl = `${this.configService.get('EMAIL_BASE_URL')}/auth/verify-email?token=${verificationToken}`;

    return this.sendEmail({
      to: email,
      subject: 'Verify Your Email Address',
      template: 'email-verification',
      context: {
        name,
        verificationUrl,
        verificationToken,
      },
    });
  }

  /**
   * Send email verification confirmation
   */
  async sendEmailVerified(email: string, name: string): Promise<boolean> {
    const loginUrl = `${this.configService.get('EMAIL_BASE_URL')}/auth/login`;

    return this.sendEmail({
      to: email,
      subject: 'Email Verified Successfully - Welcome to RSGlider!',
      template: 'email-verified',
      context: {
        name,
        verifiedEmail: email,
        loginUrl,
      },
    });
  }

  /**
   * Send developer welcome email
   */
  async sendDeveloperWelcome(email: string, name: string, giteaUsername: string): Promise<boolean> {
    const giteaUrl = `${this.configService.get('GITEA_EXTERNAL_URL', 'http://localhost:3000/gitea')}`;
    const discordUrl = this.configService.get('DISCORD_URL', 'https://discord.gg/rsglider');

    return this.sendEmail({
      to: email,
      subject: 'You have been promoted to the Developer role on RSGlider',
      template: 'developer-welcome',
      context: {
        name,
        giteaUsername,
        giteaUrl,
        discordUrl,
      },
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordReset(email: string, name: string, resetToken: string): Promise<boolean> {
    const resetUrl = `${this.configService.get('EMAIL_BASE_URL')}/auth/reset-password?token=${resetToken}`;

    return this.sendEmail({
      to: email,
      subject: 'Reset Your Password',
      template: 'password-reset',
      context: {
        name,
        resetUrl,
        resetToken,
        expiresIn: '1 hour',
      },
    });
  }

  /**
   * Send password changed confirmation
   */
  async sendPasswordChanged(email: string, name: string, deviceInfo?: any): Promise<boolean> {
    return this.sendEmail({
      to: email,
      subject: 'Your Password Has Been Changed',
      template: 'password-changed',
      context: {
        name,
        changeTime: this.formatDate(new Date()),
        deviceInfo: this.formatDeviceInfo(deviceInfo),
        ipAddress: deviceInfo?.ipAddress || 'Unknown',
      },
    });
  }

  /**
   * Send email change verification
   */
  async sendEmailChangeVerification(newEmail: string, name: string, verificationToken: string): Promise<boolean> {
    const verificationUrl = `${this.configService.get('EMAIL_BASE_URL')}/auth/verify-email-change?token=${verificationToken}`;

    return this.sendEmail({
      to: newEmail,
      subject: 'Verify Your New Email Address',
      template: 'email-change-verification',
      context: {
        name,
        verificationUrl,
        verificationToken,
      },
    });
  }

  /**
   * Send email change notification to old email
   */
  async sendEmailChangeNotification(oldEmail: string, name: string, newEmail: string): Promise<boolean> {
    return this.sendEmail({
      to: oldEmail,
      subject: 'Your Email Address Has Been Changed',
      template: 'email-changed',
      context: {
        name,
        newEmail,
        changeTime: this.formatDate(new Date()),
      },
    });
  }

  /**
   * Send email changed confirmation to new email
   */
  async sendEmailChanged(newEmail: string, name: string, oldEmail: string): Promise<boolean> {
    return this.sendEmail({
      to: newEmail,
      subject: 'Email Address Changed Successfully',
      template: 'email-changed',
      context: {
        name,
        newEmail,
        oldEmail,
        changeTime: this.formatDate(new Date()),
      },
    });
  }

  /**
   * Send password sync notification
   */
  async sendPasswordSyncNotification(email: string, name: string, source: 'gitea' | 'rsglider'): Promise<boolean> {
    return this.sendEmail({
      to: email,
      subject: 'Password Synchronized Between Platforms',
      template: 'password-sync-notification',
      context: {
        name,
        source,
        syncTime: this.formatDate(new Date()),
      },
    });
  }

  /**
   * Send password sync failure notification
   */
  async sendPasswordSyncFailure(email: string, name: string, direction: string, error: string): Promise<boolean> {
    return this.sendEmail({
      to: email,
      subject: 'Password Synchronization Failed',
      template: 'password-sync-failure',
      context: {
        name,
        direction,
        error,
        failureTime: this.formatDate(new Date()),
      },
    });
  }

  /**
   * Send 2FA enabled notification
   */
  async send2FAEnabled(email: string, name: string, backupCodes: string[]): Promise<boolean> {
    return this.sendEmail({
      to: email,
      subject: 'Two-Factor Authentication Enabled',
      template: '2fa-enabled',
      context: {
        name,
        backupCodes,
        enabledTime: this.formatDate(new Date()),
      },
    });
  }

  /**
   * Send 2FA disabled notification
   */
  async send2FADisabled(email: string, name: string, deviceInfo?: any): Promise<boolean> {
    return this.sendEmail({
      to: email,
      subject: 'Two-Factor Authentication Disabled',
      template: '2fa-disabled',
      context: {
        name,
        disabledTime: this.formatDate(new Date()),
        deviceInfo: this.formatDeviceInfo(deviceInfo),
      },
    });
  }

  /**
   * Send new device login notification
   */
  async sendNewDeviceLogin(email: string, name: string, deviceInfo: any, location?: string): Promise<boolean> {
    return this.sendEmail({
      to: email,
      subject: 'New Device Login Detected',
      template: 'new-device-login',
      context: {
        name,
        deviceInfo,
        location: location || 'Unknown location',
        loginTime: new Date(),
        ipAddress: deviceInfo?.ipAddress || 'Unknown',
      },
    });
  }

  /**
   * Send security alert
   */
  async sendSecurityAlert(email: string, name: string, alertType: string, details: any): Promise<boolean> {
    return this.sendEmail({
      to: email,
      subject: `Security Alert: ${alertType}`,
      template: 'security-alert',
      context: {
        name,
        alertType,
        details,
        alertTime: new Date(),
      },
    });
  }




}
