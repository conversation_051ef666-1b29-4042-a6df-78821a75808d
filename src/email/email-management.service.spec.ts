import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { DatabaseService } from '../common/services/database.service.js';
import { UsersService } from '../users/users.service.js';
import { EmailManagementService } from './email-management.service.js';
import { EmailService } from './email.service.js';

describe('EmailManagementService', () => {
  let service: EmailManagementService;

  const mockDatabaseService = {
    db: {
      insert: jest.fn().mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn(),
        }),
      }),
      select: jest.fn().mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            orderBy: jest.fn().mockReturnValue({
              limit: jest.fn().mockReturnValue({
                offset: jest.fn(),
              }),
            }),
            limit: jest.fn(),
          }),
          orderBy: jest.fn().mockReturnValue({
            limit: jest.fn(),
          }),
          limit: jest.fn(),
        }),
      }),
      update: jest.fn().mockReturnValue({
        set: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            returning: jest.fn(),
          }),
        }),
      }),
      delete: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          returning: jest.fn(),
        }),
      }),
    },
  };

  const mockEmailService = {
    sendEmail: jest.fn(),
  };

  const mockUsersService = {
    findUsersByRole: jest.fn(),
    findAllUsers: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailManagementService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    service = module.get<EmailManagementService>(EmailManagementService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('createTemplate', () => {
    it('should create a new email template', async () => {
      const createDto = {
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        category: 'notification',
        isActive: true,
      };

      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: null,
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockTemplate]);

      const result = await service.createTemplate(createDto);

      expect(result.id).toBe('template-123');
      expect(result.name).toBe('Test Template');
      expect(result.subject).toBe('Test Subject');
      expect(result.htmlContent).toBe('<h1>Test</h1>');
      expect(result.textContent).toBe('Test');
      expect(result.category).toBe('notification');
      expect(result.isActive).toBe(true);

      expect(mockDatabaseService.db.insert).toHaveBeenCalled();
    });

    it('should create template with variables', async () => {
      const createDto = {
        name: 'Template with Variables',
        subject: 'Hello {{name}}',
        htmlContent: '<h1>Hello {{name}}</h1>',
        textContent: 'Hello {{name}}',
        variables: ['name', 'email'],
        category: 'welcome',
      };

      const mockTemplate = {
        id: 'template-456',
        name: 'Template with Variables',
        subject: 'Hello {{name}}',
        htmlContent: '<h1>Hello {{name}}</h1>',
        textContent: 'Hello {{name}}',
        variables: '["name","email"]',
        category: 'welcome',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockTemplate]);

      const result = await service.createTemplate(createDto);

      expect(result.variables).toEqual(['name', 'email']);
      expect(mockDatabaseService.db.insert).toHaveBeenCalled();
    });
  });

  describe('getTemplates', () => {
    it('should return paginated templates', async () => {
      const mockTemplates = [
        {
          id: 'template-1',
          name: 'Template 1',
          subject: 'Subject 1',
          htmlContent: '<h1>Content 1</h1>',
          textContent: 'Content 1',
          variables: null,
          category: 'notification',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const mockCountResult = [{ count: 1 }];

      // Mock the chained query methods
      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockTemplates),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getTemplates({});

      expect(result.data).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });

    it('should filter templates by category', async () => {
      const queryDto = { category: 'welcome' };

      const mockTemplates = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockTemplates),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getTemplates(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should filter templates by isActive status', async () => {
      const queryDto = { isActive: 'true' };

      const mockTemplates = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockTemplates),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getTemplates(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should filter templates by search term', async () => {
      const queryDto = { search: 'welcome', page: 1, limit: 5 };

      const mockTemplates = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockTemplates),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getTemplates(queryDto);

      expect(result.data).toHaveLength(0);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(5);
    });
  });

  describe('getTemplate', () => {
    it('should return a template by id', async () => {
      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: '["name"]',
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockTemplate]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      const result = await service.getTemplate('template-123');

      expect(result.id).toBe('template-123');
      expect(result.variables).toEqual(['name']);
    });

    it('should throw NotFoundException when template not found', async () => {
      const mockQuery = {
        where: jest.fn().mockResolvedValue([]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      await expect(service.getTemplate('nonexistent')).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateTemplate', () => {
    it('should update an existing template', async () => {
      const updateDto = {
        name: 'Updated Template',
        subject: 'Updated Subject',
      };

      const mockUpdatedTemplate = {
        id: 'template-123',
        name: 'Updated Template',
        subject: 'Updated Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: null,
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.update().set().where().returning.mockResolvedValue([mockUpdatedTemplate]);

      const result = await service.updateTemplate('template-123', updateDto);

      expect(result.name).toBe('Updated Template');
      expect(result.subject).toBe('Updated Subject');
    });

    it('should throw NotFoundException when updating non-existent template', async () => {
      const updateDto = { name: 'Updated' };

      mockDatabaseService.db.update().set().where().returning.mockResolvedValue([]);

      await expect(service.updateTemplate('nonexistent', updateDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteTemplate', () => {
    it('should delete a template', async () => {
      const mockQuery = {
        where: jest.fn().mockResolvedValue([{ id: 'template-123' }]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.delete().where().returning.mockResolvedValue([{ id: 'template-123' }]);

      await expect(service.deleteTemplate('template-123')).resolves.not.toThrow();
    });

    it('should throw NotFoundException when deleting non-existent template', async () => {
      const mockQuery = {
        where: jest.fn().mockResolvedValue([]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      // Mock delete to return empty array (no rows affected)
      mockDatabaseService.db.delete().where().returning.mockResolvedValue([]);

      await expect(service.deleteTemplate('nonexistent')).rejects.toThrow(NotFoundException);
    });
  });

  describe('createCampaign', () => {
    it('should create a new email campaign', async () => {
      const createDto = {
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all_users',
      };

      // Mock template exists
      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: null,
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockTemplate]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      const mockCampaign = {
        id: 'campaign-123',
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all_users',
        recipientCriteria: null,
        status: 'draft',
        scheduledAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockCampaign]);

      const result = await service.createCampaign(createDto);

      expect(result.id).toBe('campaign-123');
      expect(result.name).toBe('Test Campaign');
      expect(result.status).toBe('draft');
    });

    it('should create scheduled campaign', async () => {
      const scheduledDate = new Date(Date.now() + 86400000); // Tomorrow
      const createDto = {
        name: 'Scheduled Campaign',
        templateId: 'template-123',
        targetAudience: 'premium_users',
        scheduledAt: scheduledDate.toISOString(),
      };

      // Mock template exists
      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: null,
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockTemplate]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      const mockCampaign = {
        id: 'campaign-456',
        name: 'Scheduled Campaign',
        templateId: 'template-123',
        targetAudience: 'premium_users',
        recipientCriteria: null,
        status: 'scheduled',
        scheduledAt: scheduledDate,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockCampaign]);

      const result = await service.createCampaign(createDto);

      expect(result.status).toBe('scheduled');
      expect(result.scheduledAt).toEqual(scheduledDate);
    });
  });

  describe('executeCampaign', () => {
    it('should execute a draft campaign successfully', async () => {
      const campaignId = 'campaign-123';

      // Mock campaign
      const mockCampaign = {
        id: campaignId,
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all',
        status: 'draft',
        totalRecipients: 0,
        sentCount: 0,
        failedCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock template
      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Hello {{name}}',
        htmlContent: '<h1>Hello {{name}}</h1>',
        textContent: 'Hello {{name}}',
        variables: ['name'],
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock recipients
      const mockRecipients = [
        { email: '<EMAIL>', id: 'user1', name: 'User One', variables: { name: 'User One' } },
        { email: '<EMAIL>', id: 'user2', name: 'User Two', variables: { name: 'User Two' } },
      ];

      // Mock getCampaign
      jest.spyOn(service, 'getCampaign').mockResolvedValue(mockCampaign);

      // Mock getTemplate
      jest.spyOn(service, 'getTemplate').mockResolvedValue(mockTemplate);

      // Mock getRecipientsForCampaign
      jest.spyOn(service as any, 'getRecipientsForCampaign').mockResolvedValue(mockRecipients);

      // Mock database updates
      mockDatabaseService.db.update().set().where.mockResolvedValue([]);

      // Mock email service
      mockEmailService.sendEmail.mockResolvedValue(true);

      // Mock logEmail
      jest.spyOn(service as any, 'logEmail').mockResolvedValue({ id: 'log-123' });

      await service.executeCampaign(campaignId);

      expect(service.getCampaign).toHaveBeenCalledWith(campaignId);
      expect(service.getTemplate).toHaveBeenCalledWith('template-123');
      expect(mockEmailService.sendEmail).toHaveBeenCalledTimes(2);
      expect(mockDatabaseService.db.update).toHaveBeenCalled();
    });

    it('should throw error for non-draft/scheduled campaign', async () => {
      const campaignId = 'campaign-123';

      const mockCampaign = {
        id: campaignId,
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all',
        status: 'completed',
        totalRecipients: 0,
        sentCount: 0,
        failedCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(service, 'getCampaign').mockResolvedValue(mockCampaign);

      await expect(service.executeCampaign(campaignId)).rejects.toThrow(BadRequestException);
    });

    it('should handle campaign execution failure', async () => {
      const campaignId = 'campaign-123';

      const mockCampaign = {
        id: campaignId,
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all',
        status: 'draft',
        totalRecipients: 0,
        sentCount: 0,
        failedCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(service, 'getCampaign').mockResolvedValue(mockCampaign);
      jest.spyOn(service as any, 'getRecipientsForCampaign').mockRejectedValue(new Error('Database error'));

      mockDatabaseService.db.update().set().where.mockResolvedValue([]);

      await expect(service.executeCampaign(campaignId)).rejects.toThrow('Database error');

      // Should update campaign to failed status
      expect(mockDatabaseService.db.update).toHaveBeenCalled();
    });

    it('should handle individual email failures during campaign execution', async () => {
      const campaignId = 'campaign-123';

      const mockCampaign = {
        id: campaignId,
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all',
        status: 'draft',
        totalRecipients: 0,
        sentCount: 0,
        failedCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockTemplate = {
        id: 'template-123',
        name: 'Test Template',
        subject: 'Hello {{name}}',
        htmlContent: '<h1>Hello {{name}}</h1>',
        textContent: 'Hello {{name}}',
        variables: ['name'],
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockRecipients = [
        { email: '<EMAIL>', id: 'user1', name: 'User One', variables: { name: 'User One' } },
        { email: '<EMAIL>', id: 'user2', name: 'User Two', variables: { name: 'User Two' } },
      ];

      jest.spyOn(service, 'getCampaign').mockResolvedValue(mockCampaign);
      jest.spyOn(service, 'getTemplate').mockResolvedValue(mockTemplate);
      jest.spyOn(service as any, 'getRecipientsForCampaign').mockResolvedValue(mockRecipients);

      mockDatabaseService.db.update().set().where.mockResolvedValue([]);

      // Mock email service to fail for second recipient
      mockEmailService.sendEmail
        .mockResolvedValueOnce(true)  // First email succeeds
        .mockResolvedValueOnce(false); // Second email fails

      jest.spyOn(service as any, 'logEmail').mockResolvedValue({ id: 'log-123' });

      await service.executeCampaign(campaignId);

      expect(mockEmailService.sendEmail).toHaveBeenCalledTimes(2);
      expect(service as any).toHaveProperty('logEmail');
    });
  });

  describe('updateCampaign', () => {
    it('should update an existing campaign', async () => {
      const updateDto = {
        name: 'Updated Campaign',
        status: 'draft' as const,
      };

      const mockUpdatedCampaign = {
        id: 'campaign-123',
        name: 'Updated Campaign',
        templateId: 'template-123',
        targetAudience: 'all_users',
        recipientCriteria: null,
        status: 'draft',
        scheduledAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDatabaseService.db.update().set().where.mockReturnValue({
        returning: jest.fn().mockResolvedValue([mockUpdatedCampaign]),
      });

      const result = await service.updateCampaign('campaign-123', updateDto);

      expect(result.name).toBe('Updated Campaign');
      expect(result.status).toBe('draft');
    });

    it('should throw NotFoundException when updating non-existent campaign', async () => {
      const updateDto = { name: 'Updated' };

      mockDatabaseService.db.update().set().where.mockReturnValue({
        returning: jest.fn().mockResolvedValue([]),
      });

      await expect(service.updateCampaign('nonexistent', updateDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getCampaign', () => {
    it('should return a campaign by id', async () => {
      const mockCampaign = {
        id: 'campaign-123',
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all_users',
        recipientCriteria: null,
        status: 'draft',
        scheduledAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockCampaign]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      const result = await service.getCampaign('campaign-123');

      expect(result.id).toBe('campaign-123');
      expect(result.name).toBe('Test Campaign');
    });

    it('should throw NotFoundException when campaign not found', async () => {
      const mockQuery = {
        where: jest.fn().mockResolvedValue([]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      await expect(service.getCampaign('nonexistent')).rejects.toThrow(NotFoundException);
    });
  });

  describe('getCampaigns', () => {
    it('should return paginated campaigns', async () => {
      const mockCampaigns = [
        {
          id: 'campaign-1',
          name: 'Campaign 1',
          templateId: 'template-123',
          targetAudience: 'all_users',
          recipientCriteria: null,
          status: 'draft',
          scheduledAt: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const mockCountResult = [{ count: 1 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockCampaigns),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getCampaigns({});

      expect(result.data).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
    });

    it('should filter campaigns by status', async () => {
      const queryDto = { status: 'completed' };

      const mockCampaigns = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockCampaigns),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getCampaigns(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should filter campaigns by target audience', async () => {
      const queryDto = { targetAudience: 'admin' };

      const mockCampaigns = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockCampaigns),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getCampaigns(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should filter campaigns by search term', async () => {
      const queryDto = { search: 'newsletter' };

      const mockCampaigns = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockCampaigns),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getCampaigns(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });
  });

  describe('sendEmail', () => {
    it('should send email with template', async () => {
      const sendDto = {
        templateId: 'template-123',
        recipients: ['<EMAIL>'],
        variables: { name: 'John Doe' },
      };

      // Mock template
      const mockTemplate = {
        id: 'template-123',
        name: 'Welcome Template',
        subject: 'Welcome {{name}}',
        htmlContent: '<h1>Welcome {{name}}</h1>',
        textContent: 'Welcome {{name}}',
        variables: '["name"]',
        category: 'welcome',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockTemplate]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockEmailService.sendEmail.mockResolvedValue(true);

      const mockLog = {
        id: 'log-123',
        templateId: 'template-123',
        campaignId: null,
        recipientEmail: '<EMAIL>',
        subject: 'Welcome John Doe',
        status: 'sent',
        sentAt: new Date(),
        createdAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockLog]);

      const result = await service.sendEmail(sendDto);

      expect(result.success).toBe(true);
      expect(result.logId).toBe('log-123');
      expect(mockEmailService.sendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Welcome John Doe',
        template: 'custom',
        context: {
          content: '<h1>Welcome John Doe</h1>',
          textContent: 'Welcome John Doe',
          name: 'John Doe',
        },
      });
    });

    it('should send direct email without template', async () => {
      const sendDto = {
        recipients: ['<EMAIL>'],
        subject: 'Direct Email',
        htmlContent: '<h1>Direct Content</h1>',
        textContent: 'Direct Content',
      };

      mockEmailService.sendEmail.mockResolvedValue(true);

      const mockLog = {
        id: 'log-456',
        templateId: null,
        campaignId: null,
        recipientEmail: '<EMAIL>',
        subject: 'Direct Email',
        status: 'sent',
        sentAt: new Date(),
        createdAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockLog]);

      const result = await service.sendEmail(sendDto);

      expect(result.success).toBe(true);
      expect(mockEmailService.sendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Direct Email',
        template: 'direct',
        context: {
          content: '<h1>Direct Content</h1>',
          textContent: 'Direct Content',
        },
      });
    });

    it('should handle email sending failure', async () => {
      const sendDto = {
        recipients: ['<EMAIL>'],
        subject: 'Test Email',
        htmlContent: '<h1>Test</h1>',
      };

      mockEmailService.sendEmail.mockResolvedValue(false);

      const mockLog = {
        id: 'log-789',
        templateId: null,
        campaignId: null,
        recipientEmail: '<EMAIL>',
        subject: 'Test Email',
        status: 'failed',
        errorMessage: 'Email sending failed',
        createdAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockLog]);

      const result = await service.sendEmail(sendDto);

      expect(result.success).toBe(false);
      expect(result.logId).toBe('log-789');
    });

    it('should throw BadRequestException when missing subject and content without template', async () => {
      const sendDto = {
        recipients: ['<EMAIL>'],
        // Missing subject and htmlContent
      };

      await expect(service.sendEmail(sendDto)).rejects.toThrow(BadRequestException);
    });

    it('should handle template with textContent', async () => {
      const sendDto = {
        templateId: 'template-123',
        recipients: ['<EMAIL>'],
        variables: { name: 'John Doe' },
      };

      // Mock template with textContent
      const mockTemplate = {
        id: 'template-123',
        name: 'Welcome Template',
        subject: 'Welcome {{name}}',
        htmlContent: '<h1>Welcome {{name}}</h1>',
        textContent: 'Welcome {{name}}',
        variables: '["name"]',
        category: 'welcome',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockQuery = {
        where: jest.fn().mockResolvedValue([mockTemplate]),
      };

      mockDatabaseService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockEmailService.sendEmail.mockResolvedValue(true);

      const mockLog = {
        id: 'log-123',
        templateId: 'template-123',
        campaignId: null,
        recipientEmail: '<EMAIL>',
        subject: 'Welcome John Doe',
        status: 'sent',
        sentAt: new Date(),
        createdAt: new Date(),
      };

      mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockLog]);

      const result = await service.sendEmail(sendDto);

      expect(result.success).toBe(true);
      expect(mockEmailService.sendEmail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Welcome John Doe',
        template: 'custom',
        context: {
          content: '<h1>Welcome John Doe</h1>',
          textContent: 'Welcome John Doe',
          name: 'John Doe',
        },
      });
    });
  });

  describe('getAnalyticsSummary', () => {
    it('should have analytics summary method', () => {
      // Test that the method exists and is callable
      expect(service.getAnalyticsSummary).toBeDefined();
      expect(typeof service.getAnalyticsSummary).toBe('function');
    });

    it('should handle analytics errors gracefully', async () => {
      // Mock Promise.all to reject
      const originalPromiseAll = Promise.all;
      Promise.all = jest.fn().mockRejectedValue(new Error('Database connection failed'));

      try {
        await expect(service.getAnalyticsSummary()).rejects.toThrow(BadRequestException);
      } finally {
        // Restore Promise.all
        Promise.all = originalPromiseAll;
      }
    });
  });

  describe('getTemplateStats', () => {
    it('should return template statistics', async () => {
      const templateId = 'template-123';

      // Mock template
      const mockTemplate = {
        id: templateId,
        name: 'Test Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: ['name'],
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(service, 'getTemplate').mockResolvedValue(mockTemplate);

      // Mock stats queries
      const mockSentResult = [{ count: 45 }];
      const mockFailedResult = [{ count: 5 }];
      const mockLastUsedResult = [{ sentAt: new Date('2023-12-01') }];

      let queryCount = 0;
      const mockQueries = [
        { where: jest.fn().mockResolvedValue(mockSentResult) },
        { where: jest.fn().mockResolvedValue(mockFailedResult) },
        {
          where: jest.fn().mockReturnValue({
            orderBy: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue(mockLastUsedResult),
            }),
          }),
        },
      ];

      mockDatabaseService.db.select.mockImplementation(() => ({
        from: jest.fn().mockReturnValue(mockQueries[queryCount++] || mockQueries[0]),
      }));

      const result = await service.getTemplateStats(templateId);

      expect(result.templateId).toBe(templateId);
      expect(result.totalSent).toBe(45);
      expect(result.totalFailed).toBe(5);
      expect(result.successRate).toBe(90); // 45/(45+5) * 100
      expect(result.lastUsed).toEqual(new Date('2023-12-01'));
      expect(result.template).toEqual(mockTemplate);
    });

    it('should handle template with zero usage', async () => {
      const templateId = 'template-456';

      const mockTemplate = {
        id: templateId,
        name: 'Unused Template',
        subject: 'Test Subject',
        htmlContent: '<h1>Test</h1>',
        textContent: 'Test',
        variables: [],
        category: 'notification',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(service, 'getTemplate').mockResolvedValue(mockTemplate);

      const mockSentResult = [{ count: 0 }];
      const mockFailedResult = [{ count: 0 }];
      const mockLastUsedResult = [];

      let queryCount = 0;
      const mockQueries = [
        { where: jest.fn().mockResolvedValue(mockSentResult) },
        { where: jest.fn().mockResolvedValue(mockFailedResult) },
        {
          where: jest.fn().mockReturnValue({
            orderBy: jest.fn().mockReturnValue({
              limit: jest.fn().mockResolvedValue(mockLastUsedResult),
            }),
          }),
        },
      ];

      mockDatabaseService.db.select.mockImplementation(() => ({
        from: jest.fn().mockReturnValue(mockQueries[queryCount++] || mockQueries[0]),
      }));

      const result = await service.getTemplateStats(templateId);

      expect(result.totalSent).toBe(0);
      expect(result.totalFailed).toBe(0);
      expect(result.successRate).toBe(0);
      expect(result.lastUsed).toBeNull();
    });

    it('should handle template stats errors', async () => {
      const templateId = 'template-123';

      jest.spyOn(service, 'getTemplate').mockRejectedValue(new Error('Template not found'));

      await expect(service.getTemplateStats(templateId)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getCampaignStats', () => {
    it('should return campaign statistics', async () => {
      const campaignId = 'campaign-123';

      const mockCampaign = {
        id: campaignId,
        name: 'Test Campaign',
        templateId: 'template-123',
        targetAudience: 'all',
        status: 'completed',
        totalRecipients: 100,
        sentCount: 95,
        failedCount: 5,
        startedAt: new Date('2023-12-01T10:00:00Z'),
        completedAt: new Date('2023-12-01T10:30:00Z'),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(service, 'getCampaign').mockResolvedValue(mockCampaign);

      const mockSentResult = [{ count: 95 }];
      const mockFailedResult = [{ count: 5 }];

      let queryCount = 0;
      const mockQueries = [
        { where: jest.fn().mockResolvedValue(mockSentResult) },
        { where: jest.fn().mockResolvedValue(mockFailedResult) },
      ];

      mockDatabaseService.db.select.mockImplementation(() => ({
        from: jest.fn().mockReturnValue(mockQueries[queryCount++] || mockQueries[0]),
      }));

      const result = await service.getCampaignStats(campaignId);

      expect(result.campaignId).toBe(campaignId);
      expect(result.totalRecipients).toBe(100);
      expect(result.totalSent).toBe(95);
      expect(result.totalFailed).toBe(5);
      expect(result.successRate).toBe(95); // 95/100 * 100
      expect(result.executionTime).toBe(30 * 60 * 1000); // 30 minutes in milliseconds
      expect(result.campaign).toEqual(mockCampaign);
    });

    it('should handle campaign with no execution time', async () => {
      const campaignId = 'campaign-456';

      const mockCampaign = {
        id: campaignId,
        name: 'Draft Campaign',
        templateId: 'template-123',
        targetAudience: 'all',
        status: 'draft',
        totalRecipients: 0,
        sentCount: 0,
        failedCount: 0,
        startedAt: null,
        completedAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      jest.spyOn(service, 'getCampaign').mockResolvedValue(mockCampaign);

      const mockSentResult = [{ count: 0 }];
      const mockFailedResult = [{ count: 0 }];

      let queryCount = 0;
      const mockQueries = [
        { where: jest.fn().mockResolvedValue(mockSentResult) },
        { where: jest.fn().mockResolvedValue(mockFailedResult) },
      ];

      mockDatabaseService.db.select.mockImplementation(() => ({
        from: jest.fn().mockReturnValue(mockQueries[queryCount++] || mockQueries[0]),
      }));

      const result = await service.getCampaignStats(campaignId);

      expect(result.executionTime).toBeNull();
      expect(result.successRate).toBe(0);
    });
  });

  describe('getTemplateStats', () => {
    it('should call template stats methods', async () => {
      // Test that the method exists and can be called
      expect(service.getTemplateStats).toBeDefined();
      expect(typeof service.getTemplateStats).toBe('function');
    });
  });

  describe('getEmailLogs', () => {
    it('should return paginated email logs', async () => {
      const mockLogs = [
        {
          id: 'log-1',
          templateId: 'template-123',
          campaignId: null,
          recipientEmail: '<EMAIL>',
          subject: 'Test Email',
          status: 'sent',
          sentAt: new Date(),
          createdAt: new Date(),
        },
      ];

      const mockCountResult = [{ count: 1 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getEmailLogs({});

      expect(result.data).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
    });

    it('should filter logs by status', async () => {
      const queryDto = { status: 'failed' };

      const mockLogs = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getEmailLogs(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should filter logs by templateId', async () => {
      const queryDto = { templateId: 'template-123' };

      const mockLogs = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getEmailLogs(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should filter logs by campaignId', async () => {
      const queryDto = { campaignId: 'campaign-123' };

      const mockLogs = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getEmailLogs(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should filter logs by recipientEmail', async () => {
      const queryDto = { recipientEmail: '<EMAIL>' };

      const mockLogs = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getEmailLogs(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });

    it('should filter logs by date range', async () => {
      const queryDto = {
        sentAfter: '2023-01-01T00:00:00Z',
        sentBefore: '2023-12-31T23:59:59Z'
      };

      const mockLogs = [];
      const mockCountResult = [{ count: 0 }];

      const mockQuery = {
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        offset: jest.fn().mockResolvedValue(mockLogs),
      };

      const mockCountQuery = {
        where: jest.fn().mockResolvedValue(mockCountResult),
      };

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockQuery),
      });

      mockDatabaseService.db.select.mockReturnValueOnce({
        from: jest.fn().mockReturnValue(mockCountQuery),
      });

      const result = await service.getEmailLogs(queryDto);

      expect(result.data).toHaveLength(0);
      expect(mockQuery.where).toHaveBeenCalled();
    });
  });

  describe('private helper methods', () => {
    describe('getRecipientsForCampaign', () => {
      it('should get all users for "all" target audience', async () => {
        const campaign = {
          id: 'campaign-123',
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'all',
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const mockUsers = [
          { id: 'user1', email: '<EMAIL>', name: 'User One' },
          { id: 'user2', email: '<EMAIL>', name: 'User Two' },
        ];

        const mockQuery = {
          from: jest.fn().mockResolvedValue(mockUsers),
        };

        mockDatabaseService.db.select.mockReturnValue(mockQuery);

        const result = await (service as any).getRecipientsForCampaign(campaign);

        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({
          email: '<EMAIL>',
          id: 'user1',
          name: 'User One',
          variables: { name: 'User One' },
        });
      });

      it('should get users by role for "admin" target audience', async () => {
        const campaign = {
          id: 'campaign-123',
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'admin',
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const mockAdminUsers = [
          { id: 'admin1', email: '<EMAIL>', name: 'Admin One' },
        ];

        // Mock the complex join query for users by role
        const mockQuery = {
          from: jest.fn().mockReturnValue({
            innerJoin: jest.fn().mockReturnValue({
              innerJoin: jest.fn().mockReturnValue({
                where: jest.fn().mockResolvedValue(mockAdminUsers),
              }),
            }),
          }),
        };

        mockDatabaseService.db.select.mockReturnValue(mockQuery);

        const result = await (service as any).getRecipientsForCampaign(campaign);

        expect(result).toHaveLength(1);
        expect(result[0].email).toBe('<EMAIL>');
      });

      it('should get users by role for "user" target audience', async () => {
        const campaign = {
          id: 'campaign-123',
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'user',
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const mockRegularUsers = [
          { id: 'user1', email: '<EMAIL>', name: 'User One' },
        ];

        const mockQuery = {
          from: jest.fn().mockReturnValue({
            innerJoin: jest.fn().mockReturnValue({
              innerJoin: jest.fn().mockReturnValue({
                where: jest.fn().mockResolvedValue(mockRegularUsers),
              }),
            }),
          }),
        };

        mockDatabaseService.db.select.mockReturnValue(mockQuery);

        const result = await (service as any).getRecipientsForCampaign(campaign);

        expect(result).toHaveLength(1);
        expect(result[0].email).toBe('<EMAIL>');
      });

      it('should handle specific recipient criteria with emails', async () => {
        const campaign = {
          id: 'campaign-123',
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'specific',
          recipientCriteria: { emails: ["<EMAIL>", "<EMAIL>"] },
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const result = await (service as any).getRecipientsForCampaign(campaign);

        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({
          email: '<EMAIL>',
        });
        expect(result[1]).toEqual({
          email: '<EMAIL>',
        });
      });

      it('should handle specific recipient criteria with userIds', async () => {
        const campaign = {
          id: 'campaign-123',
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'specific',
          recipientCriteria: { userIds: ["user1", "user2"] },
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const mockSpecificUsers = [
          { id: 'user1', email: '<EMAIL>', name: 'User One' },
          { id: 'user2', email: '<EMAIL>', name: 'User Two' },
        ];

        const mockQuery = {
          from: jest.fn().mockReturnValue({
            where: jest.fn().mockResolvedValue(mockSpecificUsers),
          }),
        };

        mockDatabaseService.db.select.mockReturnValue(mockQuery);

        const result = await (service as any).getRecipientsForCampaign(campaign);

        expect(result).toHaveLength(2);
        expect(result[0]).toEqual({
          email: '<EMAIL>',
          id: 'user1',
          name: 'User One',
          variables: { name: 'User One' },
        });
      });

      it('should handle invalid recipient criteria JSON', async () => {
        const campaign = {
          id: 'campaign-123',
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'specific',
          recipientCriteria: 'invalid json',
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const result = await (service as any).getRecipientsForCampaign(campaign);

        expect(result).toEqual([]);
      });

      it('should return empty array for unknown target audience', async () => {
        const campaign = {
          id: 'campaign-123',
          name: 'Test Campaign',
          templateId: 'template-123',
          targetAudience: 'unknown',
          status: 'draft',
          totalRecipients: 0,
          sentCount: 0,
          failedCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const result = await (service as any).getRecipientsForCampaign(campaign);

        expect(result).toEqual([]);
      });
    });

    describe('logEmail', () => {
      it('should create email log entry', async () => {
        const logData = {
          templateId: 'template-123',
          campaignId: 'campaign-123',
          recipientEmail: '<EMAIL>',
          subject: 'Test Email',
          status: 'sent',
          sentAt: new Date(),
        };

        const mockLogEntry = {
          id: 'log-123',
          ...logData,
          createdAt: new Date(),
        };

        mockDatabaseService.db.insert().values().returning.mockResolvedValue([mockLogEntry]);

        const result = await (service as any).logEmail(logData);

        expect(result).toEqual(mockLogEntry);
        expect(mockDatabaseService.db.insert).toHaveBeenCalled();
      });
    });

    describe('mapLogToResponse', () => {
      it('should map log to response format', () => {
        const log = {
          id: 'log-123',
          templateId: 'template-123',
          campaignId: 'campaign-123',
          recipientEmail: '<EMAIL>',
          recipientUserId: 'user-123',
          subject: 'Test Email',
          status: 'sent',
          sentAt: new Date('2023-12-01'),
          errorMessage: null,
          createdAt: new Date('2023-12-01'),
        };

        const result = (service as any).mapLogToResponse(log);

        expect(result).toEqual({
          id: 'log-123',
          templateId: 'template-123',
          campaignId: 'campaign-123',
          recipientEmail: '<EMAIL>',
          recipientUserId: 'user-123',
          subject: 'Test Email',
          status: 'sent',
          sentAt: new Date('2023-12-01'),
          errorMessage: null,
          createdAt: new Date('2023-12-01'),
        });
      });
    });

    describe('utility methods', () => {
      it('should replace variables in text', () => {
        const text = 'Hello {{name}}, your email is {{email}}';
        const variables = { name: 'John', email: '<EMAIL>' };

        const result = (service as any).replaceVariables(text, variables);

        expect(result).toBe('Hello John, your <NAME_EMAIL>');
      });

      it('should handle missing variables gracefully', () => {
        const text = 'Hello {{name}}, your email is {{email}}';
        const variables = { name: 'John' }; // missing email

        const result = (service as any).replaceVariables(text, variables);

        expect(result).toBe('Hello John, your email is {{email}}');
      });

      it('should map template to response', () => {
        const template = {
          id: 'template-123',
          name: 'Test Template',
          subject: 'Test Subject',
          htmlContent: '<h1>Test</h1>',
          textContent: 'Test',
          variables: '["name","email"]',
          category: 'notification',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const result = (service as any).mapTemplateToResponse(template);

        expect(result.variables).toEqual(['name', 'email']);
        expect(result.id).toBe('template-123');
      });

      it('should handle null variables in template mapping', () => {
        const template = {
          id: 'template-456',
          name: 'Test Template',
          subject: 'Test Subject',
          htmlContent: '<h1>Test</h1>',
          textContent: 'Test',
          variables: null,
          category: 'notification',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        const result = (service as any).mapTemplateToResponse(template);

        expect(result.variables).toBeUndefined();
      });
    });
  });
});
