import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { and, count, desc, eq, gte, ilike, inArray, lte, or } from 'drizzle-orm';
import { DatabaseService } from '../common/services/database.service.js';
import {
    EmailCampaign,
    emailCampaigns,
    EmailLog,
    emailLogs,
    EmailTemplate,
    emailTemplates,
    NewEmailCampaign,
    NewEmailLog,
    NewEmailTemplate,
    roles,
    userRoles,
    users,
} from '../database/schema/index.js';
import { UsersService } from '../users/users.service.js';
import {
    CreateEmailCampaignDto,
    CreateEmailTemplateDto,
    EmailCampaignQueryDto,
    EmailCampaignResponseDto,
    EmailLogQueryDto,
    EmailLogResponseDto,
    EmailTemplateQueryDto,
    EmailTemplateResponseDto,
    SendEmailDto,
    UpdateEmailCampaignDto,
    UpdateEmailTemplateDto,
} from './dto/index.js';
import { EmailService } from './email.service.js';

@Injectable()
export class EmailManagementService {
    private readonly logger = new Logger(EmailManagementService.name);

    constructor(
        private readonly databaseService: DatabaseService,
        private readonly emailService: EmailService,
        private readonly usersService: UsersService,
    ) { }

    // === EMAIL TEMPLATES ===

    async createTemplate(createDto: CreateEmailTemplateDto): Promise<EmailTemplateResponseDto> {
        const newTemplate: NewEmailTemplate = {
            name: createDto.name,
            subject: createDto.subject,
            htmlContent: createDto.htmlContent,
            textContent: createDto.textContent,
            variables: createDto.variables ? JSON.stringify(createDto.variables) : null,
            category: createDto.category,
            isActive: createDto.isActive ?? true,
        };

        const [template] = await this.databaseService.db
            .insert(emailTemplates)
            .values(newTemplate)
            .returning();

        return this.mapTemplateToResponse(template);
    }

    async updateTemplate(id: string, updateDto: UpdateEmailTemplateDto): Promise<EmailTemplateResponseDto> {
        const updateData: Partial<NewEmailTemplate> = {
            ...updateDto,
            variables: updateDto.variables ? JSON.stringify(updateDto.variables) : undefined,
            updatedAt: new Date(),
        };

        const [template] = await this.databaseService.db
            .update(emailTemplates)
            .set(updateData)
            .where(eq(emailTemplates.id, id))
            .returning();

        if (!template) {
            throw new NotFoundException(`Email template with ID ${id} not found`);
        }

        return this.mapTemplateToResponse(template);
    }

    async getTemplate(id: string): Promise<EmailTemplateResponseDto> {
        const [template] = await this.databaseService.db
            .select()
            .from(emailTemplates)
            .where(eq(emailTemplates.id, id));

        if (!template) {
            throw new NotFoundException(`Email template with ID ${id} not found`);
        }

        return this.mapTemplateToResponse(template);
    }

    async getTemplates(query: EmailTemplateQueryDto): Promise<{
        data: EmailTemplateResponseDto[];
        pagination: { page: number; limit: number; total: number; totalPages: number };
    }> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const offset = (page - 1) * limit;

        let whereConditions = [];

        if (query.category) {
            whereConditions.push(eq(emailTemplates.category, query.category));
        }

        if (query.isActive !== undefined) {
            whereConditions.push(eq(emailTemplates.isActive, query.isActive === 'true'));
        }

        if (query.search) {
            whereConditions.push(
                or(
                    ilike(emailTemplates.name, `%${query.search}%`),
                    ilike(emailTemplates.subject, `%${query.search}%`)
                )
            );
        }

        const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

        const [templates, totalResult] = await Promise.all([
            this.databaseService.db
                .select()
                .from(emailTemplates)
                .where(whereClause)
                .orderBy(desc(emailTemplates.createdAt))
                .limit(limit)
                .offset(offset),
            this.databaseService.db
                .select({ count: count() })
                .from(emailTemplates)
                .where(whereClause),
        ]);

        const total = totalResult[0].count;
        const totalPages = Math.ceil(total / limit);

        return {
            data: templates.map(template => this.mapTemplateToResponse(template)),
            pagination: { page, limit, total, totalPages },
        };
    }

    async deleteTemplate(id: string): Promise<void> {
        const [template] = await this.databaseService.db
            .delete(emailTemplates)
            .where(eq(emailTemplates.id, id))
            .returning();

        if (!template) {
            throw new NotFoundException(`Email template with ID ${id} not found`);
        }
    }

    // === EMAIL CAMPAIGNS ===

    async createCampaign(createDto: CreateEmailCampaignDto): Promise<EmailCampaignResponseDto> {
        // Verify template exists
        await this.getTemplate(createDto.templateId);

        const newCampaign: NewEmailCampaign = {
            name: createDto.name,
            templateId: createDto.templateId,
            targetAudience: createDto.targetAudience,
            recipientCriteria: createDto.recipientCriteria ? JSON.stringify(createDto.recipientCriteria) : null,
            status: createDto.scheduledAt ? 'scheduled' : 'draft',
            scheduledAt: createDto.scheduledAt ? new Date(createDto.scheduledAt) : null,
        };

        const [campaign] = await this.databaseService.db
            .insert(emailCampaigns)
            .values(newCampaign)
            .returning();

        return this.mapCampaignToResponse(campaign);
    }

    async updateCampaign(id: string, updateDto: UpdateEmailCampaignDto): Promise<EmailCampaignResponseDto> {
        const updateData: Partial<NewEmailCampaign> = {
            ...updateDto,
            recipientCriteria: updateDto.recipientCriteria ? JSON.stringify(updateDto.recipientCriteria) : undefined,
            scheduledAt: updateDto.scheduledAt ? new Date(updateDto.scheduledAt) : undefined,
            updatedAt: new Date(),
        };

        const [campaign] = await this.databaseService.db
            .update(emailCampaigns)
            .set(updateData)
            .where(eq(emailCampaigns.id, id))
            .returning();

        if (!campaign) {
            throw new NotFoundException(`Email campaign with ID ${id} not found`);
        }

        return this.mapCampaignToResponse(campaign);
    }

    async getCampaign(id: string): Promise<EmailCampaignResponseDto> {
        const [campaign] = await this.databaseService.db
            .select()
            .from(emailCampaigns)
            .where(eq(emailCampaigns.id, id));

        if (!campaign) {
            throw new NotFoundException(`Email campaign with ID ${id} not found`);
        }

        return this.mapCampaignToResponse(campaign);
    }

    async getCampaigns(query: EmailCampaignQueryDto): Promise<{
        data: EmailCampaignResponseDto[];
        pagination: { page: number; limit: number; total: number; totalPages: number };
    }> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const offset = (page - 1) * limit;

        let whereConditions = [];

        if (query.status) {
            whereConditions.push(eq(emailCampaigns.status, query.status));
        }

        if (query.targetAudience) {
            whereConditions.push(eq(emailCampaigns.targetAudience, query.targetAudience));
        }

        if (query.search) {
            whereConditions.push(ilike(emailCampaigns.name, `%${query.search}%`));
        }

        const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

        const [campaigns, totalResult] = await Promise.all([
            this.databaseService.db
                .select()
                .from(emailCampaigns)
                .where(whereClause)
                .orderBy(desc(emailCampaigns.createdAt))
                .limit(limit)
                .offset(offset),
            this.databaseService.db
                .select({ count: count() })
                .from(emailCampaigns)
                .where(whereClause),
        ]);

        const total = totalResult[0].count;
        const totalPages = Math.ceil(total / limit);

        return {
            data: campaigns.map(campaign => this.mapCampaignToResponse(campaign)),
            pagination: { page, limit, total, totalPages },
        };
    }

    async executeCampaign(id: string): Promise<void> {
        const campaign = await this.getCampaign(id);

        if (campaign.status !== 'draft' && campaign.status !== 'scheduled') {
            throw new BadRequestException('Campaign can only be executed from draft or scheduled status');
        }

        // Update campaign status to sending
        await this.databaseService.db
            .update(emailCampaigns)
            .set({
                status: 'sending',
                startedAt: new Date(),
                updatedAt: new Date()
            })
            .where(eq(emailCampaigns.id, id));

        try {
            // Get recipients based on target audience
            const recipients = await this.getRecipientsForCampaign(campaign);

            // Update total recipients count
            await this.databaseService.db
                .update(emailCampaigns)
                .set({
                    totalRecipients: recipients.length,
                    updatedAt: new Date()
                })
                .where(eq(emailCampaigns.id, id));

            // Get template
            const template = await this.getTemplate(campaign.templateId);

            // Send emails and log results
            let sentCount = 0;
            let failedCount = 0;

            for (const recipient of recipients) {
                try {
                    // Use the existing EmailService to send emails
                    const emailSent = await this.emailService.sendEmail({
                        to: recipient.email,
                        subject: this.replaceVariables(template.subject, recipient.variables || {}),
                        template: 'campaign', // We'll need to create this template
                        context: {
                            content: this.replaceVariables(template.htmlContent, recipient.variables || {}),
                            textContent: template.textContent ? this.replaceVariables(template.textContent, recipient.variables || {}) : undefined,
                            recipientName: recipient.name || recipient.email,
                            ...recipient.variables,
                        },
                    });

                    if (emailSent) {
                        await this.logEmail({
                            templateId: template.id,
                            campaignId: id,
                            recipientEmail: recipient.email,
                            recipientUserId: recipient.id || null,
                            subject: template.subject,
                            status: 'sent',
                            sentAt: new Date(),
                        });
                        sentCount++;
                    } else {
                        throw new Error('Email service returned false');
                    }
                } catch (error) {
                    this.logger.error(`Failed to send email to ${recipient.email}:`, error);

                    await this.logEmail({
                        templateId: template.id,
                        campaignId: id,
                        recipientEmail: recipient.email,
                        recipientUserId: recipient.id || null,
                        subject: template.subject,
                        status: 'failed',
                        errorMessage: error.message,
                    });

                    failedCount++;
                }
            }

            // Update campaign completion status
            await this.databaseService.db
                .update(emailCampaigns)
                .set({
                    status: 'completed',
                    completedAt: new Date(),
                    sentCount,
                    failedCount,
                    updatedAt: new Date(),
                })
                .where(eq(emailCampaigns.id, id));

        } catch (error) {
            this.logger.error(`Campaign ${id} execution failed:`, error);

            // Update campaign to failed status
            await this.databaseService.db
                .update(emailCampaigns)
                .set({
                    status: 'failed',
                    completedAt: new Date(),
                    updatedAt: new Date(),
                })
                .where(eq(emailCampaigns.id, id));

            throw error;
        }
    }

    // === EMAIL SENDING ===

    async sendEmail(sendDto: SendEmailDto): Promise<{ success: boolean; logId: string }> {
        let template: EmailTemplateResponseDto | null = null;
        let subject = sendDto.subject;
        let htmlContent = sendDto.htmlContent;
        let textContent = sendDto.textContent;

        // If using template, get template and merge variables
        if (sendDto.templateId) {
            template = await this.getTemplate(sendDto.templateId);
            subject = this.replaceVariables(template.subject, sendDto.variables || {});
            htmlContent = this.replaceVariables(template.htmlContent, sendDto.variables || {});
            if (template.textContent) {
                textContent = this.replaceVariables(template.textContent, sendDto.variables || {});
            }
        }

        if (!subject || !htmlContent) {
            throw new BadRequestException('Subject and content are required when not using a template');
        }

        const results = [];

        for (const recipient of sendDto.recipients) {
            try {
                let emailSent = false;

                if (sendDto.templateId) {
                    // Use template-based sending
                    emailSent = await this.emailService.sendEmail({
                        to: recipient,
                        subject: subject!,
                        template: 'custom', // We'll create a generic template
                        context: {
                            content: htmlContent!,
                            textContent: textContent,
                            ...sendDto.variables,
                        },
                    });
                } else {
                    // Direct HTML sending - we'll create a simple wrapper
                    emailSent = await this.emailService.sendEmail({
                        to: recipient,
                        subject: subject!,
                        template: 'direct', // Simple wrapper template
                        context: {
                            content: htmlContent!,
                            textContent: textContent,
                        },
                    });
                }

                if (emailSent) {
                    const logEntry = await this.logEmail({
                        templateId: template?.id || null,
                        campaignId: sendDto.campaignId || null,
                        recipientEmail: recipient,
                        subject: subject!,
                        status: 'sent',
                        sentAt: new Date(),
                    });
                    results.push({ success: true, logId: logEntry.id });
                } else {
                    throw new Error('Email service returned false');
                }
            } catch (error) {
                this.logger.error(`Failed to send email to ${recipient}:`, error);

                const logEntry = await this.logEmail({
                    templateId: template?.id || null,
                    campaignId: sendDto.campaignId || null,
                    recipientEmail: recipient,
                    subject: subject!,
                    status: 'failed',
                    errorMessage: error.message,
                });

                results.push({ success: false, logId: logEntry.id });
            }
        }

        return results[0]; // For single recipient compatibility
    }

    // === EMAIL LOGS ===

    async getEmailLogs(query: EmailLogQueryDto): Promise<{
        data: EmailLogResponseDto[];
        pagination: { page: number; limit: number; total: number; totalPages: number };
    }> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const offset = (page - 1) * limit;

        let whereConditions = [];

        if (query.status) {
            whereConditions.push(eq(emailLogs.status, query.status));
        }

        if (query.templateId) {
            whereConditions.push(eq(emailLogs.templateId, query.templateId));
        }

        if (query.campaignId) {
            whereConditions.push(eq(emailLogs.campaignId, query.campaignId));
        }

        if (query.recipientEmail) {
            whereConditions.push(ilike(emailLogs.recipientEmail, `%${query.recipientEmail}%`));
        }

        if (query.sentAfter) {
            whereConditions.push(gte(emailLogs.sentAt, new Date(query.sentAfter)));
        }

        if (query.sentBefore) {
            whereConditions.push(lte(emailLogs.sentAt, new Date(query.sentBefore)));
        }

        const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

        const [logs, totalResult] = await Promise.all([
            this.databaseService.db
                .select()
                .from(emailLogs)
                .where(whereClause)
                .orderBy(desc(emailLogs.createdAt))
                .limit(limit)
                .offset(offset),
            this.databaseService.db
                .select({ count: count() })
                .from(emailLogs)
                .where(whereClause),
        ]);

        const total = totalResult[0].count;
        const totalPages = Math.ceil(total / limit);

        return {
            data: logs.map(log => this.mapLogToResponse(log)),
            pagination: { page, limit, total, totalPages },
        };
    }

    // === ANALYTICS METHODS ===

    async getAnalyticsSummary(): Promise<{
        totalEmailsSent: number;
        totalEmailsFailed: number;
        totalTemplates: number;
        totalCampaigns: number;
        recentActivity: Array<{
            type: string;
            description: string;
            timestamp: Date;
        }>;
    }> {
        try {
            const [
                sentEmailsResult,
                failedEmailsResult,
                templatesResult,
                campaignsResult,
                recentLogsResult,
            ] = await Promise.all([
                // Total sent emails
                this.databaseService.db
                    .select({ count: count() })
                    .from(emailLogs)
                    .where(eq(emailLogs.status, 'sent')),

                // Total failed emails
                this.databaseService.db
                    .select({ count: count() })
                    .from(emailLogs)
                    .where(eq(emailLogs.status, 'failed')),

                // Total templates
                this.databaseService.db
                    .select({ count: count() })
                    .from(emailTemplates),

                // Total campaigns
                this.databaseService.db
                    .select({ count: count() })
                    .from(emailCampaigns),

                // Recent activity (last 10 email logs)
                this.databaseService.db
                    .select({
                        status: emailLogs.status,
                        recipientEmail: emailLogs.recipientEmail,
                        subject: emailLogs.subject,
                        createdAt: emailLogs.createdAt,
                    })
                    .from(emailLogs)
                    .orderBy(desc(emailLogs.createdAt))
                    .limit(10),
            ]);

            const recentActivity = recentLogsResult.map(log => ({
                type: log.status === 'sent' ? 'email_sent' : 'email_failed',
                description: `Email "${log.subject}" ${log.status} to ${log.recipientEmail}`,
                timestamp: log.createdAt,
            }));

            return {
                totalEmailsSent: sentEmailsResult[0].count,
                totalEmailsFailed: failedEmailsResult[0].count,
                totalTemplates: templatesResult[0].count,
                totalCampaigns: campaignsResult[0].count,
                recentActivity,
            };
        } catch (error) {
            this.logger.error('Error retrieving analytics summary:', error);
            throw new BadRequestException('Failed to retrieve analytics summary');
        }
    }

    async getTemplateStats(templateId: string): Promise<{
        templateId: string;
        totalSent: number;
        totalFailed: number;
        successRate: number;
        lastUsed: Date | null;
        template: EmailTemplateResponseDto;
    }> {
        try {
            // Verify template exists
            const template = await this.getTemplate(templateId);

            const [sentResult, failedResult, lastUsedResult] = await Promise.all([
                // Total sent for this template
                this.databaseService.db
                    .select({ count: count() })
                    .from(emailLogs)
                    .where(and(
                        eq(emailLogs.templateId, templateId),
                        eq(emailLogs.status, 'sent')
                    )),

                // Total failed for this template
                this.databaseService.db
                    .select({ count: count() })
                    .from(emailLogs)
                    .where(and(
                        eq(emailLogs.templateId, templateId),
                        eq(emailLogs.status, 'failed')
                    )),

                // Last used date
                this.databaseService.db
                    .select({ sentAt: emailLogs.sentAt })
                    .from(emailLogs)
                    .where(eq(emailLogs.templateId, templateId))
                    .orderBy(desc(emailLogs.sentAt))
                    .limit(1),
            ]);

            const totalSent = sentResult[0].count;
            const totalFailed = failedResult[0].count;
            const totalEmails = totalSent + totalFailed;
            const successRate = totalEmails > 0 ? (totalSent / totalEmails) * 100 : 0;
            const lastUsed = lastUsedResult[0]?.sentAt || null;

            return {
                templateId,
                totalSent,
                totalFailed,
                successRate: Math.round(successRate * 100) / 100, // Round to 2 decimal places
                lastUsed,
                template,
            };
        } catch (error) {
            this.logger.error(`Error retrieving template stats for ${templateId}:`, error);
            throw new BadRequestException('Failed to retrieve template statistics');
        }
    }

    async getCampaignStats(campaignId: string): Promise<{
        campaignId: string;
        totalRecipients: number;
        totalSent: number;
        totalFailed: number;
        successRate: number;
        executionTime: number | null;
        campaign: EmailCampaignResponseDto;
    }> {
        try {
            // Verify campaign exists
            const campaign = await this.getCampaign(campaignId);

            const [sentResult, failedResult] = await Promise.all([
                // Total sent for this campaign
                this.databaseService.db
                    .select({ count: count() })
                    .from(emailLogs)
                    .where(and(
                        eq(emailLogs.campaignId, campaignId),
                        eq(emailLogs.status, 'sent')
                    )),

                // Total failed for this campaign
                this.databaseService.db
                    .select({ count: count() })
                    .from(emailLogs)
                    .where(and(
                        eq(emailLogs.campaignId, campaignId),
                        eq(emailLogs.status, 'failed')
                    )),
            ]);

            const totalSent = sentResult[0].count;
            const totalFailed = failedResult[0].count;
            const successRate = campaign.totalRecipients > 0
                ? (totalSent / campaign.totalRecipients) * 100
                : 0;

            // Calculate execution time if campaign has started and completed
            let executionTime: number | null = null;
            if (campaign.startedAt && campaign.completedAt) {
                executionTime = campaign.completedAt.getTime() - campaign.startedAt.getTime();
            }

            return {
                campaignId,
                totalRecipients: campaign.totalRecipients,
                totalSent,
                totalFailed,
                successRate: Math.round(successRate * 100) / 100, // Round to 2 decimal places
                executionTime,
                campaign,
            };
        } catch (error) {
            this.logger.error(`Error retrieving campaign stats for ${campaignId}:`, error);
            throw new BadRequestException('Failed to retrieve campaign statistics');
        }
    }

    // === PRIVATE METHODS ===

    private async logEmail(logData: Partial<NewEmailLog> & {
        recipientEmail: string;
        subject: string;
        status: string;
    }): Promise<EmailLog> {
        const [log] = await this.databaseService.db
            .insert(emailLogs)
            .values(logData as NewEmailLog)
            .returning();

        return log;
    }

    private async getRecipientsForCampaign(campaign: EmailCampaignResponseDto): Promise<Array<{
        email: string;
        id?: string;
        name?: string;
        variables?: Record<string, any>;
    }>> {
        let recipients: Array<{ email: string; id?: string; name?: string; variables?: Record<string, any> }> = [];

        try {
            if (campaign.targetAudience === 'all') {
                // Get all users from the database
                const allUsers = await this.databaseService.db
                    .select({
                        id: users.id,
                        email: users.email,
                        name: users.name,
                    })
                    .from(users);

                recipients = allUsers.map(user => ({
                    email: user.email,
                    id: user.id,
                    name: user.name,
                    variables: { name: user.name },
                }));
            } else if (campaign.targetAudience === 'admin' || campaign.targetAudience === 'user') {
                // Get users by role
                const usersByRole = await this.databaseService.db
                    .select({
                        id: users.id,
                        email: users.email,
                        name: users.name,
                    })
                    .from(users)
                    .innerJoin(userRoles, eq(users.id, userRoles.userId))
                    .innerJoin(roles, eq(userRoles.roleId, roles.id))
                    .where(eq(roles.name, campaign.targetAudience));

                recipients = usersByRole.map(user => ({
                    email: user.email,
                    id: user.id,
                    name: user.name,
                    variables: { name: user.name },
                }));
            } else if (campaign.targetAudience === 'specific' && campaign.recipientCriteria) {
                // Handle specific criteria
                const criteria = campaign.recipientCriteria;

                if (criteria.emails && Array.isArray(criteria.emails)) {
                    // Direct email list
                    recipients = criteria.emails.map((email: string) => ({ email }));
                } else if (criteria.userIds && Array.isArray(criteria.userIds)) {
                    // Specific user IDs
                    const specificUsers = await this.databaseService.db
                        .select({
                            id: users.id,
                            email: users.email,
                            name: users.name,
                        })
                        .from(users)
                        .where(inArray(users.id, criteria.userIds));

                    recipients = specificUsers.map(user => ({
                        email: user.email,
                        id: user.id,
                        name: user.name,
                        variables: { name: user.name },
                    }));
                }
            }
        } catch (error) {
            this.logger.error('Error retrieving campaign recipients:', error);
            throw new BadRequestException('Failed to retrieve campaign recipients');
        }

        return recipients;
    }

    private replaceVariables(content: string, variables: Record<string, any>): string {
        let result = content;

        Object.entries(variables).forEach(([key, value]) => {
            const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
            result = result.replace(regex, String(value));
        });

        return result;
    }

    private mapTemplateToResponse(template: EmailTemplate): EmailTemplateResponseDto {
        return {
            id: template.id,
            name: template.name,
            subject: template.subject,
            htmlContent: template.htmlContent,
            textContent: template.textContent,
            variables: template.variables ? JSON.parse(template.variables) : undefined,
            category: template.category,
            isActive: template.isActive,
            createdAt: template.createdAt,
            updatedAt: template.updatedAt,
        };
    }

    private mapCampaignToResponse(campaign: EmailCampaign): EmailCampaignResponseDto {
        return {
            id: campaign.id,
            name: campaign.name,
            templateId: campaign.templateId,
            targetAudience: campaign.targetAudience,
            recipientCriteria: campaign.recipientCriteria ? JSON.parse(campaign.recipientCriteria) : undefined,
            status: campaign.status,
            scheduledAt: campaign.scheduledAt,
            startedAt: campaign.startedAt,
            completedAt: campaign.completedAt,
            totalRecipients: campaign.totalRecipients,
            sentCount: campaign.sentCount,
            failedCount: campaign.failedCount,
            createdAt: campaign.createdAt,
            updatedAt: campaign.updatedAt,
        };
    }

    private mapLogToResponse(log: EmailLog): EmailLogResponseDto {
        return {
            id: log.id,
            templateId: log.templateId,
            campaignId: log.campaignId,
            recipientEmail: log.recipientEmail,
            recipientUserId: log.recipientUserId,
            subject: log.subject,
            status: log.status,
            errorMessage: log.errorMessage,
            sentAt: log.sentAt,
            createdAt: log.createdAt,
        };
    }
} 