import { MailerModule } from '@nestjs-modules/mailer';
import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { join } from 'path';
import { EmailManagementModule } from './email-management.module.js';
import { EmailService } from './email.service.js';

@Module({
  imports: [
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const isTest = configService.get('NODE_ENV') === 'test';

        const smtpUser = configService.get('SMTP_USER');
        const smtpPassword = configService.get('SMTP_PASSWORD');

        const config: any = {
          transport: {
            host: configService.get('SMTP_HOST'),
            port: parseInt(configService.get('SMTP_PORT', '587'), 10),
            secure: configService.get('SMTP_SECURE') === 'true',
            // Only include auth if credentials are provided
            ...(smtpUser && smtpPassword ? {
              auth: {
                user: smtpUser,
                pass: smtpPassword,
              },
            } : {}),
            // Additional options for development
            ignoreTLS: configService.get('NODE_ENV') === 'development',
            requireTLS: configService.get('NODE_ENV') === 'production',
          },
          defaults: {
            from: `"${configService.get('EMAIL_FROM_NAME', 'RSGlider')}" <${configService.get('SMTP_FROM', '<EMAIL>')}>`,
          },
          // Preview emails in development
          preview: configService.get('NODE_ENV') === 'development',
        };

        // Only load HandlebarsAdapter when not in test mode to avoid Jest open handles
        if (!isTest) {
          const { HandlebarsAdapter } = await import('@nestjs-modules/mailer/dist/adapters/handlebars.adapter.js');
          config.template = {
            dir: join(process.cwd(), 'src', 'email', 'templates'),
            adapter: new HandlebarsAdapter({
              // Handlebars helpers
              helpers: {
                // Format date helper
                formatDate: function (date: any) {
                  return new Intl.DateTimeFormat('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  }).format(new Date(date));
                },
                // Conditional helper
                ifEquals: function (arg1: any, arg2: any, options: any) {
                  return (arg1 === arg2) ? options.fn(this) : options.inverse(this);
                },
                // URL helper for generating links
                url: function (path: any) {
                  const baseUrl = process.env.EMAIL_BASE_URL || 'http://localhost:3000';
                  return `${baseUrl}${path}`;
                },
              } as any,
            }),
            options: {
              strict: true,
            },
          };

          // Configure partials directory
          config.options = {
            partials: {
              dir: join(process.cwd(), 'src', 'email', 'templates', 'partials'),
              options: {
                strict: true,
              },
            },
          };
        }

        return config;
      },
      inject: [ConfigService],
    }),
    forwardRef(() => EmailManagementModule),
  ],
  providers: [EmailService],
  exports: [EmailService],
})
export class EmailModule { }
