import { forwardRef, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '../database/database.module.js';
import { UsersModule } from '../users/users.module.js';
import { EmailManagementService } from './email-management.service.js';
import { EmailModule } from './email.module.js';

@Module({
    imports: [
        ConfigModule,
        forwardRef(() => EmailModule),
        DatabaseModule,
        forwardRef(() => UsersModule),
    ],
    providers: [EmailManagementService],
    exports: [EmailManagementService],
})
export class EmailManagementModule { } 