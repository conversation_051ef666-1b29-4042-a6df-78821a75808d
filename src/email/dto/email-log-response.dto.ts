import { ApiProperty } from '@nestjs/swagger';
import { EmailCampaignResponseDto } from './email-campaign-response.dto.js';
import { EmailTemplateResponseDto } from './email-template-response.dto.js';

export class EmailLogResponseDto {
    @ApiProperty({ description: 'Log ID', example: '123e4567-e89b-12d3-a456-426614174000' })
    id: string;

    @ApiProperty({
        description: 'Template ID used',
        example: '123e4567-e89b-12d3-a456-426614174000',
        required: false,
    })
    templateId?: string;

    @ApiProperty({ description: 'Associated email template', required: false })
    template?: EmailTemplateResponseDto;

    @ApiProperty({
        description: 'Campaign ID if part of campaign',
        example: '123e4567-e89b-12d3-a456-426614174000',
        required: false,
    })
    campaignId?: string;

    @ApiProperty({ description: 'Associated email campaign', required: false })
    campaign?: EmailCampaignResponseDto;

    @ApiProperty({ description: 'Recipient email address', example: '<EMAIL>' })
    recipientEmail: string;

    @ApiProperty({
        description: 'Recipient user ID (if registered user)',
        example: '123e4567-e89b-12d3-a456-426614174000',
        required: false,
    })
    recipientUserId?: string;

    @ApiProperty({ description: 'Email subject that was sent', example: 'Welcome to our platform!' })
    subject: string;

    @ApiProperty({
        description: 'Email status',
        example: 'sent',
        enum: ['sent', 'failed', 'queued'],
    })
    status: string;

    @ApiProperty({
        description: 'Error message if failed',
        example: 'SMTP connection failed',
        required: false,
    })
    errorMessage?: string;

    @ApiProperty({
        description: 'Timestamp when email was sent',
        example: '2024-01-01T10:00:00Z',
        required: false,
    })
    sentAt?: Date;

    @ApiProperty({ description: 'Log creation timestamp', example: '2024-01-01T10:00:00Z' })
    createdAt: Date;
} 