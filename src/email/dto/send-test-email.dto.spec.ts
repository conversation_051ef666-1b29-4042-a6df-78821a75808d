import { validate } from 'class-validator';
import { SendTestEmailDto } from './send-test-email.dto';

describe('SendTestEmailDto', () => {
  let dto: SendTestEmailDto;

  beforeEach(() => {
    dto = new SendTestEmailDto();
  });

  it('should be defined', () => {
    expect(dto).toBeDefined();
  });

  describe('to field validation', () => {
    it('should accept valid email address', async () => {
      dto.to = '<EMAIL>';
      const errors = await validate(dto);
      const toErrors = errors.filter(error => error.property === 'to');
      expect(toErrors).toHaveLength(0);
    });

    it('should accept undefined (optional field)', async () => {
      dto.to = undefined;
      const errors = await validate(dto);
      const toErrors = errors.filter(error => error.property === 'to');
      expect(toErrors).toHaveLength(0);
    });

    it('should reject invalid email format', async () => {
      dto.to = 'invalid-email';
      const errors = await validate(dto);
      const toErrors = errors.filter(error => error.property === 'to');
      expect(toErrors).toHaveLength(1);
      expect(toErrors[0].constraints).toHaveProperty('isEmail');
    });

    it('should reject empty string', async () => {
      dto.to = '';
      const errors = await validate(dto);
      const toErrors = errors.filter(error => error.property === 'to');
      expect(toErrors).toHaveLength(1);
      expect(toErrors[0].constraints).toHaveProperty('isEmail');
    });

    it('should reject non-string values', async () => {
      (dto as any).to = 123;
      const errors = await validate(dto);
      const toErrors = errors.filter(error => error.property === 'to');
      expect(toErrors).toHaveLength(1);
      expect(toErrors[0].constraints).toHaveProperty('isEmail');
    });
  });

  describe('subject field validation', () => {
    it('should accept valid string', async () => {
      dto.subject = 'Test Subject';
      const errors = await validate(dto);
      const subjectErrors = errors.filter(error => error.property === 'subject');
      expect(subjectErrors).toHaveLength(0);
    });

    it('should accept undefined (optional field)', async () => {
      dto.subject = undefined;
      const errors = await validate(dto);
      const subjectErrors = errors.filter(error => error.property === 'subject');
      expect(subjectErrors).toHaveLength(0);
    });

    it('should accept empty string', async () => {
      dto.subject = '';
      const errors = await validate(dto);
      const subjectErrors = errors.filter(error => error.property === 'subject');
      expect(subjectErrors).toHaveLength(0);
    });

    it('should reject non-string values', async () => {
      (dto as any).subject = 123;
      const errors = await validate(dto);
      const subjectErrors = errors.filter(error => error.property === 'subject');
      expect(subjectErrors).toHaveLength(1);
      expect(subjectErrors[0].constraints).toHaveProperty('isString');
    });

    it('should accept long subject lines', async () => {
      dto.subject = 'A'.repeat(200);
      const errors = await validate(dto);
      const subjectErrors = errors.filter(error => error.property === 'subject');
      expect(subjectErrors).toHaveLength(0);
    });
  });

  describe('template field validation', () => {
    it('should accept valid template name', async () => {
      dto.template = 'welcome';
      const errors = await validate(dto);
      const templateErrors = errors.filter(error => error.property === 'template');
      expect(templateErrors).toHaveLength(0);
    });

    it('should accept undefined (optional field)', async () => {
      dto.template = undefined;
      const errors = await validate(dto);
      const templateErrors = errors.filter(error => error.property === 'template');
      expect(templateErrors).toHaveLength(0);
    });

    it('should accept template with hyphens and underscores', async () => {
      dto.template = 'password-reset_template';
      const errors = await validate(dto);
      const templateErrors = errors.filter(error => error.property === 'template');
      expect(templateErrors).toHaveLength(0);
    });

    it('should reject non-string values', async () => {
      (dto as any).template = 123;
      const errors = await validate(dto);
      const templateErrors = errors.filter(error => error.property === 'template');
      expect(templateErrors).toHaveLength(1);
      expect(templateErrors[0].constraints).toHaveProperty('isString');
    });

    it('should accept empty string', async () => {
      dto.template = '';
      const errors = await validate(dto);
      const templateErrors = errors.filter(error => error.property === 'template');
      expect(templateErrors).toHaveLength(0);
    });
  });

  describe('context field validation', () => {
    it('should accept valid object', async () => {
      dto.context = { message: 'Hello', userName: 'John' };
      const errors = await validate(dto);
      const contextErrors = errors.filter(error => error.property === 'context');
      expect(contextErrors).toHaveLength(0);
    });

    it('should accept undefined (optional field)', async () => {
      dto.context = undefined;
      const errors = await validate(dto);
      const contextErrors = errors.filter(error => error.property === 'context');
      expect(contextErrors).toHaveLength(0);
    });

    it('should accept empty object', async () => {
      dto.context = {};
      const errors = await validate(dto);
      const contextErrors = errors.filter(error => error.property === 'context');
      expect(contextErrors).toHaveLength(0);
    });

    it('should accept nested objects', async () => {
      dto.context = {
        user: { name: 'John', email: '<EMAIL>' },
        settings: { theme: 'dark', notifications: true },
      };
      const errors = await validate(dto);
      const contextErrors = errors.filter(error => error.property === 'context');
      expect(contextErrors).toHaveLength(0);
    });

    it('should accept arrays in context', async () => {
      dto.context = {
        items: ['item1', 'item2', 'item3'],
        numbers: [1, 2, 3],
      };
      const errors = await validate(dto);
      const contextErrors = errors.filter(error => error.property === 'context');
      expect(contextErrors).toHaveLength(0);
    });

    it('should reject non-object values', async () => {
      (dto as any).context = 'not an object';
      const errors = await validate(dto);
      const contextErrors = errors.filter(error => error.property === 'context');
      expect(contextErrors).toHaveLength(1);
      expect(contextErrors[0].constraints).toHaveProperty('isObject');
    });

    it('should reject arrays as top-level context', async () => {
      (dto as any).context = ['item1', 'item2'];
      const errors = await validate(dto);
      const contextErrors = errors.filter(error => error.property === 'context');
      expect(contextErrors).toHaveLength(1);
      expect(contextErrors[0].constraints).toHaveProperty('isObject');
    });

    it('should reject primitive values', async () => {
      (dto as any).context = 123;
      const errors = await validate(dto);
      const contextErrors = errors.filter(error => error.property === 'context');
      expect(contextErrors).toHaveLength(1);
      expect(contextErrors[0].constraints).toHaveProperty('isObject');
    });
  });

  describe('complete DTO validation', () => {
    it('should validate complete valid DTO', async () => {
      dto.to = '<EMAIL>';
      dto.subject = 'Test Email Subject';
      dto.template = 'test-template';
      dto.context = {
        message: 'Hello World',
        userName: 'Test User',
        customData: { key: 'value' },
      };

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty DTO (all optional fields)', async () => {
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should handle multiple validation errors', async () => {
      (dto as any).to = 'invalid-email';
      (dto as any).subject = 123;
      (dto as any).template = true;
      (dto as any).context = 'not an object';

      const errors = await validate(dto);
      expect(errors).toHaveLength(4);
      
      const errorProperties = errors.map(error => error.property);
      expect(errorProperties).toContain('to');
      expect(errorProperties).toContain('subject');
      expect(errorProperties).toContain('template');
      expect(errorProperties).toContain('context');
    });

    it('should preserve valid fields when others are invalid', async () => {
      dto.to = '<EMAIL>';
      dto.subject = 'Valid Subject';
      (dto as any).template = 123; // Invalid
      dto.context = { valid: 'context' };

      const errors = await validate(dto);
      expect(errors).toHaveLength(1);
      expect(errors[0].property).toBe('template');
      
      // Valid fields should remain unchanged
      expect(dto.to).toBe('<EMAIL>');
      expect(dto.subject).toBe('Valid Subject');
      expect(dto.context).toEqual({ valid: 'context' });
    });
  });
});
