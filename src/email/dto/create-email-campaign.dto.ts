import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsIn, IsNotEmpty, IsObject, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

export class CreateEmailCampaignDto {
    @ApiProperty({ description: 'Campaign name', example: 'Holiday Sale 2024' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(255)
    name: string;

    @ApiProperty({
        description: 'Email template ID',
        example: '123e4567-e89b-12d3-a456-************',
    })
    @IsUUID()
    templateId: string;

    @ApiProperty({
        description: 'Target audience',
        example: 'all',
        enum: ['all', 'admin', 'user', 'specific'],
    })
    @IsString()
    @IsIn(['all', 'admin', 'user', 'specific'])
    targetAudience: string;

    @ApiProperty({
        description: 'Recipient criteria for filtering (JSON object)',
        example: { roles: ['user'], createdAfter: '2024-01-01' },
        required: false,
    })
    @IsObject()
    @IsOptional()
    recipientCriteria?: Record<string, any>;

    @ApiProperty({
        description: 'Schedule the campaign for later (ISO string)',
        example: '2024-12-25T10:00:00Z',
        required: false,
    })
    @IsDateString()
    @IsOptional()
    scheduledAt?: string;
} 