import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, IsEmail, IsObject, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

export class SendEmailDto {
    @ApiProperty({
        description: 'Email template ID (if using template)',
        example: '123e4567-e89b-12d3-a456-************',
        required: false,
    })
    @IsUUID()
    @IsOptional()
    templateId?: string;

    @ApiProperty({
        description: 'Recipient email addresses',
        example: ['<EMAIL>', '<EMAIL>'],
    })
    @IsArray()
    @IsEmail({}, { each: true })
    recipients: string[];

    @ApiProperty({
        description: 'Email subject (required if not using template)',
        example: 'Important notification',
        required: false,
    })
    @IsString()
    @IsOptional()
    @MaxLength(500)
    subject?: string;

    @ApiProperty({
        description: 'HTML content (required if not using template)',
        example: '<h1>Hello!</h1><p>This is an important message.</p>',
        required: false,
    })
    @IsString()
    @IsOptional()
    htmlContent?: string;

    @ApiProperty({
        description: 'Text content (optional)',
        example: 'Hello! This is an important message.',
        required: false,
    })
    @IsString()
    @IsOptional()
    textContent?: string;

    @ApiProperty({
        description: 'Variables to replace in template',
        example: { name: 'John Doe', company: 'Acme Corp' },
        required: false,
    })
    @IsObject()
    @IsOptional()
    variables?: Record<string, any>;

    @ApiProperty({
        description: 'Campaign ID (if part of a campaign)',
        example: '123e4567-e89b-12d3-a456-************',
        required: false,
    })
    @IsUUID()
    @IsOptional()
    campaignId?: string;
} 