import { ApiProperty } from '@nestjs/swagger';
import { EmailTemplateResponseDto } from './email-template-response.dto.js';

export class EmailCampaignResponseDto {
    @ApiProperty({ description: 'Campaign ID', example: '123e4567-e89b-12d3-a456-426614174000' })
    id: string;

    @ApiProperty({ description: 'Campaign name', example: 'Holiday Sale 2024' })
    name: string;

    @ApiProperty({ description: 'Email template ID', example: '123e4567-e89b-12d3-a456-426614174000' })
    templateId: string;

    @ApiProperty({ description: 'Associated email template', required: false })
    template?: EmailTemplateResponseDto;

    @ApiProperty({
        description: 'Target audience',
        example: 'all',
        enum: ['all', 'admin', 'user', 'specific'],
    })
    targetAudience: string;

    @ApiProperty({
        description: 'Recipient criteria for filtering',
        example: { roles: ['user'], createdAfter: '2024-01-01' },
        required: false,
    })
    recipientCriteria?: Record<string, any>;

    @ApiProperty({
        description: 'Campaign status',
        example: 'draft',
        enum: ['draft', 'scheduled', 'sending', 'completed', 'failed'],
    })
    status: string;

    @ApiProperty({
        description: 'Scheduled timestamp',
        example: '2024-12-25T10:00:00Z',
        required: false,
    })
    scheduledAt?: Date;

    @ApiProperty({
        description: 'Started timestamp',
        example: '2024-12-25T10:00:00Z',
        required: false,
    })
    startedAt?: Date;

    @ApiProperty({
        description: 'Completed timestamp',
        example: '2024-12-25T10:30:00Z',
        required: false,
    })
    completedAt?: Date;

    @ApiProperty({ description: 'Total recipients', example: 1000 })
    totalRecipients: number;

    @ApiProperty({ description: 'Successfully sent count', example: 995 })
    sentCount: number;

    @ApiProperty({ description: 'Failed sends count', example: 5 })
    failedCount: number;

    @ApiProperty({ description: 'Creation timestamp', example: '2024-01-01T00:00:00Z' })
    createdAt: Date;

    @ApiProperty({ description: 'Last update timestamp', example: '2024-01-01T00:00:00Z' })
    updatedAt: Date;
} 