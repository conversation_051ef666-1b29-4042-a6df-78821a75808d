import { ApiProperty } from '@nestjs/swagger';

export class EmailTemplateResponseDto {
    @ApiProperty({ description: 'Template ID', example: '123e4567-e89b-12d3-a456-426614174000' })
    id: string;

    @ApiProperty({ description: 'Template name', example: 'welcome-email' })
    name: string;

    @ApiProperty({ description: 'Email subject', example: 'Welcome to our platform!' })
    subject: string;

    @ApiProperty({
        description: 'HTML content of the email template',
        example: '<h1>Welcome {{name}}!</h1><p>Thank you for joining us.</p>',
    })
    htmlContent: string;

    @ApiProperty({
        description: 'Plain text content of the email template',
        example: 'Welcome {{name}}! Thank you for joining us.',
        required: false,
    })
    textContent?: string;

    @ApiProperty({
        description: 'Available variables for template',
        example: ['name', 'email', 'company'],
        required: false,
    })
    variables?: string[];

    @ApiProperty({
        description: 'Template category',
        example: 'auth',
        required: false,
    })
    category?: string;

    @ApiProperty({ description: 'Is template active', example: true })
    isActive: boolean;

    @ApiProperty({ description: 'Creation timestamp', example: '2024-01-01T00:00:00Z' })
    createdAt: Date;

    @ApiProperty({ description: 'Last update timestamp', example: '2024-01-01T00:00:00Z' })
    updatedAt: Date;
} 