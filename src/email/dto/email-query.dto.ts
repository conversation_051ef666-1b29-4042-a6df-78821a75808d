import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsIn, IsOptional, IsString, IsUUID } from 'class-validator';
import { Pagination } from '../../users/dto/pagination.dto.js';

export class EmailTemplateQueryDto extends Pagination {
    @ApiProperty({
        description: 'Filter by template category',
        example: 'auth',
        required: false,
    })
    @IsString()
    @IsOptional()
    category?: string;

    @ApiProperty({
        description: 'Filter by active status',
        example: 'true',
        enum: ['true', 'false'],
        required: false,
    })
    @IsString()
    @IsOptional()
    @IsIn(['true', 'false'])
    isActive?: string;

    @ApiProperty({
        description: 'Search in template name or subject',
        example: 'welcome',
        required: false,
    })
    @IsString()
    @IsOptional()
    search?: string;
}

export class EmailCampaignQueryDto extends Pagination {
    @ApiProperty({
        description: 'Filter by campaign status',
        example: 'completed',
        enum: ['draft', 'scheduled', 'sending', 'completed', 'failed'],
        required: false,
    })
    @IsString()
    @IsOptional()
    @IsIn(['draft', 'scheduled', 'sending', 'completed', 'failed'])
    status?: string;

    @ApiProperty({
        description: 'Filter by target audience',
        example: 'all',
        enum: ['all', 'admin', 'user', 'specific'],
        required: false,
    })
    @IsString()
    @IsOptional()
    @IsIn(['all', 'admin', 'user', 'specific'])
    targetAudience?: string;

    @ApiProperty({
        description: 'Search in campaign name',
        example: 'holiday',
        required: false,
    })
    @IsString()
    @IsOptional()
    search?: string;
}

export class EmailLogQueryDto extends Pagination {
    @ApiProperty({
        description: 'Filter by email status',
        example: 'sent',
        enum: ['sent', 'failed', 'queued'],
        required: false,
    })
    @IsString()
    @IsOptional()
    @IsIn(['sent', 'failed', 'queued'])
    status?: string;

    @ApiProperty({
        description: 'Filter by template ID',
        example: '123e4567-e89b-12d3-a456-************',
        required: false,
    })
    @IsUUID()
    @IsOptional()
    templateId?: string;

    @ApiProperty({
        description: 'Filter by campaign ID',
        example: '123e4567-e89b-12d3-a456-************',
        required: false,
    })
    @IsUUID()
    @IsOptional()
    campaignId?: string;

    @ApiProperty({
        description: 'Filter by recipient email',
        example: '<EMAIL>',
        required: false,
    })
    @IsString()
    @IsOptional()
    recipientEmail?: string;

    @ApiProperty({
        description: 'Filter emails sent after this date',
        example: '2024-01-01T00:00:00Z',
        required: false,
    })
    @IsDateString()
    @IsOptional()
    sentAfter?: string;

    @ApiProperty({
        description: 'Filter emails sent before this date',
        example: '2024-12-31T23:59:59Z',
        required: false,
    })
    @IsDateString()
    @IsOptional()
    sentBefore?: string;
} 