import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

export class CreateEmailTemplateDto {
    @ApiProperty({ description: 'Template name', example: 'welcome-email' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(255)
    name: string;

    @ApiProperty({ description: 'Email subject', example: 'Welcome to our platform!' })
    @IsString()
    @IsNotEmpty()
    @MaxLength(500)
    subject: string;

    @ApiProperty({
        description: 'HTML content of the email template',
        example: '<h1>Welcome {{name}}!</h1><p>Thank you for joining us.</p>',
    })
    @IsString()
    @IsNotEmpty()
    htmlContent: string;

    @ApiProperty({
        description: 'Plain text content of the email template',
        example: 'Welcome {{name}}! Thank you for joining us.',
        required: false,
    })
    @IsString()
    @IsOptional()
    textContent?: string;

    @ApiProperty({
        description: 'Available variables for template',
        example: ['name', 'email', 'company'],
        required: false,
    })
    @IsArray()
    @IsString({ each: true })
    @IsOptional()
    variables?: string[];

    @ApiProperty({
        description: 'Template category',
        example: 'auth',
        enum: ['auth', 'notification', 'marketing', 'system'],
        required: false,
    })
    @IsString()
    @IsOptional()
    @MaxLength(100)
    category?: string;

    @ApiProperty({ description: 'Is template active', example: true, required: false })
    @IsBoolean()
    @IsOptional()
    isActive?: boolean;
} 