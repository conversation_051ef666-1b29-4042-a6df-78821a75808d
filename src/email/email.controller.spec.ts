import { Test, TestingModule } from '@nestjs/testing';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { SendTestEmailDto } from './dto/send-test-email.dto';
import { EmailController } from './email.controller';
import { EmailService } from './email.service';

describe('EmailController', () => {
  let controller: EmailController;
  let emailService: jest.Mocked<EmailService>;

  const mockEmailService = {
    sendTestEmail: jest.fn(),
    sendWelcomeEmail: jest.fn(),
    sendPasswordReset: jest.fn(),
    send2FAEnabled: jest.fn(),
    send2FADisabled: jest.fn(),
    sendNewDeviceLogin: jest.fn(),
  };

  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    password: 'hashed-password',
    name: 'Test User',
    bio: null,
    roles: ['user'],
    isActive: true,
    emailVerified: true,
    emailVerificationToken: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    twoFactorSecret: null,
    twoFactorEnabled: false,
    lastLoginAt: null,
    lastLoginIp: null,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmailController],
      providers: [
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<EmailController>(EmailController);
    emailService = module.get(EmailService);

    // Reset mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('sendTestEmail', () => {
    it('should send test email successfully with user email', async () => {
      const dto: SendTestEmailDto = {};
      emailService.sendTestEmail.mockResolvedValue(true);

      const result = await controller.sendTestEmail(mockUser, dto);

      expect(result).toEqual({
        success: true,
        message: 'Test email sent successfully',
      });
      expect(emailService.sendTestEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        undefined,
        undefined,
        undefined
      );
    });

    it('should send test email successfully with custom email', async () => {
      const dto: SendTestEmailDto = {
        to: '<EMAIL>',
        subject: 'Custom Subject',
        template: 'custom-template',
        context: { customVar: 'value' },
      };
      emailService.sendTestEmail.mockResolvedValue(true);

      const result = await controller.sendTestEmail(mockUser, dto);

      expect(result).toEqual({
        success: true,
        message: 'Test email sent successfully',
      });
      expect(emailService.sendTestEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Custom Subject',
        'custom-template',
        { customVar: 'value' }
      );
    });

    it('should handle test email sending failure', async () => {
      const dto: SendTestEmailDto = {};
      emailService.sendTestEmail.mockResolvedValue(false);

      const result = await controller.sendTestEmail(mockUser, dto);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send test email. Check server logs for details.',
      });
    });

    it('should use user email when dto.to is not provided', async () => {
      const dto: SendTestEmailDto = {
        subject: 'Test Subject',
      };
      emailService.sendTestEmail.mockResolvedValue(true);

      await controller.sendTestEmail(mockUser, dto);

      expect(emailService.sendTestEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test Subject',
        undefined,
        undefined
      );
    });
  });

  describe('sendTestWelcomeEmail', () => {
    it('should send test welcome email successfully with user email', async () => {
      const dto = {};
      emailService.sendWelcomeEmail.mockResolvedValue(true);

      const result = await controller.sendTestWelcomeEmail(mockUser, dto);

      expect(result).toEqual({
        success: true,
        message: 'Test welcome email sent successfully',
      });
      expect(emailService.sendWelcomeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test User',
        'test-verification-token-123'
      );
    });

    it('should send test welcome email with custom email', async () => {
      const dto = { to: '<EMAIL>' };
      emailService.sendWelcomeEmail.mockResolvedValue(true);

      const result = await controller.sendTestWelcomeEmail(mockUser, dto);

      expect(result).toEqual({
        success: true,
        message: 'Test welcome email sent successfully',
      });
      expect(emailService.sendWelcomeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test User',
        'test-verification-token-123'
      );
    });

    it('should handle welcome email sending failure', async () => {
      const dto = {};
      emailService.sendWelcomeEmail.mockResolvedValue(false);

      const result = await controller.sendTestWelcomeEmail(mockUser, dto);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send test welcome email. Check server logs for details.',
      });
    });

    it('should use default name when user.name is not provided', async () => {
      const userWithoutName = { ...mockUser, name: null };
      const dto = {};
      emailService.sendWelcomeEmail.mockResolvedValue(true);

      await controller.sendTestWelcomeEmail(userWithoutName, dto);

      expect(emailService.sendWelcomeEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test User',
        'test-verification-token-123'
      );
    });
  });

  describe('sendTestPasswordResetEmail', () => {
    it('should send test password reset email successfully', async () => {
      const dto = {};
      emailService.sendPasswordReset.mockResolvedValue(true);

      const result = await controller.sendTestPasswordResetEmail(mockUser, dto);

      expect(result).toEqual({
        success: true,
        message: 'Test password reset email sent successfully',
      });
      expect(emailService.sendPasswordReset).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test User',
        'test-reset-token-123'
      );
    });

    it('should send test password reset email with custom email', async () => {
      const dto = { to: '<EMAIL>' };
      emailService.sendPasswordReset.mockResolvedValue(true);

      const result = await controller.sendTestPasswordResetEmail(mockUser, dto);

      expect(result).toEqual({
        success: true,
        message: 'Test password reset email sent successfully',
      });
      expect(emailService.sendPasswordReset).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test User',
        'test-reset-token-123'
      );
    });

    it('should handle password reset email sending failure', async () => {
      const dto = {};
      emailService.sendPasswordReset.mockResolvedValue(false);

      const result = await controller.sendTestPasswordResetEmail(mockUser, dto);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send test password reset email. Check server logs for details.',
      });
    });
  });

  describe('sendTest2FAEnabledEmail', () => {
    it('should send test 2FA enabled email successfully', async () => {
      const dto = {};
      emailService.send2FAEnabled.mockResolvedValue(true);

      const result = await controller.sendTest2FAEnabledEmail(mockUser, dto);

      expect(result).toEqual({
        success: true,
        message: 'Test 2FA enabled email sent successfully',
      });
      expect(emailService.send2FAEnabled).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test User',
        expect.arrayContaining([
          expect.any(String),
          expect.any(String),
          expect.any(String),
        ])
      );
    });

    it('should handle 2FA enabled email sending failure', async () => {
      const dto = {};
      emailService.send2FAEnabled.mockResolvedValue(false);

      const result = await controller.sendTest2FAEnabledEmail(mockUser, dto);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send test 2FA enabled email. Check server logs for details.',
      });
    });
  });

  describe('sendTestNewDeviceEmail', () => {
    it('should send test new device email successfully', async () => {
      const dto = {};
      emailService.sendNewDeviceLogin.mockResolvedValue(true);

      const result = await controller.sendTestNewDeviceEmail(mockUser, dto);

      expect(result).toEqual({
        success: true,
        message: 'Test new device email sent successfully',
      });
      expect(emailService.sendNewDeviceLogin).toHaveBeenCalledWith(
        '<EMAIL>',
        'Test User',
        expect.objectContaining({
          userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          platform: 'macOS',
          browser: 'Chrome',
          ipAddress: '*************',
        }),
        'San Francisco, CA, USA'
      );
    });

    it('should handle new device email sending failure', async () => {
      const dto = {};
      emailService.sendNewDeviceLogin.mockResolvedValue(false);

      const result = await controller.sendTestNewDeviceEmail(mockUser, dto);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send test new device email. Check server logs for details.',
      });
    });
  });
});
