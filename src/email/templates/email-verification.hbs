<!-- Hero Section with Orange Gradient -->
<div style="background: linear-gradient(135deg, #ff6b35, #f7931e); padding: 48px 32px; border-radius: 20px; text-align: center; margin-bottom: 40px; box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);">
    <div style="background: rgba(255, 255, 255, 0.15); width: 96px; height: 96px; border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px); border: 2px solid rgba(255, 255, 255, 0.2);">
        <span style="font-size: 48px;">📧</span>
    </div>
    <h1 style="color: #ffffff; margin: 0 0 16px 0; font-size: 36px; font-weight: 800; text-shadow: 0 2px 8px rgba(0,0,0,0.2); letter-spacing: -0.5px;">Verify Your Email Address</h1>
    <p style="color: rgba(255, 255, 255, 0.95); margin: 0; font-size: 20px; font-weight: 500; text-shadow: 0 1px 4px rgba(0,0,0,0.1);">Hi {{#if name}}{{name}}{{else}}there{{/if}}, let's get your RSGlider account verified!</p>
</div>

<!-- Welcome Message -->
<div style="background: linear-gradient(135deg, #ecfdf5, #f0fdf4); border: 2px solid #10b981; border-radius: 16px; padding: 32px; margin-bottom: 40px; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #10b981, #059669, #047857);"></div>
    <div style="display: flex; align-items: center; gap: 20px;">
        <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);">✉️</div>
        <div>
            <h2 style="color: #065f46; margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">Almost There!</h2>
            <p style="color: #047857; margin: 0; font-size: 18px; font-weight: 500;">Please verify your email address to complete your RSGlider account setup and unlock all features</p>
        </div>
    </div>
</div>

<!-- Beautiful Orange CTA Button -->
<div style="text-align: center; margin: 48px 0;">
    <a href="{{verificationUrl}}" style="display: inline-block; background: linear-gradient(135deg, #ff6b35, #f7931e); color: #ffffff !important; padding: 20px 40px; border-radius: 16px; text-decoration: none; font-weight: 700; font-size: 18px; border: none; cursor: pointer; box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4); transition: transform 0.2s; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
        ✅ Verify Email Address
    </a>
    <p style="margin: 16px 0 0 0; color: #6b7280; font-size: 14px;">Click to activate your RSGlider account</p>
</div>

<!-- Alternative Link Section -->
<div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 32px; margin: 40px 0; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
    <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 20px;">
        <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 20px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);">🔗</div>
        <div>
            <h3 style="color: #1f2937; margin: 0 0 4px 0; font-size: 18px; font-weight: 700;">Alternative Method</h3>
            <p style="color: #6b7280; margin: 0; font-size: 14px;">If the button doesn't work, copy and paste this link:</p>
        </div>
    </div>
    <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 12px; padding: 20px; font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 14px; word-break: break-all; color: #1f2937; box-shadow: 0 2px 8px rgba(0,0,0,0.04);">{{verificationUrl}}</div>
</div>

<!-- Security Notice -->
<div style="background: linear-gradient(135deg, #fef3c7, #fde68a); border: 2px solid #f59e0b; border-radius: 16px; padding: 32px; margin: 40px 0; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #f59e0b, #d97706, #b45309);"></div>
    <div style="display: flex; align-items: center; gap: 20px;">
        <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);">🔒</div>
        <div>
            <h3 style="color: #92400e; margin: 0 0 8px 0; font-size: 20px; font-weight: 700;">Security Notice</h3>
            <p style="color: #b45309; margin: 0; font-size: 16px; font-weight: 500;">This verification link will expire in 24 hours. If you didn't request this verification, please ignore this email and your account will remain unverified.</p>
        </div>
    </div>
</div>

<!-- Help Section -->
<div style="background: linear-gradient(135deg, #eff6ff, #dbeafe); border: 2px solid #3b82f6; border-radius: 16px; padding: 32px; margin: 40px 0; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);"></div>
    <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
        <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);">🤝</div>
        <div>
            <h3 style="color: #1e40af; margin: 0 0 8px 0; font-size: 20px; font-weight: 700;">Need Help?</h3>
            <p style="color: #2563eb; margin: 0; font-size: 16px; font-weight: 500;">Our support team is here to help you get started</p>
        </div>
    </div>
    <div style="background: #ffffff; border: 1px solid #bfdbfe; border-radius: 12px; padding: 20px; text-align: center;">
        <div style="font-size: 24px; margin-bottom: 8px;">📧</div>
        <strong style="color: #1e40af; font-size: 16px;">Contact Support</strong>
        <p style="margin: 8px 0 0 0; font-size: 14px;"><a href="mailto:{{supportEmail}}" style="color: #2563eb; text-decoration: none; font-weight: 600;">{{supportEmail}}</a></p>
    </div>
</div>

<!-- Welcome Footer -->
<div style="text-align: center; margin: 48px 0; padding: 40px; background: linear-gradient(135deg, #f8fafc, #f1f5f9); border: 2px solid #e2e8f0; border-radius: 20px; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #ff6b35, #f7931e, #e97317);"></div>
    <div style="background: rgba(255, 107, 53, 0.1); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; border: 2px solid rgba(255, 107, 53, 0.2);">
        <span style="font-size: 36px;">🎉</span>
    </div>
    <h3 style="margin: 0 0 12px 0; font-size: 24px; color: #1f2937; font-weight: 700;">Welcome to RSGlider!</h3>
    <p style="margin: 0 0 24px 0; color: #6b7280; font-size: 18px; font-weight: 500;">We're excited to have you join our automation community.</p>
    <div style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; display: inline-block;">
        <p style="margin: 0 0 8px 0; font-size: 18px; color: #1f2937; font-weight: 600;">Best regards,</p>
        <p style="margin: 0; color: #6b7280; font-size: 16px;"><strong>The {{siteName}} Team</strong></p>
    </div>
</div>
