<h2>Two-Factor Authentication Disabled</h2>

<p>Hi {{#if name}}{{name}}{{else}}there{{/if}},</p>

<p>This is to notify you that two-factor authentication (2FA) has been disabled on your {{siteName}} account.</p>

<div class="alert alert-warning">
    <strong>Security Notice:</strong> Your account is now less secure without two-factor authentication enabled.
</div>

<h3>Disable Details</h3>
<ul>
    <li><strong>Date & Time:</strong> {{disabledTime}}</li>
    <li><strong>Device:</strong> {{deviceInfo}}</li>
</ul>

<div class="alert alert-warning">
    <strong>Didn't make this change?</strong><br>
    If you didn't disable two-factor authentication, your account may have been compromised. Please contact our support team immediately at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a> and consider:
    <ul style="margin: 8px 0 0 0; padding-left: 20px;">
        <li>Changing your password immediately</li>
        <li>Re-enabling two-factor authentication</li>
        <li>Reviewing your recent account activity</li>
    </ul>
</div>

<h3>Re-enable Two-Factor Authentication</h3>
<p>We strongly recommend re-enabling two-factor authentication to keep your account secure. You can do this by:</p>
<ol>
    <li>Going to your account security settings</li>
    <li>Selecting "Enable Two-Factor Authentication"</li>
    <li>Following the setup instructions</li>
</ol>

<div style="text-align: center; margin: 30px 0;">
    {{> button url=baseUrl text="Go to Security Settings"}}
</div>

<h3>Why Two-Factor Authentication Matters</h3>
<ul>
    <li>🔒 Protects against password breaches</li>
    <li>🛡️ Prevents unauthorized access</li>
    <li>✅ Industry-standard security practice</li>
    <li>📱 Easy to use with authenticator apps</li>
</ul>

<p>If you have any questions about account security or need help setting up 2FA again, please contact us at <a href="mailto:{{supportEmail}}">{{supportEmail}}</a>.</p>

<p>Best regards,<br>
<strong>The {{siteName}} Security Team</strong></p>
