<!-- Hero Section with Security Gradient -->
<div style="background: linear-gradient(135deg, #10b981, #059669); padding: 48px 32px; border-radius: 20px; text-align: center; margin-bottom: 40px; box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);">
    <div style="background: rgba(255, 255, 255, 0.15); width: 96px; height: 96px; border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px); border: 2px solid rgba(255, 255, 255, 0.2);">
        <span style="font-size: 48px;">🔐</span>
    </div>
    <h1 style="color: #ffffff; margin: 0 0 16px 0; font-size: 36px; font-weight: 800; text-shadow: 0 2px 8px rgba(0,0,0,0.2); letter-spacing: -0.5px;">Password Changed Successfully</h1>
    <p style="color: rgba(255, 255, 255, 0.95); margin: 0; font-size: 20px; font-weight: 500; text-shadow: 0 1px 4px rgba(0,0,0,0.1);">{{#if name}}{{name}}{{else}}User{{/if}}, your {{siteName}} account is now secured with your new password</p>
</div>

<!-- Success Banner with Modern Design -->
<div style="background: linear-gradient(135deg, #ecfdf5, #f0fdf4); border: 2px solid #10b981; border-radius: 16px; padding: 32px; margin-bottom: 40px; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #10b981, #059669, #047857);"></div>
    <div style="display: flex; align-items: center; gap: 20px;">
        <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);">✓</div>
        <div>
            <h2 style="color: #065f46; margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">Password Updated</h2>
            <p style="color: #047857; margin: 0; font-size: 18px; font-weight: 500;">Your account is now secured with your new password</p>
        </div>
    </div>
</div>

<!-- Change Details Card -->
<div style="background: linear-gradient(135deg, #eff6ff, #dbeafe); border: 2px solid #3b82f6; border-radius: 16px; padding: 32px; margin: 40px 0; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);"></div>
    <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);">📋</div>
        <div>
            <h3 style="color: #1e40af; margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">Change Details</h3>
            <p style="color: #2563eb; margin: 0; font-size: 16px; font-weight: 500;">Security information about this password change</p>
        </div>
    </div>
    <div style="background: #ffffff; border: 1px solid #bfdbfe; padding: 24px; border-radius: 12px; font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 15px; color: #1f2937; box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);">
        <div style="margin-bottom: 12px;"><strong style="color: #3b82f6;">Date & Time:</strong> <span style="color: #1f2937;">{{changeTime}}</span></div>
        <div style="margin-bottom: 12px;"><strong style="color: #3b82f6;">Device:</strong> <span style="color: #1f2937;">{{deviceInfo}}</span></div>
        <div><strong style="color: #3b82f6;">IP Address:</strong> <span style="color: #1f2937;">{{ipAddress}}</span></div>
    </div>
</div>

<!-- Security Alert Section -->
<div style="background: linear-gradient(135deg, #fef3c7, #fde68a); border: 2px solid #f59e0b; border-radius: 16px; padding: 32px; margin: 48px 0; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #f59e0b, #d97706, #b45309);"></div>
    <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);">⚠️</div>
        <div>
            <h3 style="color: #92400e; margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">Didn't make this change?</h3>
            <p style="color: #b45309; margin: 0; font-size: 16px; font-weight: 500;">If you didn't change your password, your account may have been compromised</p>
        </div>
    </div>
    <div style="background: #ffffff; border: 1px solid #f59e0b; border-radius: 12px; padding: 20px; text-align: center;">
        <p style="margin: 0 0 16px 0; color: #92400e; font-size: 16px; font-weight: 600;">Contact our support team immediately</p>
        <a href="mailto:{{supportEmail}}" style="display: inline-block; background: linear-gradient(135deg, #f59e0b, #d97706); color: #ffffff !important; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; font-size: 16px;">{{supportEmail}}</a>
    </div>
</div>

<!-- Security Recommendations -->
<div style="margin: 48px 0;">
    <h2 style="color: #1f2937; margin: 0 0 32px 0; font-size: 28px; font-weight: 700; text-align: center;">Security Recommendations</h2>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 32px;">
        <!-- Password Security Card -->
        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 28px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #10b981, #059669);"></div>
            <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; width: 56px; height: 56px; border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 20px; box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);">🔒</div>
            <h3 style="color: #1f2937; margin: 0 0 12px 0; font-size: 18px; font-weight: 700;">Unique Password</h3>
            <p style="color: #6b7280; margin: 0; font-size: 15px; line-height: 1.6;">Use a unique password for your {{siteName}} account that you don't use anywhere else</p>
        </div>

        <!-- 2FA Card -->
        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 28px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #3b82f6, #2563eb);"></div>
            <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; width: 56px; height: 56px; border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 20px; box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);">🛡️</div>
            <h3 style="color: #1f2937; margin: 0 0 12px 0; font-size: 18px; font-weight: 700;">Two-Factor Authentication</h3>
            <p style="color: #6b7280; margin: 0; font-size: 15px; line-height: 1.6;">Enable 2FA for an extra layer of security on your account</p>
        </div>

        <!-- Activity Monitoring Card -->
        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 28px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #8b5cf6, #7c3aed);"></div>
            <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; width: 56px; height: 56px; border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 20px; box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);">👁️</div>
            <h3 style="color: #1f2937; margin: 0 0 12px 0; font-size: 18px; font-weight: 700;">Monitor Activity</h3>
            <p style="color: #6b7280; margin: 0; font-size: 15px; line-height: 1.6;">Regularly review your account activity and login history</p>
        </div>

        <!-- Recovery Info Card -->
        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 28px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #f59e0b, #d97706);"></div>
            <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; width: 56px; height: 56px; border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 20px; box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);">📱</div>
            <h3 style="color: #1f2937; margin: 0 0 12px 0; font-size: 18px; font-weight: 700;">Recovery Information</h3>
            <p style="color: #6b7280; margin: 0; font-size: 15px; line-height: 1.6;">Keep your recovery information up to date for account security</p>
        </div>
    </div>
</div>

<!-- Support Section -->
<div style="text-align: center; margin: 48px 0; padding: 40px; background: linear-gradient(135deg, #f8fafc, #f1f5f9); border: 2px solid #e2e8f0; border-radius: 20px; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #10b981, #059669, #047857);"></div>
    <div style="background: rgba(16, 185, 129, 0.1); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; border: 2px solid rgba(16, 185, 129, 0.2);">
        <span style="font-size: 36px;">🔐</span>
    </div>
    <h3 style="margin: 0 0 12px 0; font-size: 24px; color: #1f2937; font-weight: 700;">Questions about account security?</h3>
    <p style="margin: 0 0 24px 0; color: #6b7280; font-size: 18px; font-weight: 500;">Don't hesitate to contact us at <a href="mailto:{{supportEmail}}" style="color: #10b981; text-decoration: none; font-weight: 600;">{{supportEmail}}</a></p>
    <div style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; display: inline-block;">
        <p style="margin: 0 0 8px 0; font-size: 18px; color: #1f2937; font-weight: 600;">Best regards,</p>
        <p style="margin: 0; color: #6b7280; font-size: 16px;"><strong>The {{siteName}} Security Team</strong></p>
    </div>
</div>
