<!-- Hero Section with Orange Gradient -->
<div style="background: linear-gradient(135deg, #ff6b35, #f7931e); padding: 48px 32px; border-radius: 20px; text-align: center; margin-bottom: 40px; box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);">
    <div style="background: rgba(255, 255, 255, 0.15); width: 96px; height: 96px; border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px); border: 2px solid rgba(255, 255, 255, 0.2);">
        <span style="font-size: 48px;">🚀</span>
    </div>
    <h1 style="color: #ffffff; margin: 0 0 16px 0; font-size: 36px; font-weight: 800; text-shadow: 0 2px 8px rgba(0,0,0,0.2); letter-spacing: -0.5px;">Welcome to the Developer Team!</h1>
    <p style="color: rgba(255, 255, 255, 0.95); margin: 0; font-size: 20px; font-weight: 500; text-shadow: 0 1px 4px rgba(0,0,0,0.1);">{{#if name}}{{name}}{{else}}Developer{{/if}}, you've been promoted to Developer status on RSGlider</p>
</div>

<!-- Success Banner with Modern Design -->
<div style="background: linear-gradient(135deg, #ecfdf5, #f0fdf4); border: 2px solid #10b981; border-radius: 16px; padding: 32px; margin-bottom: 40px; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #10b981, #059669, #047857);"></div>
    <div style="display: flex; align-items: center; gap: 20px;">
        <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);">✓</div>
        <div>
            <h2 style="color: #065f46; margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">Developer Status Activated</h2>
            <p style="color: #047857; margin: 0; font-size: 18px; font-weight: 500;">Your account now has full developer privileges and development environment access</p>
        </div>
    </div>
</div>

<!-- Developer Abilities Section with Beautiful Cards -->
<div style="margin: 48px 0;">
    <h2 style="color: #1f2937; margin: 0 0 32px 0; font-size: 28px; font-weight: 700; text-align: center;">Your New Developer Superpowers</h2>

    <!-- Abilities Grid with Modern Cards -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 32px;">
        <!-- Repository Management Card -->
        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 28px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); transition: transform 0.2s; position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #3b82f6, #2563eb);"></div>
            <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; width: 56px; height: 56px; border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 20px; box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);">🛠️</div>
            <h3 style="color: #1f2937; margin: 0 0 12px 0; font-size: 18px; font-weight: 700;">Repository Management</h3>
            <p style="color: #6b7280; margin: 0; font-size: 15px; line-height: 1.6;">Create and manage automation scripts with full version control and collaboration tools</p>
        </div>

        <!-- Marketplace Publishing Card -->
        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 28px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); transition: transform 0.2s; position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #10b981, #059669);"></div>
            <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; width: 56px; height: 56px; border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 20px; box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);">📦</div>
            <h3 style="color: #1f2937; margin: 0 0 12px 0; font-size: 18px; font-weight: 700;">Marketplace Publishing</h3>
            <p style="color: #6b7280; margin: 0; font-size: 15px; line-height: 1.6;">Share your scripts with the RSGlider community and build your reputation</p>
        </div>

        <!-- Revenue Generation Card -->
        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 28px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); transition: transform 0.2s; position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #f59e0b, #d97706);"></div>
            <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; width: 56px; height: 56px; border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 20px; box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);">💰</div>
            <h3 style="color: #1f2937; margin: 0 0 12px 0; font-size: 18px; font-weight: 700;">Revenue Generation</h3>
            <p style="color: #6b7280; margin: 0; font-size: 15px; line-height: 1.6;">Earn money from popular scripts, subscriptions, and community contributions</p>
        </div>

        <!-- Team Collaboration Card -->
        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 28px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); transition: transform 0.2s; position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 4px; background: linear-gradient(90deg, #8b5cf6, #7c3aed);"></div>
            <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; width: 56px; height: 56px; border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 20px; box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);">👥</div>
            <h3 style="color: #1f2937; margin: 0 0 12px 0; font-size: 18px; font-weight: 700;">Team Collaboration</h3>
            <p style="color: #6b7280; margin: 0; font-size: 15px; line-height: 1.6;">Work with other developers, share knowledge, and build amazing projects together</p>
        </div>
    </div>
</div>

<!-- Beautiful Orange CTA Button -->
<div style="text-align: center; margin: 48px 0;">
    <a href="{{giteaUrl}}" style="display: inline-block; background: linear-gradient(135deg, #ff6b35, #f7931e); color: #ffffff !important; padding: 20px 40px; border-radius: 16px; text-decoration: none; font-weight: 700; font-size: 18px; border: none; cursor: pointer; box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4); transition: transform 0.2s; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
        🚀 Access Your Development Environment
    </a>
    <p style="margin: 16px 0 0 0; color: #6b7280; font-size: 14px;">Click to start building amazing automation tools</p>
</div>

<!-- Development Access Card -->
<div style="background: linear-gradient(135deg, #eff6ff, #dbeafe); border: 2px solid #3b82f6; border-radius: 16px; padding: 32px; margin: 40px 0; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #3b82f6, #2563eb, #1d4ed8);"></div>
    <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);">🔐</div>
        <div>
            <h3 style="color: #1e40af; margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">Your Development Access</h3>
            <p style="color: #2563eb; margin: 0; font-size: 16px; font-weight: 500;">Secure, seamless access to your development environment</p>
        </div>
    </div>
    <div style="background: #ffffff; border: 1px solid #bfdbfe; padding: 24px; border-radius: 12px; font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace; font-size: 15px; color: #1f2937; box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);">
        <div style="margin-bottom: 12px;"><strong style="color: #3b82f6;">Username:</strong> <span style="color: #1f2937;">{{giteaUsername}}</span></div>
        <div style="margin-bottom: 12px;"><strong style="color: #3b82f6;">Access:</strong> <span style="color: #1f2937;">Automatic SSO login</span></div>
        <div><strong style="color: #3b82f6;">Security:</strong> <span style="color: #1f2937;">Synced with RSGlider account</span></div>
    </div>
    <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid #bfdbfe; border-radius: 12px; padding: 20px; margin-top: 20px;">
        <p style="margin: 0; color: #1e40af; font-size: 15px; font-weight: 500;">
            <strong>🔒 Secure Access:</strong> Your development environment is automatically synced with your RSGlider account. Use your RSGlider credentials - no separate password needed!
        </p>
    </div>
</div>

<!-- Next Steps Section -->
<div style="margin: 48px 0;">
    <h2 style="color: #1f2937; margin: 0 0 32px 0; font-size: 28px; font-weight: 700; text-align: center;">Your Developer Journey Starts Here</h2>

    <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; padding: 32px; box-shadow: 0 4px 16px rgba(0,0,0,0.08);">
        <div style="display: grid; gap: 20px;">
            <div style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 12px;">
                <div style="background: linear-gradient(135deg, #ff6b35, #f7931e); color: white; width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; font-size: 16px; font-weight: 700; flex-shrink: 0;">1</div>
                <div>
                    <strong style="color: #1f2937; font-size: 16px;">Explore your development environment</strong>
                    <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">Familiarize yourself with the tools and interface</p>
                </div>
            </div>

            <div style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 12px;">
                <div style="background: linear-gradient(135deg, #ff6b35, #f7931e); color: white; width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; font-size: 16px; font-weight: 700; flex-shrink: 0;">2</div>
                <div>
                    <strong style="color: #1f2937; font-size: 16px;">Create your first repository</strong>
                    <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">Start building your first automation script</p>
                </div>
            </div>

            <div style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 12px;">
                <div style="background: linear-gradient(135deg, #ff6b35, #f7931e); color: white; width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; font-size: 16px; font-weight: 700; flex-shrink: 0;">3</div>
                <div>
                    <strong style="color: #1f2937; font-size: 16px;">Review marketplace guidelines</strong>
                    <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">Learn our publishing standards and best practices</p>
                </div>
            </div>

            <div style="display: flex; align-items: center; gap: 16px; padding: 16px; background: #f8fafc; border-radius: 12px;">
                <div style="background: linear-gradient(135deg, #ff6b35, #f7931e); color: white; width: 40px; height: 40px; border-radius: 10px; display: flex; align-items: center; justify-content: center; font-size: 16px; font-weight: 700; flex-shrink: 0;">4</div>
                <div>
                    <strong style="color: #1f2937; font-size: 16px;">Set up payouts</strong>
                    <p style="margin: 4px 0 0 0; color: #6b7280; font-size: 14px;">Configure your payment preferences to start earning</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Earning Opportunities Section -->
<div style="margin: 48px 0;">
    <h2 style="color: #1f2937; margin: 0 0 32px 0; font-size: 28px; font-weight: 700; text-align: center;">Start Earning Today</h2>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 32px; border-radius: 16px; text-align: center; box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3); position: relative; overflow: hidden;">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255, 255, 255, 0.1); border-radius: 50%;"></div>
            <div style="font-size: 48px; margin-bottom: 16px;">💵</div>
            <h3 style="margin: 0 0 8px 0; font-size: 20px; font-weight: 700;">Script Sales</h3>
            <p style="margin: 0; font-size: 16px; opacity: 0.9;">Direct marketplace revenue from your automation tools</p>
        </div>

        <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; padding: 32px; border-radius: 16px; text-align: center; box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3); position: relative; overflow: hidden;">
            <div style="position: absolute; top: -20px; right: -20px; width: 80px; height: 80px; background: rgba(255, 255, 255, 0.1); border-radius: 50%;"></div>
            <div style="font-size: 48px; margin-bottom: 16px;">🔄</div>
            <h3 style="margin: 0 0 8px 0; font-size: 20px; font-weight: 700;">Subscriptions</h3>
            <p style="margin: 0; font-size: 16px; opacity: 0.9;">Recurring revenue from premium automation services</p>
        </div>
    </div>
</div>

<!-- Help Section -->
<div style="background: linear-gradient(135deg, #fef3c7, #fde68a); border: 2px solid #f59e0b; border-radius: 16px; padding: 32px; margin: 48px 0; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #f59e0b, #d97706, #b45309);"></div>
    <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 24px;">
        <div style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; width: 64px; height: 64px; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 28px; flex-shrink: 0; box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3);">🤝</div>
        <div>
            <h3 style="color: #92400e; margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">Need Help?</h3>
            <p style="color: #b45309; margin: 0; font-size: 16px; font-weight: 500;">Our developer support team is here to help you succeed</p>
        </div>
    </div>
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
        <div style="background: #ffffff; border: 1px solid #f59e0b; border-radius: 12px; padding: 20px; text-align: center;">
            <div style="font-size: 24px; margin-bottom: 8px;">📧</div>
            <strong style="color: #92400e; font-size: 16px;">Email Support</strong>
            <p style="margin: 8px 0 0 0; font-size: 14px;"><a href="mailto:<EMAIL>" style="color: #b45309; text-decoration: none; font-weight: 600;"><EMAIL></a></p>
        </div>
        <div style="background: #ffffff; border: 1px solid #f59e0b; border-radius: 12px; padding: 20px; text-align: center;">
            <div style="font-size: 24px; margin-bottom: 8px;">📚</div>
            <strong style="color: #92400e; font-size: 16px;">Documentation</strong>
            <p style="margin: 8px 0 0 0; font-size: 14px;"><a href="{{baseUrl}}/docs" style="color: #b45309; text-decoration: none; font-weight: 600;">Developer Guides</a></p>
        </div>
    </div>
</div>

<!-- Welcome Footer -->
<div style="text-align: center; margin: 48px 0; padding: 40px; background: linear-gradient(135deg, #f8fafc, #f1f5f9); border: 2px solid #e2e8f0; border-radius: 20px; position: relative; overflow: hidden;">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 6px; background: linear-gradient(90deg, #ff6b35, #f7931e, #e97317);"></div>
    <div style="background: rgba(255, 107, 53, 0.1); width: 80px; height: 80px; border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center; border: 2px solid rgba(255, 107, 53, 0.2);">
        <span style="font-size: 36px;">🎉</span>
    </div>
    <h3 style="margin: 0 0 12px 0; font-size: 24px; color: #1f2937; font-weight: 700;">Welcome to the team!</h3>
    <p style="margin: 0 0 24px 0; color: #6b7280; font-size: 18px; font-weight: 500;">We're excited to see what amazing automation tools you'll create.</p>
    <div style="background: #ffffff; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; display: inline-block;">
        <p style="margin: 0 0 8px 0; font-size: 18px; color: #1f2937; font-weight: 600;">Happy coding! 🚀</p>
        <p style="margin: 0; color: #6b7280; font-size: 16px;"><strong>The RSGlider Developer Team</strong></p>
    </div>
</div>
