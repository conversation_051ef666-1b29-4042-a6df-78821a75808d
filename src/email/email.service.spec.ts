import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { EmailContext, EmailService, SendEmailOptions } from './email.service';

describe('EmailService', () => {
  let service: EmailService;
  let mailerService: jest.Mocked<MailerService>;
  let configService: jest.Mocked<ConfigService>;

  const mockMailerService = {
    sendMail: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EmailService,
        {
          provide: MailerService,
          useValue: mockMailerService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<EmailService>(EmailService);
    mailerService = module.get(MailerService);
    configService = module.get(ConfigService);

    // Reset mocks
    jest.clearAllMocks();

    // Setup default config values
    configService.get.mockImplementation((key: string, defaultValue?: any) => {
      const config: Record<string, any> = {
        EMAIL_BASE_URL: 'https://test.rsglider.com',
        EMAIL_FROM_NAME: 'RSGlider Test',
        SUPPORT_EMAIL: '<EMAIL>',
      };
      return config[key] || defaultValue;
    });
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendEmail', () => {
    const mockEmailOptions: SendEmailOptions = {
      to: '<EMAIL>',
      subject: 'Test Subject',
      template: 'test-template',
      context: { testVar: 'testValue' },
    };

    it('should send email successfully', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.sendEmail(mockEmailOptions);

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith({
        to: '<EMAIL>',
        subject: 'Test Subject',
        template: 'test-template',
        context: {
          testVar: 'testValue',
          baseUrl: 'https://test.rsglider.com',
          siteName: 'RSGlider Test',
          currentYear: new Date().getFullYear(),
          supportEmail: '<EMAIL>',
        },
        attachments: undefined,
        cc: undefined,
        bcc: undefined,
      });
    });

    it('should send email with multiple recipients', async () => {
      const multipleRecipientsOptions = {
        ...mockEmailOptions,
        to: ['<EMAIL>', '<EMAIL>'],
      };
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.sendEmail(multipleRecipientsOptions);

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: ['<EMAIL>', '<EMAIL>'],
        })
      );
    });

    it('should send email with cc and bcc', async () => {
      const emailWithCcBcc = {
        ...mockEmailOptions,
        cc: '<EMAIL>',
        bcc: ['<EMAIL>', '<EMAIL>'],
      };
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.sendEmail(emailWithCcBcc);

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          cc: '<EMAIL>',
          bcc: ['<EMAIL>', '<EMAIL>'],
        })
      );
    });

    it('should send email with attachments', async () => {
      const emailWithAttachments = {
        ...mockEmailOptions,
        attachments: [{ filename: 'test.pdf', content: 'test-content' }],
      };
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.sendEmail(emailWithAttachments);

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          attachments: [{ filename: 'test.pdf', content: 'test-content' }],
        })
      );
    });

    it('should handle email sending failure', async () => {
      const error = new Error('SMTP connection failed');
      mailerService.sendMail.mockRejectedValue(error);

      const result = await service.sendEmail(mockEmailOptions);

      expect(result).toBe(false);
      expect(mailerService.sendMail).toHaveBeenCalled();
    });

    it('should merge context with default values', async () => {
      const customContext: EmailContext = {
        customVar: 'customValue',
        baseUrl: 'should-be-overridden',
      };
      const emailWithContext = {
        ...mockEmailOptions,
        context: customContext,
      };
      mailerService.sendMail.mockResolvedValue(undefined);

      await service.sendEmail(emailWithContext);

      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          context: expect.objectContaining({
            customVar: 'customValue',
            baseUrl: 'https://test.rsglider.com', // Should use config value
            siteName: 'RSGlider Test',
            currentYear: expect.any(Number),
            supportEmail: '<EMAIL>',
          }),
        })
      );
    });
  });

  describe('sendWelcomeEmail', () => {
    it('should send welcome email with correct parameters', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.sendWelcomeEmail(
        '<EMAIL>',
        'John Doe',
        'verification-token-123'
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Welcome to RSGlider - Verify Your Email',
          template: 'welcome',
          context: expect.objectContaining({
            name: 'John Doe',
            verificationUrl: 'https://test.rsglider.com/auth/verify-email?token=verification-token-123',
            verificationToken: 'verification-token-123',
          }),
        })
      );
    });

    it('should handle welcome email sending failure', async () => {
      mailerService.sendMail.mockRejectedValue(new Error('Send failed'));

      const result = await service.sendWelcomeEmail(
        '<EMAIL>',
        'John Doe',
        'verification-token-123'
      );

      expect(result).toBe(false);
    });
  });

  describe('sendEmailVerification', () => {
    it('should send email verification with correct parameters', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.sendEmailVerification(
        '<EMAIL>',
        'Jane Doe',
        'verify-token-456'
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Verify Your Email Address',
          template: 'email-verification',
          context: expect.objectContaining({
            name: 'Jane Doe',
            verificationUrl: 'https://test.rsglider.com/auth/verify-email?token=verify-token-456',
            verificationToken: 'verify-token-456',
          }),
        })
      );
    });
  });

  describe('sendPasswordReset', () => {
    it('should send password reset email with correct parameters', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.sendPasswordReset(
        '<EMAIL>',
        'Bob Smith',
        'reset-token-789'
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Reset Your Password',
          template: 'password-reset',
          context: expect.objectContaining({
            name: 'Bob Smith',
            resetUrl: 'https://test.rsglider.com/auth/reset-password?token=reset-token-789',
            resetToken: 'reset-token-789',
          }),
        })
      );
    });
  });

  describe('sendPasswordChanged', () => {
    it('should send password changed notification', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.sendPasswordChanged(
        '<EMAIL>',
        'Alice Johnson'
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Your Password Has Been Changed',
          template: 'password-changed',
          context: expect.objectContaining({
            name: 'Alice Johnson',
            changeTime: expect.any(Date),
            deviceInfo: 'Unknown device',
            ipAddress: 'Unknown',
          }),
        })
      );
    });
  });

  describe('sendEmailChangeVerification', () => {
    it('should send email change verification', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.sendEmailChangeVerification(
        '<EMAIL>',
        'User Name',
        'change-token-123'
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Verify Your New Email Address',
          template: 'email-change-verification',
          context: expect.objectContaining({
            name: 'User Name',
            verificationUrl: 'https://test.rsglider.com/auth/verify-email-change?token=change-token-123',
            verificationToken: 'change-token-123',
          }),
        })
      );
    });
  });

  describe('send2FAEnabled', () => {
    it('should send 2FA enabled notification with backup codes', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);
      const backupCodes = ['ABC123', 'DEF456', 'GHI789'];

      const result = await service.send2FAEnabled(
        '<EMAIL>',
        'Test User',
        backupCodes
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Two-Factor Authentication Enabled',
          template: '2fa-enabled',
          context: expect.objectContaining({
            name: 'Test User',
            enabledTime: expect.any(Date),
            backupCodes,
          }),
        })
      );
    });

    it('should send 2FA enabled notification with empty backup codes', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);

      const result = await service.send2FAEnabled(
        '<EMAIL>',
        'Test User',
        []
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          context: expect.objectContaining({
            backupCodes: [],
          }),
        })
      );
    });
  });

  describe('send2FADisabled', () => {
    it('should send 2FA disabled notification', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);
      const deviceInfo = { browser: 'Firefox', platform: 'Windows' };

      const result = await service.send2FADisabled(
        '<EMAIL>',
        'Test User',
        deviceInfo
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Two-Factor Authentication Disabled',
          template: '2fa-disabled',
          context: expect.objectContaining({
            name: 'Test User',
            disabledTime: expect.any(Date),
            deviceInfo,
          }),
        })
      );
    });
  });

  describe('sendNewDeviceLogin', () => {
    it('should send new device login notification', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);
      const deviceInfo = {
        userAgent: 'Mozilla/5.0...',
        platform: 'macOS',
        browser: 'Safari',
        ipAddress: '*************',
      };

      const result = await service.sendNewDeviceLogin(
        '<EMAIL>',
        'Test User',
        deviceInfo,
        'San Francisco, CA'
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'New Device Login Detected',
          template: 'new-device-login',
          context: expect.objectContaining({
            name: 'Test User',
            deviceInfo,
            location: 'San Francisco, CA',
            loginTime: expect.any(Date),
            ipAddress: '*************',
          }),
        })
      );
    });

    it('should handle missing location and IP address', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);
      const deviceInfo = { browser: 'Chrome' };

      const result = await service.sendNewDeviceLogin(
        '<EMAIL>',
        'Test User',
        deviceInfo
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          context: expect.objectContaining({
            location: 'Unknown location',
            ipAddress: 'Unknown',
          }),
        })
      );
    });
  });

  describe('sendSecurityAlert', () => {
    it('should send security alert', async () => {
      mailerService.sendMail.mockResolvedValue(undefined);
      const alertDetails = {
        action: 'Suspicious login attempt',
        ipAddress: '*************',
        userAgent: 'Unknown browser',
      };

      const result = await service.sendSecurityAlert(
        '<EMAIL>',
        'Test User',
        'Suspicious Activity',
        alertDetails
      );

      expect(result).toBe(true);
      expect(mailerService.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: 'Security Alert: Suspicious Activity',
          template: 'security-alert',
          context: expect.objectContaining({
            name: 'Test User',
            alertType: 'Suspicious Activity',
            details: alertDetails,
            alertTime: expect.any(Date),
          }),
        })
      );
    });
  });
});
