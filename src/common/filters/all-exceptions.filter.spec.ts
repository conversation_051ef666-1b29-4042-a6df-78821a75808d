import { ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { AllExceptionsFilter } from './all-exceptions.filter';

describe('AllExceptionsFilter', () => {
  let filter: AllExceptionsFilter;
  let mockResponse: Partial<Response>;
  let mockRequest: Partial<Request>;
  let mockArgumentsHost: Partial<ArgumentsHost>;
  let loggerErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    filter = new AllExceptionsFilter();

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockRequest = {
      url: '/test-endpoint',
      method: 'GET',
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getResponse: jest.fn().mockReturnValue(mockResponse),
        getRequest: jest.fn().mockReturnValue(mockRequest),
      }),
    };

    // Mock the logger instance
    loggerErrorSpy = jest.spyOn(filter['logger'], 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('catch', () => {
    it('should handle HttpException with string response', () => {
      // Arrange
      const exception = new HttpException('Bad Request', HttpStatus.BAD_REQUEST);

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.BAD_REQUEST,
        timestamp: expect.any(String),
        path: '/test-endpoint',
        method: 'GET',
        error: 'Internal Server Error',
        message: 'Bad Request',
      });
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        'GET /test-endpoint - 400 - Bad Request',
        expect.any(String), // HttpException has a stack trace
      );
    });

    it('should handle HttpException with object response', () => {
      // Arrange
      const exceptionResponse = {
        message: 'Validation failed',
        error: 'Bad Request',
        statusCode: 400,
      };
      const exception = new HttpException(exceptionResponse, HttpStatus.BAD_REQUEST);

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.BAD_REQUEST,
        timestamp: expect.any(String),
        path: '/test-endpoint',
        method: 'GET',
        error: 'Bad Request',
        message: 'Validation failed',
      });
    });

    it('should handle HttpException with object response missing message', () => {
      // Arrange
      const exceptionResponse = {
        error: 'Custom Error',
        statusCode: 422,
      };
      const exception = new HttpException(exceptionResponse, HttpStatus.UNPROCESSABLE_ENTITY);
      exception.message = 'Fallback message';

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.UNPROCESSABLE_ENTITY,
        timestamp: expect.any(String),
        path: '/test-endpoint',
        method: 'GET',
        error: 'Custom Error',
        message: 'Fallback message',
      });
    });

    it('should handle HttpException with null object response', () => {
      // Arrange
      const exception = new HttpException(null, HttpStatus.INTERNAL_SERVER_ERROR);

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        timestamp: expect.any(String),
        path: '/test-endpoint',
        method: 'GET',
        error: 'Internal Server Error',
        message: 'Internal server error',
      });
    });

    it('should handle regular Error instances', () => {
      // Arrange
      const error = new Error('Something went wrong');
      error.stack = 'Error: Something went wrong\n    at test';

      // Act
      filter.catch(error, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        timestamp: expect.any(String),
        path: '/test-endpoint',
        method: 'GET',
        error: 'Internal Server Error',
        message: 'Something went wrong',
      });
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        'GET /test-endpoint - 500 - Something went wrong',
        'Error: Something went wrong\n    at test',
      );
    });

    it('should handle unknown exceptions', () => {
      // Arrange
      const unknownException = 'string exception';

      // Act
      filter.catch(unknownException, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        timestamp: expect.any(String),
        path: '/test-endpoint',
        method: 'GET',
        error: 'Internal Server Error',
        message: 'Internal server error',
      });
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        'GET /test-endpoint - 500 - Internal server error',
        undefined,
      );
    });

    it('should handle different HTTP methods and paths', () => {
      // Arrange
      mockRequest.method = 'POST';
      mockRequest.url = '/api/users';
      const exception = new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.UNAUTHORIZED,
        timestamp: expect.any(String),
        path: '/api/users',
        method: 'POST',
        error: 'Internal Server Error',
        message: 'Unauthorized',
      });
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        'POST /api/users - 401 - Unauthorized',
        expect.any(String), // HttpException has a stack trace
      );
    });

    it('should handle Error without stack trace', () => {
      // Arrange
      const error = new Error('No stack trace');
      delete error.stack;

      // Act
      filter.catch(error, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(loggerErrorSpy).toHaveBeenCalledWith(
        'GET /test-endpoint - 500 - No stack trace',
        undefined,
      );
    });

    it('should generate ISO timestamp', () => {
      // Arrange
      const exception = new HttpException('Test', HttpStatus.BAD_REQUEST);
      const beforeTime = new Date().toISOString();

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      const afterTime = new Date().toISOString();
      const responseCall = (mockResponse.json as jest.Mock).mock.calls[0][0];
      expect(responseCall.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(responseCall.timestamp >= beforeTime).toBe(true);
      expect(responseCall.timestamp <= afterTime).toBe(true);
    });

    it('should handle complex nested object responses', () => {
      // Arrange
      const complexResponse = {
        message: ['field1 is required', 'field2 must be a string'],
        error: 'Validation Error',
        statusCode: 400,
        details: {
          field1: 'missing',
          field2: 'invalid type',
        },
      };
      const exception = new HttpException(complexResponse, HttpStatus.BAD_REQUEST);

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.BAD_REQUEST,
        timestamp: expect.any(String),
        path: '/test-endpoint',
        method: 'GET',
        error: 'Validation Error',
        message: ['field1 is required', 'field2 must be a string'],
      });
    });

    it('should handle exceptions with different status codes', () => {
      // Arrange
      const testCases = [
        { status: HttpStatus.NOT_FOUND, message: 'Not Found' },
        { status: HttpStatus.FORBIDDEN, message: 'Forbidden' },
        { status: HttpStatus.CONFLICT, message: 'Conflict' },
        { status: HttpStatus.TOO_MANY_REQUESTS, message: 'Too Many Requests' },
      ];

      testCases.forEach(({ status, message }) => {
        // Reset mocks
        jest.clearAllMocks();

        const exception = new HttpException(message, status);

        // Act
        filter.catch(exception, mockArgumentsHost as ArgumentsHost);

        // Assert
        expect(mockResponse.status).toHaveBeenCalledWith(status);
        expect(mockResponse.json).toHaveBeenCalledWith({
          statusCode: status,
          timestamp: expect.any(String),
          path: '/test-endpoint',
          method: 'GET',
          error: 'Internal Server Error',
          message,
        });
      });
    });
  });
});
