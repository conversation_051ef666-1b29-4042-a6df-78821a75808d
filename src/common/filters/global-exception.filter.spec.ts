import { ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { GlobalExceptionFilter } from './global-exception.filter';

describe('GlobalExceptionFilter', () => {
  let filter: GlobalExceptionFilter;
  let mockResponse: Partial<Response>;
  let mockRequest: Partial<Request>;
  let mockArgumentsHost: Partial<ArgumentsHost>;
  let loggerErrorSpy: jest.SpyInstance;
  let originalEnv: string | undefined;

  beforeEach(() => {
    filter = new GlobalExceptionFilter();
    originalEnv = process.env.NODE_ENV;

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockRequest = {
      url: '/api/test',
      method: 'POST',
      body: { username: 'test', password: 'secret123' },
      query: { page: '1' },
      params: { id: '123' },
      headers: {
        'authorization': 'Bearer token123',
        'cookie': 'session=abc123',
        'x-api-key': 'api-key-123',
        'user-agent': 'Test Agent',
        'content-type': 'application/json',
      },
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getResponse: jest.fn().mockReturnValue(mockResponse),
        getRequest: jest.fn().mockReturnValue(mockRequest),
      }),
    };

    // Mock the logger instance
    loggerErrorSpy = jest.spyOn(filter['logger'], 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
    process.env.NODE_ENV = originalEnv;
  });

  describe('catch', () => {
    it('should handle HttpException with string response', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const exception = new HttpException('Bad Request', HttpStatus.BAD_REQUEST);

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.BAD_REQUEST,
        timestamp: expect.any(String),
        path: '/api/test',
        error: 'HttpException',
        message: 'Bad Request',
      });
    });

    it('should handle HttpException with object response', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const exceptionResponse = {
        message: 'Validation failed',
        error: 'Bad Request',
      };
      const exception = new HttpException(exceptionResponse, HttpStatus.BAD_REQUEST);

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.BAD_REQUEST,
        timestamp: expect.any(String),
        path: '/api/test',
        error: 'Bad Request',
        message: 'Validation failed',
      });
    });

    it('should handle non-HTTP exceptions', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const error = new Error('Database connection failed');

      // Act
      filter.catch(error, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        timestamp: expect.any(String),
        path: '/api/test',
        error: 'Internal Server Error',
        message: 'Internal server error',
      });
    });
  });

  describe('logError', () => {
    it('should skip logging in test environment', () => {
      // Arrange
      process.env.NODE_ENV = 'test';
      const exception = new Error('Test error');

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(loggerErrorSpy).not.toHaveBeenCalled();
    });

    it('should log errors in development environment', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const exception = new Error('Development error');
      exception.stack = 'Error stack trace';

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(loggerErrorSpy).toHaveBeenCalledWith('Application Error:', expect.objectContaining({
        timestamp: expect.any(String),
        method: 'POST',
        url: '/api/test',
        status: 500,
        query: { page: '1' },
        params: { id: '123' },
        headers: expect.objectContaining({
          'user-agent': 'Test Agent',
          'content-type': 'application/json',
        }),
        body: { username: 'test', password: '[REDACTED]' },
        error: {
          name: 'Error',
          message: 'Development error',
          stack: 'Error stack trace',
        },
      }));
    });

    it('should sanitize sensitive headers in logs', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const exception = new Error('Test error');

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      const logCall = loggerErrorSpy.mock.calls[0][1];
      expect(logCall.headers).not.toHaveProperty('authorization');
      expect(logCall.headers).not.toHaveProperty('cookie');
      expect(logCall.headers).not.toHaveProperty('x-api-key');
      expect(logCall.headers).toHaveProperty('user-agent');
      expect(logCall.headers).toHaveProperty('content-type');
    });

    it('should hide stack trace in production logs', () => {
      // Arrange
      process.env.NODE_ENV = 'production';
      const exception = new Error('Production error');
      exception.stack = 'Error stack trace';

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      const logCall = loggerErrorSpy.mock.calls[0][1];
      expect(logCall.error.stack).toBeUndefined();
    });
  });

  describe('sanitizeErrorResponse', () => {
    it('should expose client errors (4xx) in production', () => {
      // Arrange
      process.env.NODE_ENV = 'production';
      const exception = new HttpException('Not Found', HttpStatus.NOT_FOUND);

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.NOT_FOUND,
        timestamp: expect.any(String),
        path: '/api/test',
        error: 'HttpException',
        message: 'Not Found',
      });
    });

    it('should hide server errors (5xx) in production', () => {
      // Arrange
      process.env.NODE_ENV = 'production';
      const exception = new Error('Database connection failed');

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        timestamp: expect.any(String),
        path: '/api/test',
        error: 'Internal Server Error',
        message: 'An unexpected error occurred',
      });
    });

    it('should expose all errors in development', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const exception = new Error('Detailed error message');

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(mockResponse.json).toHaveBeenCalledWith({
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        timestamp: expect.any(String),
        path: '/api/test',
        error: 'Internal Server Error',
        message: 'Internal server error',
      });
    });
  });

  describe('sanitizeRequestBody', () => {
    it('should redact sensitive fields from request body', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      mockRequest.body = {
        username: 'testuser',
        password: 'secret123',
        token: 'jwt-token',
        secret: 'api-secret',
        key: 'encryption-key',
        authorization: 'Bearer token',
        auth: 'auth-data',
        credential: 'cred-data',
        private: 'private-data',
        publicData: 'this should remain',
      };
      const exception = new Error('Test error');

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      const logCall = loggerErrorSpy.mock.calls[0][1];
      expect(logCall.body).toEqual({
        username: 'testuser',
        password: '[REDACTED]',
        token: '[REDACTED]',
        secret: '[REDACTED]',
        key: '[REDACTED]',
        authorization: '[REDACTED]',
        auth: '[REDACTED]',
        credential: '[REDACTED]',
        private: '[REDACTED]',
        publicData: 'this should remain',
      });
    });

    it('should handle non-object request bodies', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      mockRequest.body = 'string body';
      const exception = new Error('Test error');

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      const logCall = loggerErrorSpy.mock.calls[0][1];
      expect(logCall.body).toBe('string body');
    });

    it('should handle null/undefined request bodies', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      mockRequest.body = null;
      const exception = new Error('Test error');

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      const logCall = loggerErrorSpy.mock.calls[0][1];
      expect(logCall.body).toBeNull();
    });
  });

  describe('isDatabaseError', () => {
    it('should identify PostgreSQL errors', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const dbError = new Error('PostgresError: duplicate key value violates unique constraint');

      // Act
      filter.catch(dbError, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(loggerErrorSpy).toHaveBeenCalledWith('Database Error:', expect.any(Object));
    });

    it('should identify QueryFailedError', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const dbError = new Error('QueryFailedError: invalid input syntax for type integer');

      // Act
      filter.catch(dbError, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(loggerErrorSpy).toHaveBeenCalledWith('Database Error:', expect.any(Object));
    });

    it('should identify various database error types', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const errorTypes = [
        'DatabaseError: connection failed',
        'SequelizeError: validation failed',
        'MongoError: connection timeout',
        'Failed query: SELECT * FROM users',
        'violates foreign key constraint "fk_user_id"',
      ];

      errorTypes.forEach(errorMessage => {
        jest.clearAllMocks();
        const dbError = new Error(errorMessage);

        // Act
        filter.catch(dbError, mockArgumentsHost as ArgumentsHost);

        // Assert
        expect(loggerErrorSpy).toHaveBeenCalledWith('Database Error:', expect.any(Object));
      });
    });

    it('should not identify regular errors as database errors', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const regularError = new Error('Regular application error');

      // Act
      filter.catch(regularError, mockArgumentsHost as ArgumentsHost);

      // Assert
      expect(loggerErrorSpy).toHaveBeenCalledWith('Application Error:', expect.any(Object));
    });

    it('should handle database errors in production vs development', () => {
      // Arrange
      const dbError = new Error('PostgresError: sensitive database info');

      // Test development
      process.env.NODE_ENV = 'development';
      filter.catch(dbError, mockArgumentsHost as ArgumentsHost);
      let logCall = loggerErrorSpy.mock.calls[0][1];
      expect(logCall.databaseError).toBe(dbError);

      // Reset and test production
      jest.clearAllMocks();
      process.env.NODE_ENV = 'production';
      filter.catch(dbError, mockArgumentsHost as ArgumentsHost);
      logCall = loggerErrorSpy.mock.calls[0][1];
      expect(logCall.databaseError).toBe('Database operation failed');
    });
  });

  describe('edge cases', () => {
    it('should handle exceptions with circular references', () => {
      // Arrange
      process.env.NODE_ENV = 'development';
      const circularObj: any = { name: 'test' };
      circularObj.self = circularObj;
      const exception = new HttpException(circularObj, HttpStatus.BAD_REQUEST);

      // Act & Assert - should not throw
      expect(() => {
        filter.catch(exception, mockArgumentsHost as ArgumentsHost);
      }).not.toThrow();
    });

    it('should generate valid ISO timestamps', () => {
      // Arrange
      const exception = new HttpException('Test', HttpStatus.BAD_REQUEST);
      const beforeTime = new Date().toISOString();

      // Act
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // Assert
      const afterTime = new Date().toISOString();
      const responseCall = (mockResponse.json as jest.Mock).mock.calls[0][0];
      expect(responseCall.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(responseCall.timestamp >= beforeTime).toBe(true);
      expect(responseCall.timestamp <= afterTime).toBe(true);
    });
  });
});
