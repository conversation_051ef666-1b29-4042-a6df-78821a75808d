/**
 * Global Exception Filter
 * Sanitizes errors to prevent sensitive information exposure
 */

import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Determine status code
    const status = exception instanceof HttpException
      ? exception.getStatus()
      : HttpStatus.INTERNAL_SERVER_ERROR;

    // Get the error message
    let message: string | object = 'Internal server error';
    let error = 'Internal Server Error';

    if (exception instanceof HttpException) {
      const exceptionResponse = exception.getResponse();
      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
        error = exception.name;
      } else if (typeof exceptionResponse === 'object') {
        message = (exceptionResponse as any).message || message;
        error = (exceptionResponse as any).error || exception.name;
      }
    }

    // Log the full error internally (with sensitive details for debugging)
    this.logError(exception, request, status);

    // Sanitize the response for production
    const sanitizedResponse = this.sanitizeErrorResponse(
      status,
      message,
      error,
      request.url,
    );

    response.status(status).json(sanitizedResponse);
  }

  private logError(exception: any, request: Request, status: number): void {
    // Skip logging during tests to reduce noise
    if (process.env.NODE_ENV === 'test') {
      return;
    }

    const { method, url, body, query, params, headers } = request;

    // Create a sanitized request log (remove sensitive headers)
    const sanitizedHeaders = { ...headers };
    delete sanitizedHeaders.authorization;
    delete sanitizedHeaders.cookie;
    delete sanitizedHeaders['x-api-key'];

    const errorLog = {
      timestamp: new Date().toISOString(),
      method,
      url,
      status,
      query,
      params,
      headers: sanitizedHeaders,
      body: this.sanitizeRequestBody(body),
      error: {
        name: exception.name,
        message: exception.message,
        stack: process.env.NODE_ENV === 'development' ? exception.stack : undefined,
      },
    };

    // Log database errors with special handling
    if (this.isDatabaseError(exception)) {
      this.logger.error('Database Error:', {
        ...errorLog,
        databaseError: process.env.NODE_ENV === 'development' ? exception : 'Database operation failed',
      });
    } else {
      this.logger.error('Application Error:', errorLog);
    }
  }

  private sanitizeErrorResponse(
    status: number,
    message: string | object,
    error: string,
    path: string,
  ): object {
    const baseResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      path,
    };

    // In production, sanitize error messages
    if (process.env.NODE_ENV === 'production') {
      // Only expose safe error messages in production
      if (status >= 400 && status < 500) {
        // Client errors - safe to expose
        return {
          ...baseResponse,
          error,
          message,
        };
      } else {
        // Server errors - hide details
        return {
          ...baseResponse,
          error: 'Internal Server Error',
          message: 'An unexpected error occurred',
        };
      }
    } else {
      // Development/test - show full details
      return {
        ...baseResponse,
        error,
        message,
      };
    }
  }

  private sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };

    // Remove sensitive fields
    const sensitiveFields = [
      'password',
      'token',
      'secret',
      'key',
      'authorization',
      'auth',
      'credential',
      'private',
    ];

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  private isDatabaseError(exception: any): boolean {
    // Check for common database error patterns
    const databaseErrorIndicators = [
      'PostgresError',
      'QueryFailedError',
      'DatabaseError',
      'SequelizeError',
      'MongoError',
      'Failed query:',
      'duplicate key value',
      'violates foreign key constraint',
      'invalid input syntax',
    ];

    const errorString = exception.toString();
    return databaseErrorIndicators.some(indicator =>
      errorString.includes(indicator)
    );
  }
}
