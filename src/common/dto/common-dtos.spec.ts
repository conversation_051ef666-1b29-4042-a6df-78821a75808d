import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

import { CreateRepositoryOptionsDto } from './create-repository-options.dto';
import { CreateUserOptionsDto } from './create-user-options.dto';
import { GiteaUserDto } from './gitea-user.dto';

describe('Common DTOs', () => {
  describe('CreateRepositoryOptionsDto', () => {
    it('should be defined', () => {
      expect(CreateRepositoryOptionsDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(CreateRepositoryOptionsDto, {
        name: 'test-repo',
        description: 'A test repository',
        private: false,
        auto_init: true,
        gitignores: 'Node',
        license: 'MIT',
        readme: 'Default',
        default_branch: 'main',
        trust_model: 'default',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal required data', async () => {
      const dto = plainToClass(CreateRepositoryOptionsDto, {
        name: 'minimal-repo',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require name', async () => {
      const dto = plainToClass(CreateRepositoryOptionsDto, {
        description: 'Missing name',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('name');
    });

    it('should validate name as string', async () => {
      const dto = plainToClass(CreateRepositoryOptionsDto, {
        name: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('name');
    });

    it('should validate private as boolean', async () => {
      const dto = plainToClass(CreateRepositoryOptionsDto, {
        name: 'test-repo',
        private: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const privateError = errors.find(error => error.property === 'private');
      expect(privateError).toBeDefined();
    });

    it('should validate trust_model enum', async () => {
      const dto = plainToClass(CreateRepositoryOptionsDto, {
        name: 'test-repo',
        trust_model: 'invalid-trust-model',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const trustModelError = errors.find(error => error.property === 'trust_model');
      expect(trustModelError).toBeDefined();
    });

    it('should accept valid trust_model values', async () => {
      const validTrustModels = ['default', 'collaborator', 'committer', 'collaboratorcommitter'];

      for (const trustModel of validTrustModels) {
        const dto = plainToClass(CreateRepositoryOptionsDto, {
          name: 'test-repo',
          trust_model: trustModel,
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });
  });

  describe('CreateUserOptionsDto', () => {
    it('should be defined', () => {
      expect(CreateUserOptionsDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(CreateUserOptionsDto, {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'securepassword123',
        full_name: 'Test User',
        login_name: 'testuser',
        send_notify: true,
        source_id: 1,
        must_change_password: false,
        restricted: false,
        visibility: 'public',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal required data', async () => {
      const dto = plainToClass(CreateUserOptionsDto, {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'securepassword123',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require username', async () => {
      const dto = plainToClass(CreateUserOptionsDto, {
        email: '<EMAIL>',
        password: 'securepassword123',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('username');
    });

    it('should require email', async () => {
      const dto = plainToClass(CreateUserOptionsDto, {
        username: 'testuser',
        password: 'securepassword123',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const emailError = errors.find(error => error.property === 'email');
      expect(emailError).toBeDefined();
    });

    it('should require password', async () => {
      const dto = plainToClass(CreateUserOptionsDto, {
        username: 'testuser',
        email: '<EMAIL>',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const passwordError = errors.find(error => error.property === 'password');
      expect(passwordError).toBeDefined();
    });

    it('should validate email format', async () => {
      const dto = plainToClass(CreateUserOptionsDto, {
        username: 'testuser',
        email: 'invalid-email',
        password: 'securepassword123',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const emailError = errors.find(error => error.property === 'email');
      expect(emailError).toBeDefined();
    });

    it('should validate visibility enum', async () => {
      const dto = plainToClass(CreateUserOptionsDto, {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'securepassword123',
        visibility: 'invalid-visibility',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const visibilityError = errors.find(error => error.property === 'visibility');
      expect(visibilityError).toBeDefined();
    });

    it('should accept valid visibility values', async () => {
      const validVisibilities = ['public', 'limited', 'private'];

      for (const visibility of validVisibilities) {
        const dto = plainToClass(CreateUserOptionsDto, {
          username: 'testuser',
          email: '<EMAIL>',
          password: 'securepassword123',
          visibility: visibility,
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate source_id as number', async () => {
      const dto = plainToClass(CreateUserOptionsDto, {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'securepassword123',
        source_id: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const sourceIdError = errors.find(error => error.property === 'source_id');
      expect(sourceIdError).toBeDefined();
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(CreateUserOptionsDto, {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'securepassword123',
        send_notify: 'not-a-boolean',
        must_change_password: 'not-a-boolean',
        restricted: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);

      const booleanErrors = errors.filter(error =>
        ['send_notify', 'must_change_password', 'restricted'].includes(error.property)
      );
      expect(booleanErrors.length).toBeGreaterThan(0);
    });
  });

  describe('GiteaUserDto', () => {
    it('should be defined', () => {
      expect(GiteaUserDto).toBeDefined();
    });

    it('should create instance', () => {
      const dto = new GiteaUserDto();
      expect(dto).toBeInstanceOf(GiteaUserDto);
    });

    it('should create instance with plainToClass', () => {
      const dto = plainToClass(GiteaUserDto, {
        id: 123,
        login: 'testuser',
        full_name: 'Test User',
        email: '<EMAIL>',
        avatar_url: 'https://example.com/avatar.png',
        language: 'en-US',
        is_admin: false,
        last_login: '2023-01-01T00:00:00Z',
        created: '2022-01-01T00:00:00Z',
        restricted: false,
        active: true,
        prohibit_login: false,
        location: 'Test City',
        website: 'https://example.com',
        description: 'A test user',
        visibility: 'public',
        followers_count: 10,
        following_count: 5,
        starred_repos_count: 20,
        username: 'testuser',
      });

      expect(dto).toBeInstanceOf(GiteaUserDto);
      expect(dto.id).toBe(123);
      expect(dto.login).toBe('testuser');
      expect(dto.email).toBe('<EMAIL>');
    });

    it('should handle all properties', () => {
      const dto = new GiteaUserDto();

      // Test that all properties can be set
      dto.id = 456;
      dto.login = 'anotheruser';
      dto.full_name = 'Another User';
      dto.email = '<EMAIL>';
      dto.avatar_url = 'https://example.com/avatar2.png';
      dto.language = 'fr-FR';
      dto.is_admin = true;
      dto.last_login = '2023-02-01T00:00:00Z';
      dto.created = '2022-02-01T00:00:00Z';
      dto.restricted = true;
      dto.active = false;
      dto.prohibit_login = true;
      dto.location = 'Another City';
      dto.website = 'https://another.com';
      dto.description = 'Another test user';
      dto.visibility = 'private';
      dto.followers_count = 15;
      dto.following_count = 8;
      dto.starred_repos_count = 25;
      dto.username = 'anotheruser';

      expect(dto.id).toBe(456);
      expect(dto.is_admin).toBe(true);
      expect(dto.restricted).toBe(true);
    });
  });
});
