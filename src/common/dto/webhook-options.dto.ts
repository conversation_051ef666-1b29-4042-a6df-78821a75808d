import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsEnum, IsObject, IsOptional, IsString } from 'class-validator';

export class WebhookConfigDto {
  @ApiProperty()
  @IsString()
  url: string;

  @ApiPropertyOptional({ enum: ['json', 'form'] })
  @IsOptional()
  @IsEnum(['json', 'form'])
  content_type?: 'json' | 'form';

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  secret?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  password?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  http_method?: string;
}

export class WebhookOptionsDto {
  @ApiProperty({ enum: ['gitea', 'gogs', 'slack', 'discord', 'dingtalk', 'telegram', 'msteams', 'feishu', 'wechatwork', 'packagist'] })
  @IsEnum(['gitea', 'gogs', 'slack', 'discord', 'dingtalk', 'telegram', 'msteams', 'feishu', 'wechatwork', 'packagist'])
  type: 'gitea' | 'gogs' | 'slack' | 'discord' | 'dingtalk' | 'telegram' | 'msteams' | 'feishu' | 'wechatwork' | 'packagist';

  @ApiProperty()
  @IsObject()
  config: WebhookConfigDto;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  events: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  branch_filter?: string;
}
