import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsBoolean, IsOptional, IsArray, IsDateString } from 'class-validator';

export class GiteaVersionResponseDto {
  @ApiProperty({ description: 'Gitea version' })
  @IsString()
  version: string;
}

export class GiteaUserResponseDto {
  @ApiProperty({ description: 'User ID' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: 'Username' })
  @IsString()
  login: string;

  @ApiProperty({ description: 'Full name' })
  @IsString()
  full_name: string;

  @ApiProperty({ description: 'Email address' })
  @IsString()
  email: string;

  @ApiProperty({ description: 'Avatar URL', required: false })
  @IsOptional()
  @IsString()
  avatar_url?: string;

  @ApiProperty({ description: 'Language preference', required: false })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ description: 'Is admin user' })
  @IsBoolean()
  is_admin: boolean;

  @ApiProperty({ description: 'Last login timestamp', required: false })
  @IsOptional()
  @IsDateString()
  last_login?: string;

  @ApiProperty({ description: 'Created timestamp' })
  @IsDateString()
  created: string;

  @ApiProperty({ description: 'Restricted user' })
  @IsBoolean()
  restricted: boolean;

  @ApiProperty({ description: 'Active user' })
  @IsBoolean()
  active: boolean;

  @ApiProperty({ description: 'Prohibit login' })
  @IsBoolean()
  prohibit_login: boolean;

  @ApiProperty({ description: 'Location', required: false })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({ description: 'Website', required: false })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiProperty({ description: 'Description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Visibility' })
  @IsString()
  visibility: 'public' | 'limited' | 'private';

  @ApiProperty({ description: 'Followers count' })
  @IsNumber()
  followers_count: number;

  @ApiProperty({ description: 'Following count' })
  @IsNumber()
  following_count: number;

  @ApiProperty({ description: 'Starred repos count' })
  @IsNumber()
  starred_repos_count: number;

  @ApiProperty({ description: 'Username (alias for login)' })
  @IsString()
  username: string;
}

export class GiteaRepositoryOwnerDto {
  @ApiProperty({ description: 'Owner ID' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: 'Owner username' })
  @IsString()
  login: string;

  @ApiProperty({ description: 'Owner full name' })
  @IsString()
  full_name: string;

  @ApiProperty({ description: 'Owner email' })
  @IsString()
  email: string;

  @ApiProperty({ description: 'Owner avatar URL', required: false })
  @IsOptional()
  @IsString()
  avatar_url?: string;

  @ApiProperty({ description: 'Owner language', required: false })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ description: 'Is admin' })
  @IsBoolean()
  is_admin: boolean;

  @ApiProperty({ description: 'Last login', required: false })
  @IsOptional()
  @IsString()
  last_login?: string;

  @ApiProperty({ description: 'Created timestamp' })
  @IsString()
  created: string;

  @ApiProperty({ description: 'Restricted' })
  @IsBoolean()
  restricted: boolean;

  @ApiProperty({ description: 'Active' })
  @IsBoolean()
  active: boolean;

  @ApiProperty({ description: 'Prohibit login' })
  @IsBoolean()
  prohibit_login: boolean;

  @ApiProperty({ description: 'Location', required: false })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({ description: 'Website', required: false })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiProperty({ description: 'Description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Visibility' })
  @IsString()
  visibility: string;

  @ApiProperty({ description: 'Followers count' })
  @IsNumber()
  followers_count: number;

  @ApiProperty({ description: 'Following count' })
  @IsNumber()
  following_count: number;

  @ApiProperty({ description: 'Starred repos count' })
  @IsNumber()
  starred_repos_count: number;

  @ApiProperty({ description: 'Username' })
  @IsString()
  username: string;
}

export class GiteaRepositoryResponseDto {
  @ApiProperty({ description: 'Repository ID' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: 'Repository name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Full repository name' })
  @IsString()
  full_name: string;

  @ApiProperty({ description: 'Repository description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Repository owner' })
  owner: GiteaRepositoryOwnerDto;

  @ApiProperty({ description: 'Is private repository' })
  @IsBoolean()
  private: boolean;

  @ApiProperty({ description: 'Is fork' })
  @IsBoolean()
  fork: boolean;

  @ApiProperty({ description: 'Is template' })
  @IsBoolean()
  template: boolean;

  @ApiProperty({ description: 'Parent repository', required: false })
  @IsOptional()
  parent?: GiteaRepositoryResponseDto;

  @ApiProperty({ description: 'Is empty repository' })
  @IsBoolean()
  empty: boolean;

  @ApiProperty({ description: 'Is mirror' })
  @IsBoolean()
  mirror: boolean;

  @ApiProperty({ description: 'Repository size' })
  @IsNumber()
  size: number;

  @ApiProperty({ description: 'Primary language', required: false })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ description: 'Languages URL' })
  @IsString()
  languages_url: string;

  @ApiProperty({ description: 'HTML URL' })
  @IsString()
  html_url: string;

  @ApiProperty({ description: 'SSH URL' })
  @IsString()
  ssh_url: string;

  @ApiProperty({ description: 'Clone URL' })
  @IsString()
  clone_url: string;

  @ApiProperty({ description: 'Original URL', required: false })
  @IsOptional()
  @IsString()
  original_url?: string;

  @ApiProperty({ description: 'Website', required: false })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiProperty({ description: 'Stars count' })
  @IsNumber()
  stars_count: number;

  @ApiProperty({ description: 'Forks count' })
  @IsNumber()
  forks_count: number;

  @ApiProperty({ description: 'Watchers count' })
  @IsNumber()
  watchers_count: number;

  @ApiProperty({ description: 'Open issues count' })
  @IsNumber()
  open_issues_count: number;

  @ApiProperty({ description: 'Open PR count' })
  @IsNumber()
  open_pr_counter: number;

  @ApiProperty({ description: 'Release counter' })
  @IsNumber()
  release_counter: number;

  @ApiProperty({ description: 'Default branch' })
  @IsString()
  default_branch: string;

  @ApiProperty({ description: 'Is archived' })
  @IsBoolean()
  archived: boolean;

  @ApiProperty({ description: 'Created timestamp' })
  @IsDateString()
  created_at: string;

  @ApiProperty({ description: 'Updated timestamp' })
  @IsDateString()
  updated_at: string;

  @ApiProperty({ description: 'Pushed timestamp' })
  @IsDateString()
  pushed_at: string;

  @ApiProperty({ description: 'Git URL' })
  @IsString()
  git_url: string;

  @ApiProperty({ description: 'Has issues' })
  @IsBoolean()
  has_issues: boolean;

  @ApiProperty({ description: 'Has wiki' })
  @IsBoolean()
  has_wiki: boolean;

  @ApiProperty({ description: 'Has pull requests' })
  @IsBoolean()
  has_pull_requests: boolean;

  @ApiProperty({ description: 'Has projects' })
  @IsBoolean()
  has_projects: boolean;

  @ApiProperty({ description: 'Ignore whitespace conflicts' })
  @IsBoolean()
  ignore_whitespace_conflicts: boolean;

  @ApiProperty({ description: 'Allow merge commits' })
  @IsBoolean()
  allow_merge_commits: boolean;

  @ApiProperty({ description: 'Allow rebase' })
  @IsBoolean()
  allow_rebase: boolean;

  @ApiProperty({ description: 'Allow rebase explicit' })
  @IsBoolean()
  allow_rebase_explicit: boolean;

  @ApiProperty({ description: 'Allow squash merge' })
  @IsBoolean()
  allow_squash_merge: boolean;

  @ApiProperty({ description: 'Default merge style' })
  @IsString()
  default_merge_style: string;

  @ApiProperty({ description: 'Avatar URL', required: false })
  @IsOptional()
  @IsString()
  avatar_url?: string;

  @ApiProperty({ description: 'Is internal' })
  @IsBoolean()
  internal: boolean;

  @ApiProperty({ description: 'Mirror interval', required: false })
  @IsOptional()
  @IsString()
  mirror_interval?: string;

  @ApiProperty({ description: 'Mirror updated', required: false })
  @IsOptional()
  @IsString()
  mirror_updated?: string;

  @ApiProperty({ description: 'Repository transfer', required: false })
  @IsOptional()
  repo_transfer?: any;

  @ApiProperty({ description: 'Topics' })
  @IsArray()
  @IsString({ each: true })
  topics: string[];

  @ApiProperty({ description: 'Permissions', required: false })
  @IsOptional()
  permissions?: {
    admin: boolean;
    push: boolean;
    pull: boolean;
  };

  @ApiProperty({ description: 'Internal tracker', required: false })
  @IsOptional()
  internal_tracker?: {
    enable_time_tracker: boolean;
    allow_only_contributors_to_track_time: boolean;
    enable_issue_dependencies: boolean;
  };
}

export class GiteaWebhookResponseDto {
  @ApiProperty({ description: 'Webhook ID' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: 'Webhook type' })
  @IsString()
  type: string;

  @ApiProperty({ description: 'Webhook URL' })
  @IsString()
  url: string;

  @ApiProperty({ description: 'Content type' })
  @IsString()
  content_type: string;

  @ApiProperty({ description: 'Secret', required: false })
  @IsOptional()
  @IsString()
  secret?: string;

  @ApiProperty({ description: 'SSL verification' })
  @IsBoolean()
  ssl_verification: boolean;

  @ApiProperty({ description: 'Is active' })
  @IsBoolean()
  active: boolean;

  @ApiProperty({ description: 'Events' })
  @IsArray()
  @IsString({ each: true })
  events: string[];

  @ApiProperty({ description: 'Created timestamp' })
  @IsDateString()
  created_at: string;

  @ApiProperty({ description: 'Updated timestamp' })
  @IsDateString()
  updated_at: string;
}
