import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

import { UpdateUserOptionsDto } from './update-user-options.dto';
import { WebhookOptionsDto, WebhookConfigDto } from './webhook-options.dto';

describe('Common DTOs Part 2', () => {
  describe('UpdateUserOptionsDto', () => {
    it('should be defined', () => {
      expect(UpdateUserOptionsDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(UpdateUserOptionsDto, {
        source_id: 1,
        login_name: 'testuser',
        full_name: 'Test User',
        email: '<EMAIL>',
        password: 'newpassword123',
        must_change_password: false,
        website: 'https://example.com',
        location: 'Test City',
        description: 'Updated description',
        active: true,
        admin: false,
        allow_git_hook: true,
        allow_import_local: false,
        max_repo_creation: 10,
        prohibit_login: false,
        allow_create_organization: true,
        restricted: false,
        visibility: 'public',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with partial data (all fields optional)', async () => {
      const dto = plainToClass(UpdateUserOptionsDto, {
        full_name: 'Updated Name',
        email: '<EMAIL>',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate empty object (all fields optional)', async () => {
      const dto = plainToClass(UpdateUserOptionsDto, {});
      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate email format', async () => {
      const dto = plainToClass(UpdateUserOptionsDto, {
        email: 'invalid-email',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const emailError = errors.find(error => error.property === 'email');
      expect(emailError).toBeDefined();
    });

    it('should validate visibility enum', async () => {
      const dto = plainToClass(UpdateUserOptionsDto, {
        visibility: 'invalid-visibility',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const visibilityError = errors.find(error => error.property === 'visibility');
      expect(visibilityError).toBeDefined();
    });

    it('should accept valid visibility values', async () => {
      const validVisibilities = ['public', 'limited', 'private'];

      for (const visibility of validVisibilities) {
        const dto = plainToClass(UpdateUserOptionsDto, {
          visibility: visibility,
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate number fields', async () => {
      const dto = plainToClass(UpdateUserOptionsDto, {
        source_id: 'not-a-number',
        max_repo_creation: 'not-a-number',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const numberErrors = errors.filter(error => 
        ['source_id', 'max_repo_creation'].includes(error.property)
      );
      expect(numberErrors.length).toBeGreaterThan(0);
    });

    it('should validate boolean fields', async () => {
      const dto = plainToClass(UpdateUserOptionsDto, {
        must_change_password: 'not-a-boolean',
        active: 'not-a-boolean',
        admin: 'not-a-boolean',
        allow_git_hook: 'not-a-boolean',
        allow_import_local: 'not-a-boolean',
        prohibit_login: 'not-a-boolean',
        allow_create_organization: 'not-a-boolean',
        restricted: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const booleanFields = [
        'must_change_password', 'active', 'admin', 'allow_git_hook',
        'allow_import_local', 'prohibit_login', 'allow_create_organization', 'restricted'
      ];
      
      const booleanErrors = errors.filter(error => 
        booleanFields.includes(error.property)
      );
      expect(booleanErrors.length).toBeGreaterThan(0);
    });
  });

  describe('WebhookConfigDto', () => {
    it('should be defined', () => {
      expect(WebhookConfigDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(WebhookConfigDto, {
        url: 'https://example.com/webhook',
        content_type: 'json',
        secret: 'webhook-secret',
        username: 'webhook-user',
        password: 'webhook-pass',
        http_method: 'POST',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal required data', async () => {
      const dto = plainToClass(WebhookConfigDto, {
        url: 'https://example.com/webhook',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require url', async () => {
      const dto = plainToClass(WebhookConfigDto, {
        content_type: 'json',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('url');
    });

    it('should validate url as string', async () => {
      const dto = plainToClass(WebhookConfigDto, {
        url: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('url');
    });

    it('should validate content_type enum', async () => {
      const dto = plainToClass(WebhookConfigDto, {
        url: 'https://example.com/webhook',
        content_type: 'invalid-type',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const contentTypeError = errors.find(error => error.property === 'content_type');
      expect(contentTypeError).toBeDefined();
    });

    it('should accept valid content_type values', async () => {
      const validContentTypes = ['json', 'form'];

      for (const contentType of validContentTypes) {
        const dto = plainToClass(WebhookConfigDto, {
          url: 'https://example.com/webhook',
          content_type: contentType,
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate optional string fields', async () => {
      const dto = plainToClass(WebhookConfigDto, {
        url: 'https://example.com/webhook',
        secret: 123,
        username: 456,
        password: 789,
        http_method: 101112,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      
      const stringFields = ['secret', 'username', 'password', 'http_method'];
      const stringErrors = errors.filter(error => 
        stringFields.includes(error.property)
      );
      expect(stringErrors.length).toBeGreaterThan(0);
    });
  });

  describe('WebhookOptionsDto', () => {
    it('should be defined', () => {
      expect(WebhookOptionsDto).toBeDefined();
    });

    it('should validate with valid data', async () => {
      const dto = plainToClass(WebhookOptionsDto, {
        type: 'gitea',
        config: {
          url: 'https://example.com/webhook',
          content_type: 'json',
          secret: 'webhook-secret',
        },
        events: ['push', 'pull_request', 'issues'],
        active: true,
        branch_filter: 'main',
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should validate with minimal required data', async () => {
      const dto = plainToClass(WebhookOptionsDto, {
        type: 'gitea',
        config: {
          url: 'https://example.com/webhook',
        },
        events: ['push'],
      });

      const errors = await validate(dto);
      expect(errors).toHaveLength(0);
    });

    it('should require type', async () => {
      const dto = plainToClass(WebhookOptionsDto, {
        config: {
          url: 'https://example.com/webhook',
        },
        events: ['push'],
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors[0].property).toBe('type');
    });

    it('should require config', async () => {
      const dto = plainToClass(WebhookOptionsDto, {
        type: 'gitea',
        events: ['push'],
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const configError = errors.find(error => error.property === 'config');
      expect(configError).toBeDefined();
    });

    it('should require events', async () => {
      const dto = plainToClass(WebhookOptionsDto, {
        type: 'gitea',
        config: {
          url: 'https://example.com/webhook',
        },
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const eventsError = errors.find(error => error.property === 'events');
      expect(eventsError).toBeDefined();
    });

    it('should validate type enum', async () => {
      const dto = plainToClass(WebhookOptionsDto, {
        type: 'invalid-type',
        config: {
          url: 'https://example.com/webhook',
        },
        events: ['push'],
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const typeError = errors.find(error => error.property === 'type');
      expect(typeError).toBeDefined();
    });

    it('should accept valid type values', async () => {
      const validTypes = ['gitea', 'gogs', 'slack', 'discord', 'dingtalk', 'telegram', 'msteams', 'feishu', 'wechatwork', 'packagist'];

      for (const type of validTypes) {
        const dto = plainToClass(WebhookOptionsDto, {
          type: type,
          config: {
            url: 'https://example.com/webhook',
          },
          events: ['push'],
        });

        const errors = await validate(dto);
        expect(errors).toHaveLength(0);
      }
    });

    it('should validate events as array of strings', async () => {
      const dto = plainToClass(WebhookOptionsDto, {
        type: 'gitea',
        config: {
          url: 'https://example.com/webhook',
        },
        events: ['push', 123, 'pull_request'],
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const eventsError = errors.find(error => error.property === 'events');
      expect(eventsError).toBeDefined();
    });

    it('should validate active as boolean', async () => {
      const dto = plainToClass(WebhookOptionsDto, {
        type: 'gitea',
        config: {
          url: 'https://example.com/webhook',
        },
        events: ['push'],
        active: 'not-a-boolean',
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const activeError = errors.find(error => error.property === 'active');
      expect(activeError).toBeDefined();
    });

    it('should validate branch_filter as string', async () => {
      const dto = plainToClass(WebhookOptionsDto, {
        type: 'gitea',
        config: {
          url: 'https://example.com/webhook',
        },
        events: ['push'],
        branch_filter: 123,
      });

      const errors = await validate(dto);
      expect(errors.length).toBeGreaterThan(0);
      const branchFilterError = errors.find(error => error.property === 'branch_filter');
      expect(branchFilterError).toBeDefined();
    });
  });
});
