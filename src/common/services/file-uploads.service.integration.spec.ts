/**
 * FileUploadsService Integration Tests
 * Tests the FileUploadsService with real database operations and mocked S3
 */

import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { eq } from 'drizzle-orm';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createMockFile,
  createMockS3Service,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase
} from '../../../test/database-setup.js';
import { fileUploads } from '../../database/schema/index.js';
import { FileUploadsService } from './file-uploads.service.js';
import { S3Service } from './s3.service.js';

describe('FileUploadsService Integration Tests', () => {
  let service: FileUploadsService;
  let testDb: TestDatabase;
  let mockS3Service: any;
  let testData: any;

  beforeAll(async () => {
    testDb = await createTestDatabase();
    mockS3Service = createMockS3Service();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FileUploadsService,
        {
          provide: 'DB',
          useValue: testDb,
        },
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
      ],
    }).compile();

    service = module.get<FileUploadsService>(FileUploadsService);
  });

  beforeEach(async () => {
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  describe('uploadFile', () => {
    it('should upload file successfully and store metadata in database', async () => {
      const mockFile = createMockFile({
        originalname: 'test-image.png',
        mimetype: 'image/png',
        size: 2048,
      });

      const uploadOptions = {
        userId: testData.testUser.id,
        file: mockFile,
        isPublic: false,
      };

      const result = await service.uploadFile(uploadOptions);

      expect(result).toMatchObject({
        id: expect.any(String),
        userId: testData.testUser.id,
        originalName: 'test-image.png',
        mimeType: 'image/png',
        fileType: 'image',
        fileSize: '2048',
        isPublic: false,
        status: 'completed',
        isValidated: true,
      });

      // Verify file record was created in database
      const [dbRecord] = await testDb
        .select()
        .from(fileUploads)
        .where(eq(fileUploads.id, result.id))
        .limit(1);

      expect(dbRecord).toBeDefined();
      expect(dbRecord.userId).toBe(testData.testUser.id);
      expect(dbRecord.originalName).toBe('test-image.png');
      expect(dbRecord.status).toBe('completed');

      // Verify S3 upload was called
      expect(mockS3Service.uploadFile).toHaveBeenCalledWith(
        expect.objectContaining({
          key: expect.stringMatching(/^uploads\/.*\/.*\.png$/),
          body: mockFile.buffer,
          contentType: 'image/png',
        })
      );
    });

    it('should upload public file with expiration', async () => {
      const mockFile = createMockFile();
      const expiresIn = 86400; // 24 hours

      const uploadOptions = {
        userId: testData.testUser.id,
        file: mockFile,
        isPublic: true,
        expiresIn,
      };

      const result = await service.uploadFile(uploadOptions);

      expect(result.isPublic).toBe(true);
      expect(result.expiresAt).toBeTruthy();
      expect(result.expiresAt.getTime()).toBeGreaterThan(Date.now());

      // Verify database record
      const [dbRecord] = await testDb
        .select()
        .from(fileUploads)
        .where(eq(fileUploads.id, result.id))
        .limit(1);

      expect(dbRecord.isPublic).toBe(true);
      expect(dbRecord.expiresAt).toBeTruthy();
    });

    it('should reject invalid file types', async () => {
      const mockFile = createMockFile({
        originalname: 'malicious.exe',
        mimetype: 'application/x-executable',
      });

      const uploadOptions = {
        userId: testData.testUser.id,
        file: mockFile,
        isPublic: false,
      };

      await expect(service.uploadFile(uploadOptions)).rejects.toThrow(BadRequestException);
      await expect(service.uploadFile(uploadOptions)).rejects.toThrow('File type not allowed');
    });

    it('should reject files that are too large', async () => {
      const mockFile = createMockFile({
        size: 100 * 1024 * 1024, // 100MB
      });

      const uploadOptions = {
        userId: testData.testUser.id,
        file: mockFile,
        isPublic: false,
      };

      await expect(service.uploadFile(uploadOptions)).rejects.toThrow(BadRequestException);
      await expect(service.uploadFile(uploadOptions)).rejects.toThrow('Image too large');
    });

    it('should handle S3 upload failures', async () => {
      mockS3Service.uploadFile.mockRejectedValueOnce(new Error('S3 upload failed'));

      const mockFile = createMockFile();
      const uploadOptions = {
        userId: testData.testUser.id,
        file: mockFile,
        isPublic: false,
      };

      await expect(service.uploadFile(uploadOptions)).rejects.toThrow('S3 upload failed');

      // Verify no database record was created
      const records = await testDb
        .select()
        .from(fileUploads)
        .where(eq(fileUploads.userId, testData.testUser.id));

      expect(records).toHaveLength(0);
    });
  });

  describe('generatePresignedUploadUrl', () => {
    it('should generate presigned URL and create pending upload record', async () => {
      const options = {
        userId: testData.testUser.id,
        fileName: 'test-document.jpg',
        fileSize: 5120,
        mimeType: 'image/jpeg',
        isPublic: false,
      };

      const result = await service.generatePresignedUploadUrl(options);

      expect(result).toMatchObject({
        uploadUrl: 'https://mock-bucket.s3.amazonaws.com/presigned-url',
        fileId: expect.any(String),
        s3Key: expect.stringMatching(/^uploads\/.*\/.*\.jpg$/),
      });

      // Verify pending upload record was created
      const [dbRecord] = await testDb
        .select()
        .from(fileUploads)
        .where(eq(fileUploads.id, result.fileId))
        .limit(1);

      expect(dbRecord).toBeDefined();
      expect(dbRecord.status).toBe('pending');
      expect(dbRecord.originalName).toBe('test-document.jpg');
      expect(dbRecord.mimeType).toBe('image/jpeg');
      expect(dbRecord.fileSize).toBe('5120');

      // Verify S3 service was called
      expect(mockS3Service.getPresignedUploadUrl).toHaveBeenCalledWith(
        expect.objectContaining({
          key: expect.stringMatching(/^uploads\/.*\/.*\.jpg$/),
          contentType: 'image/jpeg',
        })
      );
    });

    it('should reject invalid file types for presigned uploads', async () => {
      const options = {
        userId: testData.testUser.id,
        fileName: 'malicious.exe',
        fileSize: 1024,
        mimeType: 'application/x-executable',
        isPublic: false,
      };

      await expect(service.generatePresignedUploadUrl(options)).rejects.toThrow(BadRequestException);
      await expect(service.generatePresignedUploadUrl(options)).rejects.toThrow('File type not allowed');
    });
  });

  describe('completeUpload', () => {
    let pendingUploadId: string;

    beforeEach(async () => {
      // Create a pending upload record
      const [pendingUpload] = await testDb.insert(fileUploads).values({
        userId: testData.testUser.id,
        originalName: 'pending-file.jpg',
        fileName: 'uuid-pending-file.jpg',
        mimeType: 'image/jpeg',
        fileType: 'image',
        fileSize: '3072',
        s3Key: 'uploads/test-user/pending-file.jpg',
        s3Bucket: 'test-bucket',
        status: 'pending',
        uploadProgress: '0',
        isValidated: false,
        isPublic: false,
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();

      pendingUploadId = pendingUpload.id;
    });

    it('should complete upload successfully', async () => {
      const result = await service.completeUpload(pendingUploadId, testData.testUser.id);

      expect(result.status).toBe('completed');
      expect(result.uploadProgress).toBe('100.00');
      expect(result.isValidated).toBe(true);

      // Verify database was updated
      const [dbRecord] = await testDb
        .select()
        .from(fileUploads)
        .where(eq(fileUploads.id, pendingUploadId))
        .limit(1);

      expect(dbRecord.status).toBe('completed');
      expect(dbRecord.uploadProgress).toBe('100.00');
      expect(dbRecord.isValidated).toBe(true);
    });

    it('should throw NotFoundException for non-existent upload', async () => {
      const nonExistentId = 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a44'; // Valid UUID that doesn't exist
      await expect(
        service.completeUpload(nonExistentId, testData.testUser.id)
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException for other users upload', async () => {
      await expect(
        service.completeUpload(pendingUploadId, testData.adminUser.id)
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for already completed upload', async () => {
      // Complete the upload first
      await service.completeUpload(pendingUploadId, testData.testUser.id);

      // Try to complete again
      await expect(
        service.completeUpload(pendingUploadId, testData.testUser.id)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.completeUpload(pendingUploadId, testData.testUser.id)
      ).rejects.toThrow('Upload is not in pending state');
    });
  });

  describe('getUserFiles', () => {
    beforeEach(async () => {
      // Create test files for the user
      await testDb.insert(fileUploads).values([
        {
          userId: testData.testUser.id,
          originalName: 'user-file-1.jpg',
          fileName: 'uuid-user-file-1.jpg',
          mimeType: 'image/jpeg',
          fileType: 'image',
          fileSize: '2048',
          s3Key: 'uploads/test-user/file-1.jpg',
          s3Bucket: 'test-bucket',
          status: 'completed',
          uploadProgress: '100',
          isValidated: true,
          isPublic: false,
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          userId: testData.testUser.id,
          originalName: 'user-file-2.pdf',
          fileName: 'uuid-user-file-2.pdf',
          mimeType: 'application/pdf',
          fileType: 'document',
          fileSize: '4096',
          s3Key: 'uploads/test-user/file-2.pdf',
          s3Bucket: 'test-bucket',
          status: 'completed',
          uploadProgress: '100',
          isValidated: true,
          isPublic: true,
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          userId: testData.adminUser.id, // Different user's file
          originalName: 'admin-file.txt',
          fileName: 'uuid-admin-file.txt',
          mimeType: 'text/plain',
          fileType: 'document',
          fileSize: '1024',
          s3Key: 'uploads/admin-user/file.txt',
          s3Bucket: 'test-bucket',
          status: 'completed',
          uploadProgress: '100',
          isValidated: true,
          isPublic: false,
          metadata: {},
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]);
    });

    it('should return only the users own files', async () => {
      const result = await service.getUserFiles(testData.testUser.id);

      expect(result).toHaveLength(2);
      expect(result.every(file => file.userId === testData.testUser.id)).toBe(true);
      expect(result.map(f => f.originalName)).toEqual(
        expect.arrayContaining(['user-file-1.jpg', 'user-file-2.pdf'])
      );
    });

    it('should return empty array for user with no files', async () => {
      // Use a UUID that doesn't have any files
      const emptyUserId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479';

      const result = await service.getUserFiles(emptyUserId);

      expect(result).toHaveLength(0);
    });
  });

  describe('getDownloadUrl', () => {
    let testFileId: string;
    let publicFileId: string;
    let expiredFileId: string;

    beforeEach(async () => {
      // Create test files
      const [privateFile] = await testDb.insert(fileUploads).values({
        userId: testData.testUser.id,
        originalName: 'private-file.jpg',
        fileName: 'uuid-private-file.jpg',
        mimeType: 'image/jpeg',
        fileType: 'image',
        fileSize: '2048',
        s3Key: 'uploads/test-user/private-file.jpg',
        s3Bucket: 'test-bucket',
        status: 'completed',
        uploadProgress: '100',
        isValidated: true,
        isPublic: false,
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();

      const [publicFile] = await testDb.insert(fileUploads).values({
        userId: testData.testUser.id,
        originalName: 'public-file.jpg',
        fileName: 'uuid-public-file.jpg',
        mimeType: 'image/jpeg',
        fileType: 'image',
        fileSize: '2048',
        s3Key: 'uploads/test-user/public-file.jpg',
        s3Bucket: 'test-bucket',
        status: 'completed',
        uploadProgress: '100',
        isValidated: true,
        isPublic: true,
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();

      const [expiredFile] = await testDb.insert(fileUploads).values({
        userId: testData.testUser.id,
        originalName: 'expired-file.jpg',
        fileName: 'uuid-expired-file.jpg',
        mimeType: 'image/jpeg',
        fileType: 'image',
        fileSize: '2048',
        s3Key: 'uploads/test-user/expired-file.jpg',
        s3Bucket: 'test-bucket',
        status: 'completed',
        uploadProgress: '100',
        isValidated: true,
        isPublic: true,
        expiresAt: new Date(Date.now() - 3600000), // Expired 1 hour ago
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();

      testFileId = privateFile.id;
      publicFileId = publicFile.id;
      expiredFileId = expiredFile.id;
    });

    it('should allow user to access their own private file', async () => {
      const result = await service.getDownloadUrl(testFileId, testData.testUser.id);

      expect(result).toBe('https://mock-bucket.s3.amazonaws.com/download-url');
      expect(mockS3Service.getPresignedDownloadUrl).toHaveBeenCalledWith(
        expect.objectContaining({
          key: 'uploads/test-user/private-file.jpg',
        })
      );
    });

    it('should allow access to public files by any user', async () => {
      const result = await service.getDownloadUrl(publicFileId, testData.adminUser.id);

      expect(result).toBe('https://mock-bucket.s3.amazonaws.com/download-url');
    });

    it('should allow access to public files without authentication', async () => {
      const result = await service.getDownloadUrl(publicFileId, null);

      expect(result).toBe('https://mock-bucket.s3.amazonaws.com/download-url');
    });

    it('should deny access to other users private files', async () => {
      await expect(
        service.getDownloadUrl(testFileId, testData.adminUser.id)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.getDownloadUrl(testFileId, testData.adminUser.id)
      ).rejects.toThrow('Access denied');
    });

    it('should deny access to expired files', async () => {
      await expect(
        service.getDownloadUrl(expiredFileId, testData.testUser.id)
      ).rejects.toThrow(BadRequestException);
      await expect(
        service.getDownloadUrl(expiredFileId, testData.testUser.id)
      ).rejects.toThrow('File has expired');
    });

    it('should throw NotFoundException for non-existent files', async () => {
      const nonExistentId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479'; // Valid UUID that doesn't exist
      await expect(
        service.getDownloadUrl(nonExistentId, testData.testUser.id)
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('deleteFile', () => {
    let testFileId: string;

    beforeEach(async () => {
      const [testFile] = await testDb.insert(fileUploads).values({
        userId: testData.testUser.id,
        originalName: 'delete-test.jpg',
        fileName: 'uuid-delete-test.jpg',
        mimeType: 'image/jpeg',
        fileType: 'image',
        fileSize: '2048',
        s3Key: 'uploads/test-user/delete-test.jpg',
        s3Bucket: 'test-bucket',
        status: 'completed',
        uploadProgress: '100',
        isValidated: true,
        isPublic: false,
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();

      testFileId = testFile.id;
    });

    it('should allow user to delete their own file', async () => {
      await service.deleteFile(testFileId, testData.testUser.id);

      // Verify file was soft deleted
      const [deletedFile] = await testDb
        .select()
        .from(fileUploads)
        .where(eq(fileUploads.id, testFileId))
        .limit(1);

      expect(deletedFile.deletedAt).toBeTruthy();

      // Verify S3 delete was called
      expect(mockS3Service.deleteFile).toHaveBeenCalledWith(
        'uploads/test-user/delete-test.jpg',
        'test-bucket'
      );
    });

    it('should deny user from deleting other users files', async () => {
      await expect(
        service.deleteFile(testFileId, testData.adminUser.id)
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.deleteFile(testFileId, testData.adminUser.id)
      ).rejects.toThrow('File not found');
    });

    it('should throw NotFoundException for non-existent file', async () => {
      const nonExistentId = 'f47ac10b-58cc-4372-a567-0e02b2c3d479'; // Valid UUID that doesn't exist
      await expect(
        service.deleteFile(nonExistentId, testData.testUser.id)
      ).rejects.toThrow(NotFoundException);
    });
  });
});
