/**
 * ClientReleasesService Unit Tests
 * Tests the ClientReleasesService business logic with proper typing
 */

import { Test, TestingModule } from '@nestjs/testing';
import { ClientReleasesService } from './client-releases.service.js';
import { S3Service } from './s3.service.js';

describe('ClientReleasesService', () => {
  let service: ClientReleasesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ClientReleasesService,
        {
          provide: 'DB',
          useValue: {},
        },
        {
          provide: S3Service,
          useValue: {
            getPresignedDownloadUrl: jest.fn(),
            deleteFile: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ClientReleasesService>(ClientReleasesService);
    jest.clearAllMocks();
  });

  describe('isVersionNewer (private method testing)', () => {
    // Test the private isVersionNewer method by accessing it through bracket notation
    it('should correctly compare semantic versions', () => {
      const isVersionNewer = service['isVersionNewer'].bind(service);

      // Test cases: [newVersion, currentVersion, expected]
      const testCases = [
        ['1.2.0', '1.0.0', true],
        ['2.0.0', '1.9.9', true],
        ['1.0.1', '1.0.0', true],
        ['1.0.0', '1.0.0', false],
        ['1.0.0', '1.0.1', false],
        ['1.0.0', '2.0.0', false],
        ['1.2.3', '1.2.2', true],
        ['1.2.2', '1.2.3', false],
      ];

      testCases.forEach(([newVersion, currentVersion, expected]) => {
        const result = isVersionNewer(newVersion, currentVersion);
        expect(result).toBe(expected);
      });
    });

    it('should handle version strings with v prefix', () => {
      const isVersionNewer = service['isVersionNewer'].bind(service);

      expect(isVersionNewer('v1.2.0', 'v1.0.0')).toBe(true);
      expect(isVersionNewer('v1.0.0', 'v1.2.0')).toBe(false);
      expect(isVersionNewer('v1.0.0', 'v1.0.0')).toBe(false);
    });

    it('should handle mixed version formats', () => {
      const isVersionNewer = service['isVersionNewer'].bind(service);

      expect(isVersionNewer('1.2.0', 'v1.0.0')).toBe(true);
      expect(isVersionNewer('v1.2.0', '1.0.0')).toBe(true);
      expect(isVersionNewer('v1.0.0', '1.0.0')).toBe(false);
    });

    it('should handle different version part lengths', () => {
      const isVersionNewer = service['isVersionNewer'].bind(service);

      expect(isVersionNewer('1.2.0.1', '1.2.0')).toBe(true);
      expect(isVersionNewer('1.2.0', '1.2.0.1')).toBe(false);
      expect(isVersionNewer('1.2', '1.1.9')).toBe(true);
      expect(isVersionNewer('1.2.0.0', '1.2.0')).toBe(false);
    });

    it('should handle edge cases', () => {
      const isVersionNewer = service['isVersionNewer'].bind(service);

      // Empty version parts should be treated as 0
      expect(isVersionNewer('1.0', '1')).toBe(false);
      expect(isVersionNewer('1.1', '1')).toBe(true);
      expect(isVersionNewer('2', '1.9.9')).toBe(true);
    });
  });

  describe('service configuration', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should have database connection', () => {
      expect(service['db']).toBeDefined();
    });
  });

  describe('TauriUpdateResponse format', () => {
    it('should format response correctly for Tauri updater', () => {
      const mockResponse = {
        version: '1.2.0',
        notes: 'Bug fixes and improvements',
        pub_date: '2024-01-01T00:00:00.000Z',
        url: 'https://download-url',
        signature: 'mock-signature-123',
      };

      // Test that the response format matches Tauri's expected structure
      expect(mockResponse).toHaveProperty('version');
      expect(mockResponse).toHaveProperty('notes');
      expect(mockResponse).toHaveProperty('pub_date');
      expect(mockResponse).toHaveProperty('url');
      expect(mockResponse).toHaveProperty('signature');

      expect(typeof mockResponse.version).toBe('string');
      expect(typeof mockResponse.notes).toBe('string');
      expect(typeof mockResponse.pub_date).toBe('string');
      expect(typeof mockResponse.url).toBe('string');
      expect(typeof mockResponse.signature).toBe('string');
    });
  });
});
