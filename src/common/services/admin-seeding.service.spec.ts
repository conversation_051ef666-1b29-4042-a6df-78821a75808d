import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import * as bcrypt from 'bcrypt';
import { AdminSeedingService } from './admin-seeding.service';

// Mock bcrypt
jest.mock('bcrypt');
const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;

describe('AdminSeedingService', () => {
  let service: AdminSeedingService;
  let mockDb: any;
  let mockConfigService: jest.Mocked<ConfigService>;
  let loggerLogSpy: jest.SpyInstance;
  let loggerErrorSpy: jest.SpyInstance;

  beforeEach(async () => {
    // Mock database with chainable methods
    mockDb = {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      returning: jest.fn().mockResolvedValue([]),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
    };

    // Mock ConfigService
    mockConfigService = {
      get: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminSeedingService,
        {
          provide: 'DB',
          useValue: mockDb,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<AdminSeedingService>(AdminSeedingService);

    // Spy on the service's logger instance methods
    loggerLogSpy = jest.spyOn(service['logger'], 'log').mockImplementation();
    loggerErrorSpy = jest.spyOn(service['logger'], 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('seedAdminUser', () => {
    beforeEach(() => {
      mockConfigService.get.mockReturnValue('test_admin_password');
      mockedBcrypt.hash.mockResolvedValue('hashed_password' as never);
    });

    it('should skip creation if admin user already exists', async () => {
      // Mock existing admin user
      mockDb.limit.mockResolvedValue([{ 
        id: 1, 
        email: '<EMAIL>',
        name: 'Existing Admin'
      }]);

      await service.seedAdminUser();

      expect(mockDb.select).toHaveBeenCalled();
      expect(mockDb.insert).not.toHaveBeenCalled();
      expect(loggerLogSpy).toHaveBeenCalledWith('✅ Admin user already exists');
    });

    it('should create admin user when none exists', async () => {
      // Mock no existing admin user
      mockDb.limit.mockResolvedValue([]);
      
      // Mock successful user creation
      const mockCreatedUser = {
        id: 1,
        email: '<EMAIL>',
        name: 'RSGlider Administrator'
      };
      mockDb.returning.mockResolvedValue([mockCreatedUser]);

      await service.seedAdminUser();

      expect(mockDb.select).toHaveBeenCalled();
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'hashed_password',
        name: 'RSGlider Administrator',
        roles: ['admin', 'user', 'developer'],
        isActive: true,
        emailVerified: true,
        emailVerificationToken: null,
        twoFactorEnabled: false,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
      expect(mockedBcrypt.hash).toHaveBeenCalledWith('test_admin_password', 12);
      expect(loggerLogSpy).toHaveBeenCalledWith('✅ Admin user created successfully');
    });

    it('should use default password when config is not provided', async () => {
      mockConfigService.get.mockReturnValue('rsglider_admin_password_change_in_production');
      mockDb.limit.mockResolvedValue([]);
      mockDb.returning.mockResolvedValue([{}]);

      await service.seedAdminUser();

      expect(mockConfigService.get).toHaveBeenCalledWith('ADMIN_PASSWORD', 'rsglider_admin_password_change_in_production');
      expect(mockedBcrypt.hash).toHaveBeenCalledWith('rsglider_admin_password_change_in_production', 12);
    });

    it('should handle database errors gracefully', async () => {
      mockDb.limit.mockRejectedValue(new Error('Database connection failed'));

      await service.seedAdminUser();

      expect(loggerErrorSpy).toHaveBeenCalledWith('❌ Failed to seed admin user:', 'Database connection failed');
    });

    it('should handle bcrypt errors gracefully', async () => {
      mockDb.limit.mockResolvedValue([]);
      mockedBcrypt.hash.mockRejectedValue(new Error('Bcrypt failed') as never);

      await service.seedAdminUser();

      expect(loggerErrorSpy).toHaveBeenCalledWith('❌ Failed to seed admin user:', 'Bcrypt failed');
    });
  });

  describe('updateAdminPassword', () => {
    beforeEach(() => {
      mockedBcrypt.hash.mockResolvedValue('new_hashed_password' as never);
    });

    it('should update admin password successfully', async () => {
      const newPassword = 'new_secure_password';

      await service.updateAdminPassword(newPassword);

      expect(mockedBcrypt.hash).toHaveBeenCalledWith(newPassword, 12);
      expect(mockDb.update).toHaveBeenCalled();
      expect(mockDb.set).toHaveBeenCalledWith({
        password: 'new_hashed_password',
        updatedAt: expect.any(Date),
      });
      expect(loggerLogSpy).toHaveBeenCalledWith('✅ Admin password updated in RSGlider database');
    });

    it('should throw error when database update fails', async () => {
      const newPassword = 'new_secure_password';
      mockDb.where.mockRejectedValue(new Error('Database update failed'));

      await expect(service.updateAdminPassword(newPassword)).rejects.toThrow('Database update failed');
    });

    it('should throw error when bcrypt fails', async () => {
      const newPassword = 'new_secure_password';
      mockedBcrypt.hash.mockRejectedValue(new Error('Bcrypt failed') as never);

      await expect(service.updateAdminPassword(newPassword)).rejects.toThrow('Bcrypt failed');
    });
  });

  describe('getAdminUser', () => {
    it('should return admin user when found', async () => {
      const mockAdminUser = {
        id: 1,
        email: '<EMAIL>',
        name: 'RSGlider Administrator',
        roles: ['admin', 'user', 'developer'],
        isActive: true,
        emailVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      mockDb.limit.mockResolvedValue([mockAdminUser]);

      const result = await service.getAdminUser();

      expect(result).toEqual(mockAdminUser);
      expect(mockDb.select).toHaveBeenCalledWith({
        id: expect.anything(),
        email: expect.anything(),
        name: expect.anything(),
        roles: expect.anything(),
        isActive: expect.anything(),
        emailVerified: expect.anything(),
        createdAt: expect.anything(),
        updatedAt: expect.anything(),
      });
    });

    it('should return undefined when admin user not found', async () => {
      mockDb.limit.mockResolvedValue([]);

      const result = await service.getAdminUser();

      expect(result).toBeUndefined();
    });

    it('should handle database errors', async () => {
      mockDb.limit.mockRejectedValue(new Error('Database query failed'));

      await expect(service.getAdminUser()).rejects.toThrow('Database query failed');
    });
  });

  describe('integration scenarios', () => {
    it('should handle complete admin setup flow', async () => {
      mockConfigService.get.mockReturnValue('integration_test_password');
      mockedBcrypt.hash.mockResolvedValue('hashed_integration_password' as never);
      
      // First call - no admin exists
      mockDb.limit.mockResolvedValueOnce([]);
      mockDb.returning.mockResolvedValue([{
        id: 1,
        email: '<EMAIL>',
        name: 'RSGlider Administrator'
      }]);

      await service.seedAdminUser();

      // Verify admin was created
      expect(mockDb.insert).toHaveBeenCalled();
      expect(loggerLogSpy).toHaveBeenCalledWith('🎉 Admin user seeding completed successfully!');
    });
  });
});
