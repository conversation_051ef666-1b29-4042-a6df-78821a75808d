/**
 * DeveloperManagementService Integration Tests
 * Tests the DeveloperManagementService with real database operations
 */

import { BadRequestException, ConflictException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { eq } from 'drizzle-orm';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase,
} from '../../../test/database-setup.js';
import { giteaProfiles, users } from '../../database/schema/index.js';
import { DatabaseService } from './database.service.js';
import { DeveloperManagementService } from './developer-management.service.js';
import { GiteaService } from './gitea.service.js';

describe('DeveloperManagementService Integration Tests', () => {
  let service: DeveloperManagementService;
  let testDb: TestDatabase;
  let testData: any;
  let mockGiteaService: jest.Mocked<GiteaService>;

  const mockGiteaUser = {
    id: 456,
    login: 'testuser',
    username: 'testuser', // Added missing username property
    email: '<EMAIL>',
    full_name: 'Test User',
    avatar_url: 'https://example.com/avatar.jpg',
    description: 'Test bio',
    website: 'https://example.com',
    location: 'Test Location',
    followers_count: 10,
    following_count: 5,
    starred_repos_count: 20,
    language: 'en',
    is_admin: false,
    last_login: '2024-01-01T00:00:00Z',
    created: '2024-01-01T00:00:00Z',
    restricted: false,
    active: true,
    prohibit_login: false,
    visibility: 'public',
  };

  const mockRepositories = [
    {
      id: 1,
      name: 'repo1',
      full_name: 'testuser/repo1',
      private: false,
      owner: {
        id: 456,
        login: 'testuser',
        full_name: 'Test User',
        email: '<EMAIL>',
        avatar_url: 'https://example.com/avatar.jpg',
        language: 'en',
        is_admin: false,
        last_login: '2024-01-01T00:00:00Z',
        created: '2024-01-01T00:00:00Z',
        restricted: false,
        active: true,
        prohibit_login: false,
        location: 'Test Location',
        website: 'https://example.com',
        description: 'Test user',
        visibility: 'public',
        followers_count: 10,
        following_count: 5,
        starred_repos_count: 20,
        username: 'testuser',
      },
      fork: false,
      template: false,
      archived: false,
      empty: false,
      mirror: false,
      size: 1024,
      stars_count: 5,
      forks_count: 2,
      watchers_count: 3,
      open_issues_count: 1,
      open_pr_counter: 0,
      release_counter: 0,
      default_branch: 'main',
      html_url: 'https://gitea.example.com/testuser/repo1',
      clone_url: 'https://gitea.example.com/testuser/repo1.git',
      ssh_url: '*********************:testuser/repo1.git',
      git_url: 'git://gitea.example.com/testuser/repo1.git',
      permissions: { admin: false, push: false, pull: true },
      has_issues: true,
      internal_tracker: { enable_time_tracker: false, allow_only_contributors_to_track_time: false, enable_issue_dependencies: false },
      has_wiki: false,
      has_pull_requests: true,
      has_projects: false,
      ignore_whitespace_conflicts: false,
      allow_merge_commits: true,
      allow_rebase: true,
      allow_rebase_explicit: true,
      allow_squash_merge: true,
      default_merge_style: 'merge',
      avatar_url: '',
      internal: false,
      mirror_interval: '',
      languages_url: 'https://gitea.example.com/api/v1/repos/testuser/repo1/languages',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
      pushed_at: '2024-01-03T00:00:00Z',
      topics: [],
    },
    {
      id: 2,
      name: 'repo2',
      full_name: 'testuser/repo2',
      private: true,
      owner: {
        id: 456,
        login: 'testuser',
        full_name: 'Test User',
        email: '<EMAIL>',
        avatar_url: 'https://example.com/avatar.jpg',
        language: 'en',
        is_admin: false,
        last_login: '2024-01-01T00:00:00Z',
        created: '2024-01-01T00:00:00Z',
        restricted: false,
        active: true,
        prohibit_login: false,
        location: 'Test Location',
        website: 'https://example.com',
        description: 'Test user',
        visibility: 'public',
        followers_count: 10,
        following_count: 5,
        starred_repos_count: 20,
        username: 'testuser',
      },
      fork: false,
      template: false,
      archived: false,
      empty: false,
      mirror: false,
      size: 2048,
      stars_count: 0,
      forks_count: 0,
      watchers_count: 1,
      open_issues_count: 0,
      open_pr_counter: 0,
      release_counter: 0,
      default_branch: 'main',
      html_url: 'https://gitea.example.com/testuser/repo2',
      clone_url: 'https://gitea.example.com/testuser/repo2.git',
      ssh_url: '*********************:testuser/repo2.git',
      git_url: 'git://gitea.example.com/testuser/repo2.git',
      permissions: { admin: true, push: true, pull: true },
      has_issues: true,
      internal_tracker: { enable_time_tracker: false, allow_only_contributors_to_track_time: false, enable_issue_dependencies: false },
      has_wiki: false,
      has_pull_requests: true,
      has_projects: false,
      ignore_whitespace_conflicts: false,
      allow_merge_commits: true,
      allow_rebase: true,
      allow_rebase_explicit: true,
      allow_squash_merge: true,
      default_merge_style: 'merge',
      avatar_url: '',
      internal: false,
      mirror_interval: '',
      languages_url: 'https://gitea.example.com/api/v1/repos/testuser/repo2/languages',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
      pushed_at: '2024-01-03T00:00:00Z',
      topics: [],
    },
  ];

  beforeAll(async () => {
    // Create test database and NestJS module
    testDb = await createTestDatabase();

    // Mock only external services, not database
    mockGiteaService = {
      createUser: jest.fn(),
      getUserByUsername: jest.fn(),
      getUserRepositories: jest.fn(),
      deleteUser: jest.fn(),
    } as unknown as jest.Mocked<GiteaService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeveloperManagementService,
        {
          provide: DatabaseService,
          useValue: { db: testDb },
        },
        {
          provide: GiteaService,
          useValue: mockGiteaService,
        },
      ],
    }).compile();

    service = module.get<DeveloperManagementService>(DeveloperManagementService);
  });

  beforeEach(async () => {
    // Clean database and seed fresh data for each test
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
    jest.clearAllMocks();
  });

  afterAll(async () => {
    // Cleanup database connection
    await closeTestDatabase();
  });

  describe('createDeveloper', () => {
    it('should create developer with auto-provision successfully', async () => {
      mockGiteaService.createUser.mockResolvedValue(mockGiteaUser);

      const options = {
        userId: testData.testUser.id,
        giteaUsername: 'testuser',
        giteaPassword: 'password123',
        autoProvision: true,
      };

      const result = await service.createDeveloper(options);

      expect(result).toMatchObject({
        user: expect.objectContaining({
          id: testData.testUser.id,
          email: testData.testUser.email,
        }),
        isProvisioned: true,
        repositoryCount: 0,
        publishedCount: 0,
      });

      expect(result.giteaProfile).toMatchObject({
        userId: testData.testUser.id,
        giteaUsername: 'testuser',
        isProvisioned: true,
        syncStatus: 'completed',
      });

      // Verify in database
      const [profile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id))
        .limit(1);

      expect(profile).toMatchObject({
        userId: testData.testUser.id,
        giteaUsername: 'testuser',
        isProvisioned: true,
        syncStatus: 'completed',
      });

      expect(mockGiteaService.createUser).toHaveBeenCalledWith({
        username: 'testuser',
        email: testData.testUser.email,
        password: 'password123',
        full_name: testData.testUser.name,
        send_notify: false,
        must_change_password: true,
        restricted: false,
        visibility: 'public',
      });
    });

    it('should create developer without auto-provision', async () => {
      const options = {
        userId: testData.testUser.id,
        autoProvision: false,
      };

      const result = await service.createDeveloper(options);

      expect(result.isProvisioned).toBe(false);
      expect(mockGiteaService.createUser).not.toHaveBeenCalled();

      // Verify in database
      const [profile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id))
        .limit(1);

      expect(profile).toMatchObject({
        userId: testData.testUser.id,
        isProvisioned: false,
        syncStatus: 'pending',
      });
    });

    it('should throw ConflictException when profile already exists', async () => {
      // First create a profile
      await service.createDeveloper({
        userId: testData.testUser.id,
        autoProvision: false,
      });

      // Try to create again
      await expect(service.createDeveloper({
        userId: testData.testUser.id,
        autoProvision: false,
      })).rejects.toThrow(ConflictException);
      await expect(service.createDeveloper({
        userId: testData.testUser.id,
        autoProvision: false,
      })).rejects.toThrow('Developer profile already exists for this user');
    });

    it('should handle Gitea provisioning failure gracefully', async () => {
      mockGiteaService.createUser.mockRejectedValue(new Error('Gitea error'));

      const options = {
        userId: testData.testUser.id,
        autoProvision: true,
      };

      const result = await service.createDeveloper(options);

      expect(result.isProvisioned).toBe(false);

      // Verify error was stored in database
      const [profile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id))
        .limit(1);

      expect(profile).toMatchObject({
        isProvisioned: false,
        syncStatus: 'failed',
        syncErrors: ['Gitea error'],
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      const nonExistentUserId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

      await expect(service.createDeveloper({
        userId: nonExistentUserId,
        autoProvision: false,
      })).rejects.toThrow(NotFoundException);
      await expect(service.createDeveloper({
        userId: nonExistentUserId,
        autoProvision: false,
      })).rejects.toThrow('User not found');
    });
  });

  describe('getDeveloperProfile', () => {
    it('should return developer profile when found', async () => {
      // Create a profile first
      await service.createDeveloper({
        userId: testData.testUser.id,
        autoProvision: false,
      });

      const result = await service.getDeveloperProfile(testData.testUser.id);

      expect(result).toMatchObject({
        user: expect.objectContaining({
          id: testData.testUser.id,
          email: testData.testUser.email,
        }),
        isProvisioned: false,
        repositoryCount: 0,
        publishedCount: 0,
      });
    });

    it('should return null when profile not found', async () => {
      const result = await service.getDeveloperProfile(testData.testUser.id);
      expect(result).toBeNull();
    });

    it('should throw NotFoundException when user not found', async () => {
      const nonExistentUserId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

      await expect(service.getDeveloperProfile(nonExistentUserId)).rejects.toThrow(NotFoundException);
      await expect(service.getDeveloperProfile(nonExistentUserId)).rejects.toThrow('User not found');
    });
  });

  describe('provisionGiteaAccount', () => {
    beforeEach(async () => {
      // Create unprovisioned profile for each test
      await service.createDeveloper({
        userId: testData.testUser.id,
        autoProvision: false,
      });
    });

    it('should provision Gitea account successfully', async () => {
      mockGiteaService.createUser.mockResolvedValue(mockGiteaUser);

      const result = await service.provisionGiteaAccount(testData.testUser.id, 'password123');

      expect(result.isProvisioned).toBe(true);
      expect(mockGiteaService.createUser).toHaveBeenCalled();

      // Verify in database
      const [profile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id))
        .limit(1);

      expect(profile).toMatchObject({
        isProvisioned: true,
        syncStatus: 'completed',
        giteaUserId: mockGiteaUser.id,
      });
    });

    it('should throw NotFoundException when profile not found', async () => {
      await expect(service.provisionGiteaAccount(testData.adminUser.id)).rejects.toThrow(NotFoundException);
      await expect(service.provisionGiteaAccount(testData.adminUser.id)).rejects.toThrow('Developer profile not found');
    });

    it('should throw ConflictException when already provisioned', async () => {
      // First provision the account
      mockGiteaService.createUser.mockResolvedValue(mockGiteaUser);
      await service.provisionGiteaAccount(testData.testUser.id);

      // Try to provision again
      await expect(service.provisionGiteaAccount(testData.testUser.id)).rejects.toThrow(ConflictException);
      await expect(service.provisionGiteaAccount(testData.testUser.id)).rejects.toThrow('Gitea account already provisioned');
    });

    it('should handle Gitea provisioning failure', async () => {
      mockGiteaService.createUser.mockRejectedValue(new Error('Gitea error'));

      await expect(service.provisionGiteaAccount(testData.testUser.id)).rejects.toThrow(BadRequestException);
      await expect(service.provisionGiteaAccount(testData.testUser.id)).rejects.toThrow('Failed to provision Gitea account: Gitea error');

      // Verify error was stored in database
      const [profile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id))
        .limit(1);

      expect(profile).toMatchObject({
        syncStatus: 'failed',
        syncErrors: ['Gitea error'],
      });
    });
  });

  describe('syncDeveloperProfile', () => {
    beforeEach(async () => {
      // Create provisioned profile for each test
      mockGiteaService.createUser.mockResolvedValue(mockGiteaUser);
      await service.createDeveloper({
        userId: testData.testUser.id,
        autoProvision: true,
      });
      jest.clearAllMocks();
    });

    it('should sync developer profile successfully', async () => {
      mockGiteaService.getUserByUsername.mockResolvedValue(mockGiteaUser);
      mockGiteaService.getUserRepositories.mockResolvedValue(mockRepositories);

      const result = await service.syncDeveloperProfile(testData.testUser.id);

      expect(result.isProvisioned).toBe(true);
      expect(result.repositoryCount).toBe(2);

      // Verify in database
      const [profile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id))
        .limit(1);

      expect(profile).toMatchObject({
        totalRepositories: 2,
        publicRepositories: 1,
        privateRepositories: 1,
        syncStatus: 'completed',
      });

      expect(mockGiteaService.getUserByUsername).toHaveBeenCalledWith('testuser');
      expect(mockGiteaService.getUserRepositories).toHaveBeenCalledWith('testuser');
    });

    it('should throw NotFoundException when profile not found', async () => {
      await expect(service.syncDeveloperProfile(testData.adminUser.id)).rejects.toThrow(NotFoundException);
      await expect(service.syncDeveloperProfile(testData.adminUser.id)).rejects.toThrow('Provisioned developer profile not found');
    });

    it('should handle sync failure', async () => {
      mockGiteaService.getUserByUsername.mockRejectedValue(new Error('Sync error'));

      await expect(service.syncDeveloperProfile(testData.testUser.id)).rejects.toThrow(BadRequestException);
      await expect(service.syncDeveloperProfile(testData.testUser.id)).rejects.toThrow('Failed to sync developer profile: Sync error');

      // Verify error was stored in database
      const [profile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id))
        .limit(1);

      expect(profile).toMatchObject({
        syncStatus: 'failed',
        syncErrors: ['Sync error'],
      });
    });
  });

  describe('deleteDeveloper', () => {
    beforeEach(async () => {
      // Create provisioned profile for each test
      mockGiteaService.createUser.mockResolvedValue(mockGiteaUser);
      await service.createDeveloper({
        userId: testData.testUser.id,
        autoProvision: true,
      });
      jest.clearAllMocks();
    });

    it('should delete developer profile without Gitea account', async () => {
      await service.deleteDeveloper(testData.testUser.id, false);

      expect(mockGiteaService.deleteUser).not.toHaveBeenCalled();

      // Verify profile was deleted from database
      const profiles = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id));

      expect(profiles).toHaveLength(0);
    });

    it('should delete developer profile with Gitea account', async () => {
      mockGiteaService.deleteUser.mockResolvedValue(undefined);

      await service.deleteDeveloper(testData.testUser.id, true);

      expect(mockGiteaService.deleteUser).toHaveBeenCalledWith('testuser');

      // Verify profile was deleted from database
      const profiles = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id));

      expect(profiles).toHaveLength(0);
    });

    it('should continue deletion even if Gitea deletion fails', async () => {
      mockGiteaService.deleteUser.mockRejectedValue(new Error('Gitea delete error'));

      await service.deleteDeveloper(testData.testUser.id, true);

      expect(mockGiteaService.deleteUser).toHaveBeenCalledWith('testuser');

      // Verify profile was still deleted from database
      const profiles = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id));

      expect(profiles).toHaveLength(0);
    });

    it('should throw NotFoundException when profile not found', async () => {
      await expect(service.deleteDeveloper(testData.adminUser.id)).rejects.toThrow(NotFoundException);
      await expect(service.deleteDeveloper(testData.adminUser.id)).rejects.toThrow('Developer profile not found');
    });
  });

  describe('private helper methods', () => {
    it('should generate username from email', () => {
      const username = service['generateGiteaUsername']('<EMAIL>');
      expect(username).toMatch(/^test[a-z0-9]{4}$/);
    });

    it('should generate username from name', () => {
      const username = service['generateGiteaUsername']('Test User Name');
      expect(username).toMatch(/^testusername[a-z0-9]{4}$/);
    });

    it('should generate random password', () => {
      const password = service['generateRandomPassword']();
      expect(password).toHaveLength(16);
      expect(password).toMatch(/^[A-Za-z0-9!@#$%^&*]+$/);
    });
  });

  describe('edge cases', () => {
    it('should handle user with no name', async () => {
      // Update test user to have no name
      await testDb
        .update(users)
        .set({ name: null })
        .where(eq(users.id, testData.testUser.id));

      const result = await service.createDeveloper({
        userId: testData.testUser.id,
        autoProvision: false,
      });

      expect(result).toBeDefined();
      expect(result.giteaProfile?.giteaFullName).toMatch(/^test[a-z0-9]{4}$/);
    });

    it('should handle empty repository list in sync', async () => {
      mockGiteaService.createUser.mockResolvedValue(mockGiteaUser);
      await service.createDeveloper({
        userId: testData.testUser.id,
        autoProvision: true,
      });

      mockGiteaService.getUserByUsername.mockResolvedValue(mockGiteaUser);
      mockGiteaService.getUserRepositories.mockResolvedValue([]);

      const result = await service.syncDeveloperProfile(testData.testUser.id);

      expect(result.repositoryCount).toBe(0);

      // Verify in database
      const [profile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.userId, testData.testUser.id))
        .limit(1);

      expect(profile).toMatchObject({
        totalRepositories: 0,
        publicRepositories: 0,
        privateRepositories: 0,
      });
    });
  });
});
