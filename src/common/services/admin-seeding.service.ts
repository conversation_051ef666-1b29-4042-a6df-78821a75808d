import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { eq } from 'drizzle-orm';
import { users } from '../../database/schema/users.schema.js';

@Injectable()
export class AdminSeedingService {
  private readonly logger = new Logger(AdminSeedingService.name);

  constructor(
    @Inject('DB') private readonly db: any,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Seeds the admin user and syncs with Gite<PERSON>
   * This runs during application startup
   */
  async seedAdminUser(): Promise<void> {
    try {
      const adminEmail = '<EMAIL>';
      const adminPassword = this.configService.get('ADMIN_PASSWORD', 'rsglider_admin_password_change_in_production');
      const adminName = 'RSGlider Administrator';

      this.logger.log('🌱 Checking for admin user...');

      // Check if admin user already exists
      const [existingAdmin] = await this.db
        .select()
        .from(users)
        .where(eq(users.email, adminEmail))
        .limit(1);

      if (existingAdmin) {
        this.logger.log('✅ Admin user already exists');
        return;
      }

      this.logger.log('👤 Creating admin user...');

      // Hash the admin password
      const hashedPassword = await bcrypt.hash(adminPassword, 12);

      // Create admin user in RSGlider database
      const [] = await this.db.insert(users).values({
        email: adminEmail,
        password: hashedPassword,
        name: adminName,
        roles: ['admin', 'user', 'developer'], // Give admin all roles
        isActive: true,
        emailVerified: true, // Admin is pre-verified
        emailVerificationToken: null,
        twoFactorEnabled: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      }).returning();

      this.logger.log('✅ Admin user created successfully');
      this.logger.log('🎉 Admin user seeding completed successfully!');
      this.logger.log(`📋 Admin Credentials:`);
      this.logger.log(`   Email: ${adminEmail}`);
      this.logger.log(`   Password: ${adminPassword}`);
      this.logger.log(`   Roles: admin, user, developer`);

    } catch (error) {
      this.logger.error('❌ Failed to seed admin user:', error.message);
      // Don't throw - let the app start even if admin seeding fails
    }
  }


  /**
   * Updates admin user password in both RSGlider and Gitea
   */
  async updateAdminPassword(newPassword: string): Promise<void> {
    try {
      const adminEmail = '<EMAIL>';
      
      // Update in RSGlider database
      const hashedPassword = await bcrypt.hash(newPassword, 12);
      await this.db
        .update(users)
        .set({ 
          password: hashedPassword,
          updatedAt: new Date()
        })
        .where(eq(users.email, adminEmail));

      this.logger.log('✅ Admin password updated in RSGlider database');

    } catch (error) {
      this.logger.error('❌ Failed to update admin password:', error.message);
      throw error;
    }
  }

  /**
   * Gets the current admin user
   */
  async getAdminUser(): Promise<any> {
    const [adminUser] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        roles: users.roles,
        isActive: users.isActive,
        emailVerified: users.emailVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .where(eq(users.email, '<EMAIL>'))
      .limit(1);

    return adminUser;
  }
}
