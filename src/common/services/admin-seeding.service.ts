import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { eq } from 'drizzle-orm';
import { users } from '../../database/schema/users.schema.js';

@Injectable()
export class AdminSeedingService {
  private readonly logger = new Logger(AdminSeedingService.name);

  constructor(
    @Inject('DB') private readonly db: any,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Seeds the admin user and test users for local development
   * This runs during application startup
   */
  async seedAdminUser(): Promise<void> {
    try {
      await this.seedAdmin();
      await this.seedDeveloperUser();
      await this.seedRegularUser();
    } catch (error) {
      this.logger.error('❌ Failed to seed users:', error.message);
      // Don't throw - let the app start even if seeding fails
    }
  }

  /**
   * Seeds the admin user (system administrator)
   */
  private async seedAdmin(): Promise<void> {
    const adminEmail = '<EMAIL>';
    const adminPassword = this.configService.get('ADMIN_PASSWORD', 'rsglider_admin_password_change_in_production');
    const adminName = 'RSGlider Administrator';

    this.logger.log('🌱 Checking for admin user...');

    // Check if admin user already exists
    const [existingAdmin] = await this.db
      .select()
      .from(users)
      .where(eq(users.email, adminEmail))
      .limit(1);

    if (existingAdmin) {
      this.logger.log('✅ Admin user already exists');

      // Fix admin roles if they have developer role (they shouldn't)
      if (existingAdmin.roles.includes('developer')) {
        this.logger.log('🔧 Fixing admin roles (removing developer role)...');
        const correctRoles = existingAdmin.roles.filter(role => role !== 'developer');
        await this.db
          .update(users)
          .set({
            roles: correctRoles,
            updatedAt: new Date()
          })
          .where(eq(users.email, adminEmail));
        this.logger.log('✅ Admin roles fixed - admins manage, they don\'t develop');
      }
      return;
    }

    this.logger.log('👤 Creating admin user...');

    // Hash the admin password
    const hashedPassword = await bcrypt.hash(adminPassword, 12);

    // Create admin user in RSGlider database
    await this.db.insert(users).values({
      email: adminEmail,
      password: hashedPassword,
      name: adminName,
      roles: ['admin', 'user'], // Admin manages system, doesn't develop
      isActive: true,
      emailVerified: true, // Admin is pre-verified
      emailVerificationToken: null,
      twoFactorEnabled: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    this.logger.log('✅ Admin user created successfully');
    this.logger.log(`📋 Admin Credentials:`);
    this.logger.log(`   Email: ${adminEmail}`);
    this.logger.log(`   Password: ${adminPassword}`);
    this.logger.log(`   Roles: admin, user (NO developer role)`);
  }

  /**
   * Seeds the developer test user for local development
   */
  private async seedDeveloperUser(): Promise<void> {
    const devEmail = '<EMAIL>';
    const devPassword = 'Password123!';
    const devName = 'RSGlider Developer';

    this.logger.log('🌱 Checking for developer test user...');

    // Check if dev user already exists
    const [existingDev] = await this.db
      .select()
      .from(users)
      .where(eq(users.email, devEmail))
      .limit(1);

    if (existingDev) {
      this.logger.log('✅ Developer test user already exists');
      return;
    }

    this.logger.log('👨‍💻 Creating developer test user...');

    // Hash the dev password
    const hashedPassword = await bcrypt.hash(devPassword, 12);

    // Create dev user in RSGlider database
    await this.db.insert(users).values({
      email: devEmail,
      password: hashedPassword,
      name: devName,
      roles: ['developer', 'user'], // Developer can develop and use the system
      isActive: true,
      emailVerified: true, // Pre-verified for testing
      emailVerificationToken: null,
      twoFactorEnabled: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    this.logger.log('✅ Developer test user created successfully');
    this.logger.log(`📋 Developer Credentials:`);
    this.logger.log(`   Email: ${devEmail}`);
    this.logger.log(`   Password: ${devPassword}`);
    this.logger.log(`   Roles: developer, user`);
    this.logger.log(`🎯 Gitea account will be created when developer role is assigned via API`);
    this.logger.log(`ℹ️ Use admin API to assign developer role to trigger onboarding`);
  }

  /**
   * Seeds the regular test user for local development
   */
  private async seedRegularUser(): Promise<void> {
    const userEmail = '<EMAIL>';
    const userPassword = 'Password123!';
    const userName = 'Test User';

    this.logger.log('🌱 Checking for regular test user...');

    // Check if regular user already exists
    const [existingUser] = await this.db
      .select()
      .from(users)
      .where(eq(users.email, userEmail))
      .limit(1);

    if (existingUser) {
      this.logger.log('✅ Regular test user already exists');
      return;
    }

    this.logger.log('👤 Creating regular test user...');

    // Hash the user password
    const hashedPassword = await bcrypt.hash(userPassword, 12);

    // Create regular user in RSGlider database
    await this.db.insert(users).values({
      email: userEmail,
      password: hashedPassword,
      name: userName,
      roles: ['user'], // Just regular user, no special permissions
      isActive: true,
      emailVerified: true, // Pre-verified for testing
      emailVerificationToken: null,
      twoFactorEnabled: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    this.logger.log('✅ Regular test user created successfully');
    this.logger.log(`📋 Regular User Credentials:`);
    this.logger.log(`   Email: ${userEmail}`);
    this.logger.log(`   Password: ${userPassword}`);
    this.logger.log(`   Roles: user`);
    this.logger.log(`   ℹ️  Regular users don't get Gitea accounts`);

    this.logger.log('\n🎉 All test users seeded successfully!');
    this.logger.log('🧪 Local development environment ready for testing!');
  }


  /**
   * Updates admin user password in both RSGlider and Gitea
   */
  async updateAdminPassword(newPassword: string): Promise<void> {
    try {
      const adminEmail = '<EMAIL>';
      
      // Update in RSGlider database
      const hashedPassword = await bcrypt.hash(newPassword, 12);
      await this.db
        .update(users)
        .set({ 
          password: hashedPassword,
          updatedAt: new Date()
        })
        .where(eq(users.email, adminEmail));

      this.logger.log('✅ Admin password updated in RSGlider database');

    } catch (error) {
      this.logger.error('❌ Failed to update admin password:', error.message);
      throw error;
    }
  }

  /**
   * Gets the current admin user
   */
  async getAdminUser(): Promise<any> {
    const [adminUser] = await this.db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        roles: users.roles,
        isActive: users.isActive,
        emailVerified: users.emailVerified,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .where(eq(users.email, '<EMAIL>'))
      .limit(1);

    return adminUser;
  }
}
