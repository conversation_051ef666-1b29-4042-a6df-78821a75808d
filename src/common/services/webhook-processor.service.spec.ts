/**
 * WebhookProcessorService Unit Tests
 * Tests the WebhookProcessorService with mocked dependencies
 */

import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import * as crypto from 'crypto';
import { RepositorySyncService } from './repository-sync.service.js';
import { WebhookProcessorService } from './webhook-processor.service.js';

describe('WebhookProcessorService', () => {
  let service: WebhookProcessorService;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockRepositorySyncService: jest.Mocked<RepositorySyncService>;
  let mockDb: any;

  const mockGiteaProfile = {
    id: 'profile-123',
    userId: 'user-123',
    giteaUsername: 'testuser',
    isProvisioned: true,
    totalRepositories: 1,
    publicRepositories: 1,
    privateRepositories: 0,
    publishedRepositories: 0,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  };

  const mockWebhookPayload = {
    action: 'created',
    repository: {
      id: 123,
      name: 'test-repo',
      full_name: 'testuser/test-repo',
      owner: {
        id: 456,
        login: 'testuser',
        full_name: 'Test User',
        email: '<EMAIL>',
        avatar_url: 'https://example.com/avatar.jpg',
        language: 'en',
        is_admin: false,
        last_login: '2024-01-01T00:00:00Z',
        created: '2024-01-01T00:00:00Z',
        restricted: false,
        active: true,
        prohibit_login: false,
        location: 'Test Location',
        website: 'https://example.com',
        description: 'Test user',
        visibility: 'public',
        followers_count: 0,
        following_count: 0,
        starred_repos_count: 0,
        username: 'testuser',
      },
      private: false,
      html_url: 'https://gitea.example.com/testuser/test-repo',
      clone_url: 'https://gitea.example.com/testuser/test-repo.git',
      ssh_url: '*********************:testuser/test-repo.git',
      description: 'A test repository',
      website: 'https://example.com',
      stars_count: 0,
      forks_count: 0,
      watchers_count: 0,
      open_issues_count: 0,
      default_branch: 'main',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-02T00:00:00Z',
      pushed_at: '2024-01-03T00:00:00Z',
    },
    sender: {
      id: 456,
      login: 'testuser',
      full_name: 'Test User',
      email: '<EMAIL>',
      avatar_url: 'https://example.com/avatar.jpg',
      language: 'en',
      is_admin: false,
      last_login: '2024-01-01T00:00:00Z',
      created: '2024-01-01T00:00:00Z',
      restricted: false,
      active: true,
      prohibit_login: false,
      location: 'Test Location',
      website: 'https://example.com',
      description: 'Test user',
      visibility: 'public',
      followers_count: 0,
      following_count: 0,
      starred_repos_count: 0,
      username: 'testuser',
    },
  };

  beforeEach(async () => {
    // Create mock database with query builder pattern
    mockDb = {
      select: jest.fn(() => mockDb),
      from: jest.fn(() => mockDb),
      where: jest.fn(() => mockDb),
      limit: jest.fn(() => Promise.resolve([mockGiteaProfile])),
      delete: jest.fn(() => mockDb),
    };

    mockConfigService = {
      get: jest.fn().mockReturnValue('test-secret'),
    } as unknown as jest.Mocked<ConfigService>;

    mockRepositorySyncService = {
      syncRepository: jest.fn(),
    } as unknown as jest.Mocked<RepositorySyncService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookProcessorService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: 'DB',
          useValue: mockDb,
        },
        {
          provide: RepositorySyncService,
          useValue: mockRepositorySyncService,
        },
      ],
    }).compile();

    service = module.get<WebhookProcessorService>(WebhookProcessorService);
    jest.clearAllMocks();
  });

  describe('service configuration', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should have ConfigService injected', () => {
      expect(service['configService']).toBeDefined();
    });

    it('should have database injected', () => {
      expect(service['db']).toBeDefined();
    });

    it('should have RepositorySyncService injected', () => {
      expect(service['repositorySyncService']).toBeDefined();
    });

    it('should initialize webhook secret from config', () => {
      // The webhook secret is initialized in the constructor
      // We can verify this by checking that the service has the webhookSecret property
      expect(service['webhookSecret']).toBeDefined();
    });
  });

  describe('verifyWebhookSignature', () => {
    it('should return true when no webhook secret is configured', async () => {
      // Create a new service instance with no webhook secret
      const noSecretConfigService = { get: jest.fn().mockReturnValue(undefined) };
      const moduleWithNoSecret = await Test.createTestingModule({
        providers: [
          WebhookProcessorService,
          { provide: ConfigService, useValue: noSecretConfigService },
          { provide: 'DB', useValue: mockDb },
          { provide: RepositorySyncService, useValue: mockRepositorySyncService },
        ],
      }).compile();

      const serviceWithNoSecret = moduleWithNoSecret.get<WebhookProcessorService>(WebhookProcessorService);
      const result = serviceWithNoSecret.verifyWebhookSignature('test-payload', 'sha256=signature');

      expect(result).toBe(true);
    });

    it('should return false when no signature is provided', async () => {
      // Create a new service instance with webhook secret
      const secretConfigService = { get: jest.fn().mockReturnValue('test-secret') };
      const moduleWithSecret = await Test.createTestingModule({
        providers: [
          WebhookProcessorService,
          { provide: ConfigService, useValue: secretConfigService },
          { provide: 'DB', useValue: mockDb },
          { provide: RepositorySyncService, useValue: mockRepositorySyncService },
        ],
      }).compile();

      const serviceWithSecret = moduleWithSecret.get<WebhookProcessorService>(WebhookProcessorService);
      const result = serviceWithSecret.verifyWebhookSignature('test-payload', '');

      expect(result).toBe(false);
    });

    it('should return true for valid signature', async () => {
      const payload = 'test-payload';
      const secret = 'test-secret';
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(payload)
        .digest('hex');

      // Create a new service instance with webhook secret
      const secretConfigService = { get: jest.fn().mockReturnValue(secret) };
      const moduleWithSecret = await Test.createTestingModule({
        providers: [
          WebhookProcessorService,
          { provide: ConfigService, useValue: secretConfigService },
          { provide: 'DB', useValue: mockDb },
          { provide: RepositorySyncService, useValue: mockRepositorySyncService },
        ],
      }).compile();

      const serviceWithSecret = moduleWithSecret.get<WebhookProcessorService>(WebhookProcessorService);
      const result = serviceWithSecret.verifyWebhookSignature(payload, `sha256=${expectedSignature}`);

      expect(result).toBe(true);
    });

    it('should return false for invalid signature', async () => {
      // Create a new service instance with webhook secret
      const secretConfigService = { get: jest.fn().mockReturnValue('test-secret') };
      const moduleWithSecret = await Test.createTestingModule({
        providers: [
          WebhookProcessorService,
          { provide: ConfigService, useValue: secretConfigService },
          { provide: 'DB', useValue: mockDb },
          { provide: RepositorySyncService, useValue: mockRepositorySyncService },
        ],
      }).compile();

      const serviceWithSecret = moduleWithSecret.get<WebhookProcessorService>(WebhookProcessorService);
      // Use a properly formatted 64-character hex string (same length as SHA256)
      const invalidSignature = 'a'.repeat(64);
      const result = serviceWithSecret.verifyWebhookSignature('test-payload', `sha256=${invalidSignature}`);

      expect(result).toBe(false);
    });
  });

  describe('processRepositoryWebhook', () => {
    it('should process repository created webhook successfully', async () => {
      mockRepositorySyncService.syncRepository.mockResolvedValue({ created: true });

      const result = await service.processRepositoryWebhook(mockWebhookPayload);

      expect(result).toEqual({
        processed: true,
        action: 'created',
        repository: 'testuser/test-repo',
        message: 'Repository created and synced successfully',
      });
      expect(mockRepositorySyncService.syncRepository).toHaveBeenCalledWith(
        'profile-123',
        mockWebhookPayload.repository
      );
    });

    it('should handle repository deleted webhook', async () => {
      const deletedPayload = { ...mockWebhookPayload, action: 'deleted' };

      const result = await service.processRepositoryWebhook(deletedPayload);

      expect(result).toEqual({
        processed: true,
        action: 'deleted',
        repository: 'testuser/test-repo',
        message: 'Repository deleted and removed from sync',
      });
      expect(mockDb.delete).toHaveBeenCalled();
    });

    it('should handle repository updated webhook', async () => {
      const updatedPayload = { ...mockWebhookPayload, action: 'updated' };
      mockRepositorySyncService.syncRepository.mockResolvedValue({ created: false });

      const result = await service.processRepositoryWebhook(updatedPayload);

      expect(result).toEqual({
        processed: true,
        action: 'updated',
        repository: 'testuser/test-repo',
        message: 'Repository updated and re-synced successfully',
      });
    });

    it('should handle unknown action', async () => {
      const unknownPayload = { ...mockWebhookPayload, action: 'unknown' };

      const result = await service.processRepositoryWebhook(unknownPayload);

      expect(result).toEqual({
        processed: false,
        action: 'unknown',
        repository: 'testuser/test-repo',
        message: 'Unhandled repository action: unknown',
      });
    });

    it('should handle missing developer profile', async () => {
      mockDb.limit.mockResolvedValue([]);

      const result = await service.processRepositoryWebhook(mockWebhookPayload);

      expect(result).toEqual({
        processed: false,
        action: 'created',
        repository: 'testuser/test-repo',
        message: 'Developer profile not found for testuser',
      });
    });

    it('should handle processing errors', async () => {
      mockRepositorySyncService.syncRepository.mockRejectedValue(new Error('Sync failed'));

      const result = await service.processRepositoryWebhook(mockWebhookPayload);

      expect(result).toEqual({
        processed: false,
        action: 'created',
        repository: 'testuser/test-repo',
        message: 'Processing failed: Sync failed',
      });
    });
  });

  describe('processPushWebhook', () => {
    const mockPushPayload = {
      repository: mockWebhookPayload.repository,
      pusher: { login: 'testuser' },
      commits: [
        { added: ['file1.txt'], modified: [], removed: [] },
        { added: [], modified: ['file2.txt'], removed: [] },
      ],
    };

    it('should process push webhook successfully', async () => {
      mockRepositorySyncService.syncRepository.mockResolvedValue({ created: false });

      const result = await service.processPushWebhook(mockPushPayload);

      expect(result).toEqual({
        processed: true,
        action: 'push',
        repository: 'testuser/test-repo',
        message: 'Processed push with 2 commits',
      });
    });

    it('should detect marketplace metadata changes', async () => {
      const marketplacePayload = {
        ...mockPushPayload,
        commits: [
          { added: ['marketplace.json'], modified: [], removed: [] },
        ],
      };

      const result = await service.processPushWebhook(marketplacePayload);

      expect(result.processed).toBe(true);
      expect(result.message).toContain('1 commits');
    });

    it('should handle missing developer profile in push', async () => {
      mockDb.limit.mockResolvedValue([]);

      const result = await service.processPushWebhook(mockPushPayload);

      expect(result).toEqual({
        processed: false,
        action: 'push',
        repository: 'testuser/test-repo',
        message: 'Developer profile not found for testuser',
      });
    });
  });

  describe('processReleaseWebhook', () => {
    const mockReleasePayload = {
      action: 'published',
      repository: mockWebhookPayload.repository,
      release: { tag_name: 'v1.0.0' },
    };

    it('should process release webhook successfully', async () => {
      mockRepositorySyncService.syncRepository.mockResolvedValue({ created: false });

      const result = await service.processReleaseWebhook(mockReleasePayload);

      expect(result).toEqual({
        processed: true,
        action: 'release_published',
        repository: 'testuser/test-repo',
        message: 'Processed release published: v1.0.0',
      });
    });

    it('should handle missing developer profile in release', async () => {
      mockDb.limit.mockResolvedValue([]);

      const result = await service.processReleaseWebhook(mockReleasePayload);

      expect(result).toEqual({
        processed: false,
        action: 'release_published',
        repository: 'testuser/test-repo',
        message: 'Developer profile not found for testuser',
      });
    });
  });

  describe('private methods', () => {
    it('should have findDeveloperProfile method', () => {
      expect(service['findDeveloperProfile']).toBeDefined();
      expect(typeof service['findDeveloperProfile']).toBe('function');
    });

    it('should have handleRepositoryCreated method', () => {
      expect(service['handleRepositoryCreated']).toBeDefined();
      expect(typeof service['handleRepositoryCreated']).toBe('function');
    });

    it('should have handleRepositoryDeleted method', () => {
      expect(service['handleRepositoryDeleted']).toBeDefined();
      expect(typeof service['handleRepositoryDeleted']).toBe('function');
    });

    it('should have handleRepositoryUpdated method', () => {
      expect(service['handleRepositoryUpdated']).toBeDefined();
      expect(typeof service['handleRepositoryUpdated']).toBe('function');
    });
  });
});
