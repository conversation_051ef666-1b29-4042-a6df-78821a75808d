import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '../../database/database.module.js';
import { EmailModule } from '../../email/email.module.js';
import { ClientReleasesService } from './client-releases.service.js';
import { DatabaseService } from './database.service.js';
import { DeveloperManagementService } from './developer-management.service.js';
import { FileUploadsService } from './file-uploads.service.js';
import { GiteaService } from './gitea.service.js';
import { PasswordSyncService } from './password-sync.service.js';
import { RedisService } from './redis.service.js';
import { RepositorySyncService } from './repository-sync.service.js';
import { S3Service } from './s3.service.js';
import { WebhookProcessorService } from './webhook-processor.service.js';

@Global()
@Module({
  imports: [ConfigModule, DatabaseModule, EmailModule],
  providers: [
    DatabaseService,
    RedisService,
    S3Service,
    FileUploadsService,
    ClientReleasesService,
    GiteaService,
    PasswordSyncService,
    DeveloperManagementService,
    RepositorySyncService,
    WebhookProcessorService,
  ],
  exports: [
    DatabaseService,
    RedisService,
    S3Service,
    FileUploadsService,
    ClientReleasesService,
    GiteaService,
    PasswordSyncService,
    DeveloperManagementService,
    RepositorySyncService,
    WebhookProcessorService,
  ],
})
export class ServicesModule { }
