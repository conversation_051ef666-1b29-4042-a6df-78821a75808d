/**
 * RepositorySyncService Integration Tests
 * Tests the RepositorySyncService with real database operations
 */

import { BadRequestException, NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { eq } from 'drizzle-orm';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase,
} from '../../../test/database-setup.js';
import { giteaProfiles, giteaRepositories } from '../../database/schema/index.js';
import { DatabaseService } from './database.service.js';
import { GiteaRepositoryResponseDto, GiteaService } from './gitea.service.js';
import { RepositorySyncService } from './repository-sync.service.js';

describe('RepositorySyncService Integration Tests', () => {
  let service: RepositorySyncService;
  let testDb: TestDatabase;
  let testData: any;
  let mockGiteaService: jest.Mocked<GiteaService>;

  const mockGiteaRepo: GiteaRepositoryResponseDto = {
    id: 123,
    name: 'test-repo',
    full_name: 'testuser/test-repo',
    description: 'A test repository',
    private: false,
    fork: false,
    template: false,
    archived: false,
    empty: false,
    mirror: false,
    size: 1024,
    stars_count: 5,
    forks_count: 2,
    watchers_count: 3,
    open_issues_count: 1,
    open_pr_counter: 0,
    release_counter: 0,
    default_branch: 'main',
    language: 'TypeScript',
    html_url: 'https://gitea.example.com/testuser/test-repo',
    clone_url: 'https://gitea.example.com/testuser/test-repo.git',
    ssh_url: '*********************:testuser/test-repo.git',
    git_url: 'git://gitea.example.com/testuser/test-repo.git',
    permissions: { admin: true, push: true, pull: true },
    has_issues: true,
    internal_tracker: { enable_time_tracker: false, allow_only_contributors_to_track_time: false, enable_issue_dependencies: false },
    has_wiki: false,
    has_pull_requests: true,
    has_projects: false,
    ignore_whitespace_conflicts: false,
    allow_merge_commits: true,
    allow_rebase: true,
    allow_rebase_explicit: true,
    allow_squash_merge: true,
    default_merge_style: 'merge',
    avatar_url: '',
    internal: false,
    mirror_interval: '',
    languages_url: 'https://gitea.example.com/api/v1/repos/testuser/test-repo/languages',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    pushed_at: '2024-01-03T00:00:00Z',
    topics: [],
    owner: {
      id: 456,
      login: 'testuser',
      full_name: 'Test User',
      email: '<EMAIL>',
      avatar_url: 'https://example.com/avatar.jpg',
      language: 'en',
      is_admin: false,
      last_login: '2024-01-01T00:00:00Z',
      created: '2024-01-01T00:00:00Z',
      restricted: false,
      active: true,
      prohibit_login: false,
      location: 'Test Location',
      website: 'https://example.com',
      description: 'Test user',
      visibility: 'public',
      followers_count: 10,
      following_count: 5,
      starred_repos_count: 20,
      username: 'testuser',
    },
  };

  const mockPrivateGiteaRepo: GiteaRepositoryResponseDto = {
    ...mockGiteaRepo,
    id: 124,
    name: 'private-repo',
    full_name: 'testuser/private-repo',
    description: 'A private test repository',
    private: true,
    html_url: 'https://gitea.example.com/testuser/private-repo',
    clone_url: 'https://gitea.example.com/testuser/private-repo.git',
    ssh_url: '*********************:testuser/private-repo.git',
    git_url: 'git://gitea.example.com/testuser/private-repo.git',
    languages_url: 'https://gitea.example.com/api/v1/repos/testuser/private-repo/languages',
  };

  beforeAll(async () => {
    // Create test database and NestJS module
    testDb = await createTestDatabase();

    // Mock only external services, not database
    mockGiteaService = {
      getUserRepositories: jest.fn(),
    } as unknown as jest.Mocked<GiteaService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RepositorySyncService,
        {
          provide: DatabaseService,
          useValue: { db: testDb },
        },
        {
          provide: GiteaService,
          useValue: mockGiteaService,
        },
      ],
    }).compile();

    service = module.get<RepositorySyncService>(RepositorySyncService);
  });

  beforeEach(async () => {
    // Clean database and seed fresh data for each test
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
    jest.clearAllMocks();

    // Create a test Gitea profile
    const [giteaProfile] = await testDb
      .insert(giteaProfiles)
      .values({
        userId: testData.testUser.id,
        giteaUserId: 456,
        giteaUsername: 'testuser',
        giteaEmail: '<EMAIL>',
        giteaFullName: 'Test User',
        giteaAvatarUrl: 'https://example.com/avatar.jpg',
        isActive: true,
        isProvisioned: true,
        syncStatus: 'completed',
        lastSyncAt: new Date(),
        totalRepositories: 0,
        publicRepositories: 0,
        privateRepositories: 0,
        publishedRepositories: 0,
        giteaProfile: {
          bio: 'Test bio',
          website: 'https://example.com',
          location: 'Test Location',
          followers: 10,
          following: 5,
          starredRepos: 20,
        },
      })
      .returning();

    testData.giteaProfile = giteaProfile;
  });

  afterAll(async () => {
    // Cleanup database connection
    await closeTestDatabase();
  });

  describe('syncDeveloperRepositories', () => {
    it('should sync repositories successfully', async () => {
      mockGiteaService.getUserRepositories.mockResolvedValue([mockGiteaRepo, mockPrivateGiteaRepo]);

      const result = await service.syncDeveloperRepositories(testData.testUser.id);

      expect(result).toMatchObject({
        synced: 2,
        created: 2,
        updated: 0,
        errors: [],
      });

      // Verify repositories were created in database
      const repos = await testDb
        .select()
        .from(giteaRepositories)
        .where(eq(giteaRepositories.giteaProfileId, testData.giteaProfile.id));

      expect(repos).toHaveLength(2);
      expect(repos[0]).toMatchObject({
        name: 'test-repo',
        fullName: 'testuser/test-repo',
        visibility: 'public',
        syncStatus: 'completed',
      });
      expect(repos[1]).toMatchObject({
        name: 'private-repo',
        fullName: 'testuser/private-repo',
        visibility: 'private',
        syncStatus: 'completed',
      });

      // Verify profile stats were updated
      const [updatedProfile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.id, testData.giteaProfile.id))
        .limit(1);

      expect(updatedProfile).toMatchObject({
        totalRepositories: 2,
        publicRepositories: 1,
        privateRepositories: 1,
        publishedRepositories: 0,
      });

      expect(mockGiteaService.getUserRepositories).toHaveBeenCalledWith('testuser');
    });

    it('should update existing repositories', async () => {
      // First sync to create repositories
      mockGiteaService.getUserRepositories.mockResolvedValue([mockGiteaRepo]);
      await service.syncDeveloperRepositories(testData.testUser.id);

      // Update the mock repo data
      const updatedMockRepo = {
        ...mockGiteaRepo,
        description: 'Updated description',
        stars_count: 10,
        forks_count: 5,
      };

      mockGiteaService.getUserRepositories.mockResolvedValue([updatedMockRepo]);

      const result = await service.syncDeveloperRepositories(testData.testUser.id);

      expect(result).toMatchObject({
        synced: 1,
        created: 0,
        updated: 1,
        errors: [],
      });

      // Verify repository was updated
      const [repo] = await testDb
        .select()
        .from(giteaRepositories)
        .where(eq(giteaRepositories.giteaProfileId, testData.giteaProfile.id))
        .limit(1);

      expect(repo).toMatchObject({
        description: 'Updated description',
        starsCount: 10,
        forksCount: 5,
      });
    });

    it('should handle sync errors gracefully', async () => {
      // Create a spy on the syncRepository method to simulate an error
      const syncRepositorySpy = jest.spyOn(service, 'syncRepository');
      syncRepositorySpy
        .mockResolvedValueOnce({ created: true }) // First repo succeeds
        .mockRejectedValueOnce(new Error('Database error')); // Second repo fails

      const errorRepo = { ...mockGiteaRepo, id: 124, name: 'error-repo', full_name: 'testuser/error-repo' };
      mockGiteaService.getUserRepositories.mockResolvedValue([mockGiteaRepo, errorRepo]);

      const result = await service.syncDeveloperRepositories(testData.testUser.id);

      expect(result).toMatchObject({
        synced: 1,
        created: 1,
        updated: 0,
        errors: ['testuser/error-repo: Database error'],
      });

      // Restore the spy
      syncRepositorySpy.mockRestore();
    });

    it('should throw BadRequestException when profile not provisioned', async () => {
      // Update profile to not be provisioned
      await testDb
        .update(giteaProfiles)
        .set({ isProvisioned: false })
        .where(eq(giteaProfiles.id, testData.giteaProfile.id));

      await expect(service.syncDeveloperRepositories(testData.testUser.id)).rejects.toThrow(BadRequestException);
      await expect(service.syncDeveloperRepositories(testData.testUser.id)).rejects.toThrow('Developer profile not provisioned with Gitea');
    });

    it('should throw NotFoundException when profile not found', async () => {
      const nonExistentUserId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

      await expect(service.syncDeveloperRepositories(nonExistentUserId)).rejects.toThrow(NotFoundException);
      await expect(service.syncDeveloperRepositories(nonExistentUserId)).rejects.toThrow('Developer profile not found');
    });

    it('should handle Gitea service errors', async () => {
      mockGiteaService.getUserRepositories.mockRejectedValue(new Error('Gitea API error'));

      await expect(service.syncDeveloperRepositories(testData.testUser.id)).rejects.toThrow(BadRequestException);
      await expect(service.syncDeveloperRepositories(testData.testUser.id)).rejects.toThrow('Failed to sync repositories: Gitea API error');
    });
  });

  describe('getDeveloperRepositories', () => {
    beforeEach(async () => {
      // Create test repositories
      await testDb.insert(giteaRepositories).values([
        {
          giteaProfileId: testData.giteaProfile.id,
          giteaRepoId: 123,
          name: 'public-repo',
          fullName: 'testuser/public-repo',
          description: 'A public repository',
          visibility: 'public',
          isFork: false,
          isTemplate: false,
          isArchived: false,
          isEmpty: false,
          size: 1024,
          starsCount: 5,
          forksCount: 2,
          watchersCount: 3,
          openIssuesCount: 1,
          defaultBranch: 'main',
          language: 'TypeScript',
          topics: [],
          htmlUrl: 'https://gitea.example.com/testuser/public-repo',
          cloneUrl: 'https://gitea.example.com/testuser/public-repo.git',
          sshUrl: '*********************:testuser/public-repo.git',
          hasMarketplaceMetadata: false,
          marketplaceMetadata: null,
          isPublished: false,
          marketplaceItemId: null,
          syncStatus: 'completed',
          lastSyncAt: new Date(),
          giteaCreatedAt: new Date('2024-01-01'),
          giteaUpdatedAt: new Date('2024-01-02'),
          giteaPushedAt: new Date('2024-01-03'),
        },
        {
          giteaProfileId: testData.giteaProfile.id,
          giteaRepoId: 124,
          name: 'private-repo',
          fullName: 'testuser/private-repo',
          description: 'A private repository',
          visibility: 'private',
          isFork: false,
          isTemplate: false,
          isArchived: false,
          isEmpty: false,
          size: 2048,
          starsCount: 0,
          forksCount: 0,
          watchersCount: 1,
          openIssuesCount: 0,
          defaultBranch: 'main',
          language: 'JavaScript',
          topics: [],
          htmlUrl: 'https://gitea.example.com/testuser/private-repo',
          cloneUrl: 'https://gitea.example.com/testuser/private-repo.git',
          sshUrl: '*********************:testuser/private-repo.git',
          hasMarketplaceMetadata: false,
          marketplaceMetadata: null,
          isPublished: false,
          marketplaceItemId: null,
          syncStatus: 'completed',
          lastSyncAt: new Date(),
          giteaCreatedAt: new Date('2024-01-01'),
          giteaUpdatedAt: new Date('2024-01-02'),
          giteaPushedAt: new Date('2024-01-03'),
        },
      ]);
    });

    it('should return all repositories when includePrivate is true', async () => {
      const result = await service.getDeveloperRepositories(testData.testUser.id, true);

      expect(result).toHaveLength(2);
      expect(result.map(r => r.name)).toEqual(expect.arrayContaining(['public-repo', 'private-repo']));
    });

    it('should return only public repositories when includePrivate is false', async () => {
      const result = await service.getDeveloperRepositories(testData.testUser.id, false);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        name: 'public-repo',
        visibility: 'public',
      });
    });

    it('should return all repositories by default', async () => {
      const result = await service.getDeveloperRepositories(testData.testUser.id);

      expect(result).toHaveLength(2);
    });

    it('should throw NotFoundException when profile not found', async () => {
      const nonExistentUserId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

      await expect(service.getDeveloperRepositories(nonExistentUserId)).rejects.toThrow(NotFoundException);
      await expect(service.getDeveloperRepositories(nonExistentUserId)).rejects.toThrow('Developer profile not found');
    });
  });

  describe('getRepository', () => {
    let repositoryId: string;

    beforeEach(async () => {
      const [repo] = await testDb.insert(giteaRepositories).values({
        giteaProfileId: testData.giteaProfile.id,
        giteaRepoId: 123,
        name: 'test-repo',
        fullName: 'testuser/test-repo',
        description: 'A test repository',
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 1024,
        starsCount: 5,
        forksCount: 2,
        watchersCount: 3,
        openIssuesCount: 1,
        defaultBranch: 'main',
        language: 'TypeScript',
        topics: [],
        htmlUrl: 'https://gitea.example.com/testuser/test-repo',
        cloneUrl: 'https://gitea.example.com/testuser/test-repo.git',
        sshUrl: '*********************:testuser/test-repo.git',
        hasMarketplaceMetadata: false,
        marketplaceMetadata: null,
        isPublished: false,
        marketplaceItemId: null,
        syncStatus: 'completed',
        lastSyncAt: new Date(),
        giteaCreatedAt: new Date('2024-01-01'),
        giteaUpdatedAt: new Date('2024-01-02'),
        giteaPushedAt: new Date('2024-01-03'),
      }).returning();

      repositoryId = repo.id;
    });

    it('should return repository when found', async () => {
      const result = await service.getRepository(repositoryId);

      expect(result).toMatchObject({
        id: repositoryId,
        name: 'test-repo',
        fullName: 'testuser/test-repo',
        visibility: 'public',
      });
    });

    it('should throw NotFoundException when repository not found', async () => {
      const nonExistentRepoId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

      await expect(service.getRepository(nonExistentRepoId)).rejects.toThrow(NotFoundException);
      await expect(service.getRepository(nonExistentRepoId)).rejects.toThrow('Repository not found');
    });
  });

  describe('updateRepositoryMarketplaceMetadata', () => {
    let repositoryId: string;

    beforeEach(async () => {
      const [repo] = await testDb.insert(giteaRepositories).values({
        giteaProfileId: testData.giteaProfile.id,
        giteaRepoId: 123,
        name: 'test-repo',
        fullName: 'testuser/test-repo',
        description: 'A test repository',
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 1024,
        starsCount: 5,
        forksCount: 2,
        watchersCount: 3,
        openIssuesCount: 1,
        defaultBranch: 'main',
        language: 'TypeScript',
        topics: [],
        htmlUrl: 'https://gitea.example.com/testuser/test-repo',
        cloneUrl: 'https://gitea.example.com/testuser/test-repo.git',
        sshUrl: '*********************:testuser/test-repo.git',
        hasMarketplaceMetadata: false,
        marketplaceMetadata: null,
        isPublished: false,
        marketplaceItemId: null,
        syncStatus: 'completed',
        lastSyncAt: new Date(),
        giteaCreatedAt: new Date('2024-01-01'),
        giteaUpdatedAt: new Date('2024-01-02'),
        giteaPushedAt: new Date('2024-01-03'),
      }).returning();

      repositoryId = repo.id;
    });

    it('should update marketplace metadata successfully', async () => {
      const metadata = {
        name: 'Test Plugin',
        description: 'A test plugin for the marketplace',
        version: '1.0.0',
        author: 'Test Author',
        category: 'utility',
        tags: ['test', 'plugin'],
        pricing: {
          type: 'free' as const,
        },
        compatibility: {
          minVersion: '1.0.0',
          maxVersion: '2.0.0',
        },
        features: ['feature1', 'feature2'],
        screenshots: ['screenshot1.png', 'screenshot2.png'],
        documentation: 'https://example.com/docs',
        support: 'https://example.com/support',
        license: 'MIT',
      };

      const result = await service.updateRepositoryMarketplaceMetadata(repositoryId, metadata);

      expect(result).toMatchObject({
        id: repositoryId,
        hasMarketplaceMetadata: true,
        marketplaceMetadata: metadata,
      });

      // Verify in database
      const [updatedRepo] = await testDb
        .select()
        .from(giteaRepositories)
        .where(eq(giteaRepositories.id, repositoryId))
        .limit(1);

      expect(updatedRepo).toMatchObject({
        hasMarketplaceMetadata: true,
        marketplaceMetadata: metadata,
      });
    });

    it('should throw NotFoundException when repository not found', async () => {
      const nonExistentRepoId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';
      const metadata = {
        name: 'Test Plugin',
        description: 'A test plugin',
        version: '1.0.0',
        author: 'Test Author',
        category: 'utility',
        tags: [],
        pricing: { type: 'free' as const },
        compatibility: { minVersion: '1.0.0', maxVersion: '2.0.0' },
        features: [],
        screenshots: [],
        documentation: '',
        support: '',
        license: 'MIT',
      };

      await expect(service.updateRepositoryMarketplaceMetadata(nonExistentRepoId, metadata)).rejects.toThrow(NotFoundException);
      await expect(service.updateRepositoryMarketplaceMetadata(nonExistentRepoId, metadata)).rejects.toThrow('Repository not found');
    });
  });

  describe('markRepositoryAsPublished', () => {
    let repositoryId: string;

    beforeEach(async () => {
      const [repo] = await testDb.insert(giteaRepositories).values({
        giteaProfileId: testData.giteaProfile.id,
        giteaRepoId: 123,
        name: 'test-repo',
        fullName: 'testuser/test-repo',
        description: 'A test repository',
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 1024,
        starsCount: 5,
        forksCount: 2,
        watchersCount: 3,
        openIssuesCount: 1,
        defaultBranch: 'main',
        language: 'TypeScript',
        topics: [],
        htmlUrl: 'https://gitea.example.com/testuser/test-repo',
        cloneUrl: 'https://gitea.example.com/testuser/test-repo.git',
        sshUrl: '*********************:testuser/test-repo.git',
        hasMarketplaceMetadata: false,
        marketplaceMetadata: null,
        isPublished: false,
        marketplaceItemId: null,
        syncStatus: 'completed',
        lastSyncAt: new Date(),
        giteaCreatedAt: new Date('2024-01-01'),
        giteaUpdatedAt: new Date('2024-01-02'),
        giteaPushedAt: new Date('2024-01-03'),
      }).returning();

      repositoryId = repo.id;
    });

    it('should mark repository as published successfully', async () => {
      const marketplaceItemId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';

      const result = await service.markRepositoryAsPublished(repositoryId, marketplaceItemId);

      expect(result).toMatchObject({
        id: repositoryId,
        isPublished: true,
        marketplaceItemId,
      });

      // Verify in database
      const [updatedRepo] = await testDb
        .select()
        .from(giteaRepositories)
        .where(eq(giteaRepositories.id, repositoryId))
        .limit(1);

      expect(updatedRepo).toMatchObject({
        isPublished: true,
        marketplaceItemId,
      });

      // Verify profile stats were updated
      const [updatedProfile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.id, testData.giteaProfile.id))
        .limit(1);

      expect(updatedProfile.publishedRepositories).toBe(1);
    });

    it('should throw NotFoundException when repository not found', async () => {
      const nonExistentRepoId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';
      const marketplaceItemId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';

      await expect(service.markRepositoryAsPublished(nonExistentRepoId, marketplaceItemId)).rejects.toThrow(NotFoundException);
      await expect(service.markRepositoryAsPublished(nonExistentRepoId, marketplaceItemId)).rejects.toThrow('Repository not found');
    });
  });

  describe('unmarkRepositoryAsPublished', () => {
    let repositoryId: string;

    beforeEach(async () => {
      const [repo] = await testDb.insert(giteaRepositories).values({
        giteaProfileId: testData.giteaProfile.id,
        giteaRepoId: 123,
        name: 'test-repo',
        fullName: 'testuser/test-repo',
        description: 'A test repository',
        visibility: 'public',
        isFork: false,
        isTemplate: false,
        isArchived: false,
        isEmpty: false,
        size: 1024,
        starsCount: 5,
        forksCount: 2,
        watchersCount: 3,
        openIssuesCount: 1,
        defaultBranch: 'main',
        language: 'TypeScript',
        topics: [],
        htmlUrl: 'https://gitea.example.com/testuser/test-repo',
        cloneUrl: 'https://gitea.example.com/testuser/test-repo.git',
        sshUrl: '*********************:testuser/test-repo.git',
        hasMarketplaceMetadata: false,
        marketplaceMetadata: null,
        isPublished: true,
        marketplaceItemId: 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
        syncStatus: 'completed',
        lastSyncAt: new Date(),
        giteaCreatedAt: new Date('2024-01-01'),
        giteaUpdatedAt: new Date('2024-01-02'),
        giteaPushedAt: new Date('2024-01-03'),
      }).returning();

      repositoryId = repo.id;

      // Update profile to have 1 published repository
      await testDb
        .update(giteaProfiles)
        .set({ publishedRepositories: 1 })
        .where(eq(giteaProfiles.id, testData.giteaProfile.id));
    });

    it('should unmark repository as published successfully', async () => {
      const result = await service.unmarkRepositoryAsPublished(repositoryId);

      expect(result).toMatchObject({
        id: repositoryId,
        isPublished: false,
        marketplaceItemId: null,
      });

      // Verify in database
      const [updatedRepo] = await testDb
        .select()
        .from(giteaRepositories)
        .where(eq(giteaRepositories.id, repositoryId))
        .limit(1);

      expect(updatedRepo).toMatchObject({
        isPublished: false,
        marketplaceItemId: null,
      });

      // Verify profile stats were updated
      const [updatedProfile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.id, testData.giteaProfile.id))
        .limit(1);

      expect(updatedProfile.publishedRepositories).toBe(0);
    });

    it('should throw NotFoundException when repository not found', async () => {
      const nonExistentRepoId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

      await expect(service.unmarkRepositoryAsPublished(nonExistentRepoId)).rejects.toThrow(NotFoundException);
      await expect(service.unmarkRepositoryAsPublished(nonExistentRepoId)).rejects.toThrow('Repository not found');
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle repositories with null/undefined fields', async () => {
      const repoWithNulls = {
        ...mockGiteaRepo,
        description: null,
        language: null,
        pushed_at: null,
      };

      mockGiteaService.getUserRepositories.mockResolvedValue([repoWithNulls]);

      const result = await service.syncDeveloperRepositories(testData.testUser.id);

      expect(result.synced).toBe(1);

      // Verify repository was created with null values handled correctly
      const [repo] = await testDb
        .select()
        .from(giteaRepositories)
        .where(eq(giteaRepositories.giteaProfileId, testData.giteaProfile.id))
        .limit(1);

      expect(repo).toMatchObject({
        description: null,
        language: null,
        giteaPushedAt: null,
      });
    });

    it('should handle empty repository list', async () => {
      mockGiteaService.getUserRepositories.mockResolvedValue([]);

      const result = await service.syncDeveloperRepositories(testData.testUser.id);

      expect(result).toMatchObject({
        synced: 0,
        created: 0,
        updated: 0,
        errors: [],
      });

      // Verify profile stats were still updated
      const [updatedProfile] = await testDb
        .select()
        .from(giteaProfiles)
        .where(eq(giteaProfiles.id, testData.giteaProfile.id))
        .limit(1);

      expect(updatedProfile).toMatchObject({
        totalRepositories: 0,
        publicRepositories: 0,
        privateRepositories: 0,
        publishedRepositories: 0,
      });
    });

    it('should handle repositories with special characters in names', async () => {
      const specialRepo = {
        ...mockGiteaRepo,
        name: 'test-repo-with-special-chars_123',
        full_name: 'testuser/test-repo-with-special-chars_123',
      };

      mockGiteaService.getUserRepositories.mockResolvedValue([specialRepo]);

      const result = await service.syncDeveloperRepositories(testData.testUser.id);

      expect(result.synced).toBe(1);

      const [repo] = await testDb
        .select()
        .from(giteaRepositories)
        .where(eq(giteaRepositories.giteaProfileId, testData.giteaProfile.id))
        .limit(1);

      expect(repo).toMatchObject({
        name: 'test-repo-with-special-chars_123',
        fullName: 'testuser/test-repo-with-special-chars_123',
      });
    });
  });
});
