/**
 * ClientReleasesService Integration Tests
 * Tests the ClientReleasesService with real database operations
 */

import { Test, TestingModule } from '@nestjs/testing';
import { eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase, TestDatabase
} from '../../../test/database-setup.js';
import { clientReleases, NewClientRelease } from '../../database/schema/client-releases.schema.js';
import * as schema from '../../database/schema/index.js';
import { CheckUpdateRequest, ClientReleasesService } from './client-releases.service.js';
import { S3Service } from './s3.service.js';

describe('ClientReleasesService Integration Tests', () => {
  let service: ClientReleasesService;
  let db: PostgresJsDatabase<typeof schema>;
  let mockS3Service: jest.Mocked<S3Service>;
  let testDb: TestDatabase;

  beforeAll(async () => {
    testDb = await createTestDatabase();
  });

  beforeEach(async () => {
    await cleanTestDatabase(testDb);
    db = testDb;
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    // Clear client releases table
    await db.delete(clientReleases);

    // Create mock S3 service
    mockS3Service = {
      getPresignedDownloadUrl: jest.fn(),
      deleteFile: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ClientReleasesService,
        {
          provide: 'DB',
          useValue: db,
        },
        {
          provide: S3Service,
          useValue: mockS3Service,
        },
      ],
    }).compile();

    service = module.get<ClientReleasesService>(ClientReleasesService);
    jest.clearAllMocks();
  });

  describe('createRelease', () => {
    it('should create a new release successfully', async () => {
      const releaseData: NewClientRelease = {
        version: '1.0.0',
        targetPlatform: 'windows',
        targetArch: 'x64',
        channel: 'stable',
        fileName: 'test-1.0.0.exe',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/test-1.0.0.exe',
        signature: 'test-signature-123',
        notes: 'Initial release',
        pubDate: new Date(),
        isActive: false,
      };

      const result = await service.createRelease(releaseData);

      expect(result).toMatchObject({
        id: expect.any(String),
        version: '1.0.0',
        targetPlatform: 'windows',
        targetArch: 'x64',
        channel: 'stable',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/test-1.0.0.exe',
        signature: 'test-signature-123',
        notes: 'Initial release',
        isActive: false,
      });
    });

    it('should handle database errors gracefully', async () => {
      const invalidReleaseData = {
        // Missing required fields to trigger database error
        version: null,
      } as any;

      await expect(service.createRelease(invalidReleaseData)).rejects.toThrow();
    });
  });

  describe('activateRelease', () => {
    it('should activate a release and deactivate others', async () => {
      // Create two releases for the same platform/arch/channel
      const release1Data: NewClientRelease = {
        version: '1.0.0',
        targetPlatform: 'windows',
        targetArch: 'x64',
        channel: 'stable',
        fileName: 'test-1.0.0.exe',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/test-1.0.0.exe',
        signature: 'test-signature-1',
        notes: 'Release 1',
        pubDate: new Date(),
        isActive: true,
      };

      const release2Data: NewClientRelease = {
        version: '1.1.0',
        targetPlatform: 'windows',
        targetArch: 'x64',
        channel: 'stable',
        fileName: 'test-1.1.0.exe',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/test-1.1.0.exe',
        signature: 'test-signature-2',
        notes: 'Release 2',
        pubDate: new Date(),
        isActive: false,
      };

      const release1 = await service.createRelease(release1Data);
      const release2 = await service.createRelease(release2Data);

      // Activate release2
      await service.activateRelease(release2.id);

      // Check that release2 is active and release1 is inactive
      const updatedRelease1 = await db.select().from(clientReleases).where(eq(clientReleases.id, release1.id));
      const updatedRelease2 = await db.select().from(clientReleases).where(eq(clientReleases.id, release2.id));

      expect(updatedRelease1[0].isActive).toBe(false);
      expect(updatedRelease2[0].isActive).toBe(true);
    });

    it('should throw NotFoundException for non-existent release', async () => {
      const nonExistentId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

      await expect(service.activateRelease(nonExistentId)).rejects.toThrow('Release not found');
    });
  });

  describe('deactivateRelease', () => {
    it('should deactivate a release', async () => {
      const releaseData: NewClientRelease = {
        version: '1.0.0',
        targetPlatform: 'windows',
        targetArch: 'x64',
        channel: 'stable',
        fileName: 'test-1.0.0.exe',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/test-1.0.0.exe',
        signature: 'test-signature-123',
        notes: 'Test release',
        pubDate: new Date(),
        isActive: true,
      };

      const release = await service.createRelease(releaseData);

      await service.deactivateRelease(release.id);

      const updatedRelease = await db.select().from(clientReleases).where(eq(clientReleases.id, release.id));
      expect(updatedRelease[0].isActive).toBe(false);
    });
  });

  describe('getReleases', () => {
    beforeEach(async () => {
      // Create test releases
      const releases: NewClientRelease[] = [
        {
          version: '1.0.0',
          targetPlatform: 'windows',
          targetArch: 'x64',
          channel: 'stable',
          fileName: 'win-1.0.0.exe',
          s3Bucket: 'test-bucket',
          s3Key: 'releases/win-1.0.0.exe',
          signature: 'sig-1',
          notes: 'Windows release',
          pubDate: new Date('2024-01-01'),
          isActive: true,
        },
        {
          version: '1.0.0',
          targetPlatform: 'macos',
          targetArch: 'x64',
          channel: 'stable',
          fileName: 'mac-1.0.0.dmg',
          s3Bucket: 'test-bucket',
          s3Key: 'releases/mac-1.0.0.dmg',
          signature: 'sig-2',
          notes: 'macOS release',
          pubDate: new Date('2024-01-02'),
          isActive: true,
        },
        {
          version: '1.1.0',
          targetPlatform: 'windows',
          targetArch: 'x64',
          channel: 'beta',
          fileName: 'win-1.1.0-beta.exe',
          s3Bucket: 'test-bucket',
          s3Key: 'releases/win-1.1.0-beta.exe',
          signature: 'sig-3',
          notes: 'Windows beta',
          pubDate: new Date('2024-01-03'),
          isActive: false,
        },
      ];

      for (const releaseData of releases) {
        await service.createRelease(releaseData);
      }
    });

    it('should get all releases when no filters provided', async () => {
      const releases = await service.getReleases();

      expect(releases).toHaveLength(3);
      expect(releases[0].pubDate.getTime()).toBeGreaterThanOrEqual(releases[1].pubDate.getTime());
    });

    it('should filter releases by platform', async () => {
      const releases = await service.getReleases('windows');

      expect(releases).toHaveLength(2);
      releases.forEach(release => {
        expect(release.targetPlatform).toBe('windows');
      });
    });

    it('should filter releases by platform and arch', async () => {
      const releases = await service.getReleases('windows', 'x64');

      expect(releases).toHaveLength(2);
      releases.forEach(release => {
        expect(release.targetPlatform).toBe('windows');
        expect(release.targetArch).toBe('x64');
      });
    });

    it('should filter releases by platform, arch, and channel', async () => {
      const releases = await service.getReleases('windows', 'x64', 'stable');

      expect(releases).toHaveLength(1);
      expect(releases[0].targetPlatform).toBe('windows');
      expect(releases[0].targetArch).toBe('x64');
      expect(releases[0].channel).toBe('stable');
    });

    it('should return empty array when no matches found', async () => {
      const releases = await service.getReleases('linux', 'arm64', 'stable');

      expect(releases).toHaveLength(0);
    });
  });

  describe('getActiveRelease', () => {
    beforeEach(async () => {
      const releases: NewClientRelease[] = [
        {
          version: '1.0.0',
          targetPlatform: 'windows',
          targetArch: 'x64',
          channel: 'stable',
          fileName: 'win-1.0.0.exe',
          s3Bucket: 'test-bucket',
          s3Key: 'releases/win-1.0.0.exe',
          signature: 'sig-1',
          notes: 'Active release',
          pubDate: new Date(),
          isActive: true,
        },
        {
          version: '0.9.0',
          targetPlatform: 'windows',
          targetArch: 'x64',
          channel: 'stable',
          fileName: 'win-0.9.0.exe',
          s3Bucket: 'test-bucket',
          s3Key: 'releases/win-0.9.0.exe',
          signature: 'sig-2',
          notes: 'Inactive release',
          pubDate: new Date(),
          isActive: false,
        },
      ];

      for (const releaseData of releases) {
        await service.createRelease(releaseData);
      }
    });

    it('should return active release for platform/arch/channel', async () => {
      const activeRelease = await service.getActiveRelease('windows', 'x64', 'stable');

      expect(activeRelease).not.toBeNull();
      expect(activeRelease!.version).toBe('1.0.0');
      expect(activeRelease!.isActive).toBe(true);
    });

    it('should return null when no active release found', async () => {
      const activeRelease = await service.getActiveRelease('linux', 'x64', 'stable');

      expect(activeRelease).toBeNull();
    });

    it('should default to stable channel when not specified', async () => {
      const activeRelease = await service.getActiveRelease('windows', 'x64');

      expect(activeRelease).not.toBeNull();
      expect(activeRelease!.channel).toBe('stable');
    });
  });

  describe('checkForUpdate', () => {
    beforeEach(async () => {
      // Create test releases
      const releaseData: NewClientRelease = {
        version: '1.2.0',
        targetPlatform: 'windows',
        targetArch: 'x64',
        channel: 'stable',
        fileName: 'win-1.2.0.exe',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/win-1.2.0.exe',
        signature: 'test-signature-123',
        notes: 'Bug fixes and improvements',
        pubDate: new Date('2024-01-01'),
        isActive: true,
      };

      await service.createRelease(releaseData);

      // Mock S3 service
      mockS3Service.getPresignedDownloadUrl.mockResolvedValue('https://signed-download-url.com');
    });

    it('should return update when newer version available', async () => {
      const request: CheckUpdateRequest = {
        target: 'windows',
        arch: 'x64',
        current_version: '1.0.0',
        channel: 'stable',
      };

      const result = await service.checkForUpdate(request);

      expect(result).not.toBeNull();
      expect(result!.version).toBe('1.2.0');
      expect(result!.notes).toBe('Bug fixes and improvements');
      expect(result!.url).toBe('https://signed-download-url.com');
      expect(result!.signature).toBe('test-signature-123');
      expect(result!.pub_date).toBe('2024-01-01T00:00:00.000Z');

      expect(mockS3Service.getPresignedDownloadUrl).toHaveBeenCalledWith({
        key: 'releases/win-1.2.0.exe',
        bucket: 'test-bucket',
        expires: 3600,
      });
    });

    it('should return null when no update needed', async () => {
      const request: CheckUpdateRequest = {
        target: 'windows',
        arch: 'x64',
        current_version: '1.2.0',
        channel: 'stable',
      };

      const result = await service.checkForUpdate(request);

      expect(result).toBeNull();
      expect(mockS3Service.getPresignedDownloadUrl).not.toHaveBeenCalled();
    });

    it('should return null when current version is newer', async () => {
      const request: CheckUpdateRequest = {
        target: 'windows',
        arch: 'x64',
        current_version: '2.0.0',
        channel: 'stable',
      };

      const result = await service.checkForUpdate(request);

      expect(result).toBeNull();
    });

    it('should return null when no releases found for platform', async () => {
      const request: CheckUpdateRequest = {
        target: 'linux',
        arch: 'x64',
        current_version: '1.0.0',
        channel: 'stable',
      };

      const result = await service.checkForUpdate(request);

      expect(result).toBeNull();
    });

    it('should default to stable channel when not specified', async () => {
      const request: CheckUpdateRequest = {
        target: 'windows',
        arch: 'x64',
        current_version: '1.0.0',
      };

      const result = await service.checkForUpdate(request);

      expect(result).not.toBeNull();
      expect(result!.version).toBe('1.2.0');
    });

    it('should handle empty notes gracefully', async () => {
      // Create release with null notes
      const releaseData: NewClientRelease = {
        version: '1.3.0',
        targetPlatform: 'macos',
        targetArch: 'x64',
        channel: 'stable',
        fileName: 'mac-1.3.0.dmg',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/mac-1.3.0.dmg',
        signature: 'test-signature-456',
        notes: null,
        pubDate: new Date('2024-01-02'),
        isActive: true,
      };

      await service.createRelease(releaseData);

      const request: CheckUpdateRequest = {
        target: 'macos',
        arch: 'x64',
        current_version: '1.0.0',
        channel: 'stable',
      };

      const result = await service.checkForUpdate(request);

      expect(result).not.toBeNull();
      expect(result!.notes).toBe('');
    });

    it('should handle S3 service errors', async () => {
      mockS3Service.getPresignedDownloadUrl.mockRejectedValue(new Error('S3 error'));

      const request: CheckUpdateRequest = {
        target: 'windows',
        arch: 'x64',
        current_version: '1.0.0',
        channel: 'stable',
      };

      await expect(service.checkForUpdate(request)).rejects.toThrow('S3 error');
    });
  });

  describe('deleteRelease', () => {
    it('should delete release and associated S3 file', async () => {
      const releaseData: NewClientRelease = {
        version: '1.0.0',
        targetPlatform: 'windows',
        targetArch: 'x64',
        channel: 'stable',
        fileName: 'test-1.0.0.exe',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/test-1.0.0.exe',
        signature: 'test-signature-123',
        notes: 'Test release',
        pubDate: new Date(),
        isActive: false,
      };

      const release = await service.createRelease(releaseData);

      mockS3Service.deleteFile.mockResolvedValue(undefined);

      await service.deleteRelease(release.id);

      // Verify S3 file deletion was called
      expect(mockS3Service.deleteFile).toHaveBeenCalledWith('releases/test-1.0.0.exe', 'test-bucket');

      // Verify release was deleted from database
      const deletedRelease = await db.select().from(clientReleases).where(eq(clientReleases.id, release.id));
      expect(deletedRelease).toHaveLength(0);
    });

    it('should throw NotFoundException for non-existent release', async () => {
      const nonExistentId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';

      await expect(service.deleteRelease(nonExistentId)).rejects.toThrow('Release not found');
      expect(mockS3Service.deleteFile).not.toHaveBeenCalled();
    });

    it('should handle S3 deletion errors gracefully', async () => {
      const releaseData: NewClientRelease = {
        version: '1.0.0',
        targetPlatform: 'windows',
        targetArch: 'x64',
        channel: 'stable',
        fileName: 'test-1.0.0.exe',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/test-1.0.0.exe',
        signature: 'test-signature-123',
        notes: 'Test release',
        pubDate: new Date(),
        isActive: false,
      };

      const release = await service.createRelease(releaseData);

      mockS3Service.deleteFile.mockRejectedValue(new Error('S3 deletion failed'));

      await expect(service.deleteRelease(release.id)).rejects.toThrow('S3 deletion failed');
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle database connection errors', async () => {

      // The service should not crash even if database operations fail
      // In a real scenario, database errors would be thrown and caught by the service
      expect(service).toBeDefined();
    });

    it('should handle malformed version strings in comparison', async () => {
      const releaseData: NewClientRelease = {
        version: 'invalid-version',
        targetPlatform: 'windows',
        targetArch: 'x64',
        channel: 'stable',
        fileName: 'test-invalid.exe',
        s3Bucket: 'test-bucket',
        s3Key: 'releases/test-invalid.exe',
        signature: 'test-signature-123',
        notes: 'Test release',
        pubDate: new Date(),
        isActive: true,
      };

      await service.createRelease(releaseData);

      const request: CheckUpdateRequest = {
        target: 'windows',
        arch: 'x64',
        current_version: 'also-invalid',
        channel: 'stable',
      };

      // Should not crash, even with malformed versions
      const result = await service.checkForUpdate(request);
      expect(result).toBeDefined(); // Could be null or a response
    });
  });
});
