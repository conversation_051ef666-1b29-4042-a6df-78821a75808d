/**
 * Basic utility tests to verify Jest setup
 */

describe('Basic Jest Setup', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2);
  });

  it('should handle async operations', async () => {
    const result = await Promise.resolve('test');
    expect(result).toBe('test');
  });

  it('should work with mocks', () => {
    const mockFn = jest.fn();
    mockFn('test');
    expect(mockFn).toHaveBeenCalledWith('test');
  });
});

// Test utility functions
describe('Utility Functions', () => {
  describe('parseExpirationToSeconds', () => {
    function parseExpirationToSeconds(expiration: string): number {
      const match = expiration.match(/^(\d+)([smhd])$/);
      if (!match) {
        return 900; // Default to 15 minutes
      }

      const value = parseInt(match[1]);
      const unit = match[2];

      switch (unit) {
        case 's': return value;
        case 'm': return value * 60;
        case 'h': return value * 60 * 60;
        case 'd': return value * 60 * 60 * 24;
        default: return 900;
      }
    }

    it('should parse seconds correctly', () => {
      expect(parseExpirationToSeconds('30s')).toBe(30);
    });

    it('should parse minutes correctly', () => {
      expect(parseExpirationToSeconds('15m')).toBe(900);
    });

    it('should parse hours correctly', () => {
      expect(parseExpirationToSeconds('1h')).toBe(3600);
    });

    it('should parse days correctly', () => {
      expect(parseExpirationToSeconds('7d')).toBe(604800);
    });

    it('should return default for invalid format', () => {
      expect(parseExpirationToSeconds('invalid')).toBe(900);
    });
  });
});
