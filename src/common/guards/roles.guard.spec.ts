import { Test, TestingModule } from '@nestjs/testing';
import { Reflector } from '@nestjs/core';
import { ExecutionContext, ForbiddenException } from '@nestjs/common';
import { RolesGuard } from './roles.guard';
import { UserRole } from '../enums/user-role.enum';
import { ROLES_KEY } from '../decorators/roles.decorator';

describe('RolesGuard', () => {
  let guard: RolesGuard;
  let reflector: Reflector;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolesGuard,
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<RolesGuard>(RolesGuard);
    reflector = module.get<Reflector>(Reflector);
  });

  it('should be defined', () => {
    expect(guard).toBeDefined();
  });

  describe('canActivate', () => {
    let mockExecutionContext: Partial<ExecutionContext>;
    let mockRequest: any;

    beforeEach(() => {
      mockRequest = {};
      mockExecutionContext = {
        getHandler: jest.fn(),
        getClass: jest.fn(),
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: () => mockRequest,
        }),
      };
    });

    it('should return true when no roles are required', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(null);

      // Act
      const result = guard.canActivate(mockExecutionContext as ExecutionContext);

      // Assert
      expect(result).toBe(true);
      expect(reflector.getAllAndOverride).toHaveBeenCalledWith(ROLES_KEY, [
        mockExecutionContext.getHandler(),
        mockExecutionContext.getClass(),
      ]);
    });

    it('should return true when no roles are required (undefined)', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(undefined);

      // Act
      const result = guard.canActivate(mockExecutionContext as ExecutionContext);

      // Assert
      expect(result).toBe(true);
    });

    it('should throw ForbiddenException when user is not authenticated', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.ADMIN]);
      mockRequest.user = null;

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext as ExecutionContext))
        .toThrow(new ForbiddenException('User not authenticated'));
    });

    it('should throw ForbiddenException when user is undefined', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.ADMIN]);
      // mockRequest.user is undefined by default

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext as ExecutionContext))
        .toThrow(new ForbiddenException('User not authenticated'));
    });

    it('should return true when user has required role', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.ADMIN]);
      mockRequest.user = {
        id: 'user-1',
        roles: [UserRole.USER, UserRole.ADMIN],
      };

      // Act
      const result = guard.canActivate(mockExecutionContext as ExecutionContext);

      // Assert
      expect(result).toBe(true);
    });

    it('should return true when user has one of multiple required roles', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.ADMIN, UserRole.DEVELOPER]);
      mockRequest.user = {
        id: 'user-1',
        roles: [UserRole.USER, UserRole.DEVELOPER],
      };

      // Act
      const result = guard.canActivate(mockExecutionContext as ExecutionContext);

      // Assert
      expect(result).toBe(true);
    });

    it('should throw ForbiddenException when user does not have required role', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.ADMIN]);
      mockRequest.user = {
        id: 'user-1',
        roles: [UserRole.USER],
      };

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext as ExecutionContext))
        .toThrow(new ForbiddenException('Insufficient permissions. Required roles: admin'));
    });

    it('should throw ForbiddenException when user does not have any of multiple required roles', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.ADMIN, UserRole.SUPER_ADMIN]);
      mockRequest.user = {
        id: 'user-1',
        roles: [UserRole.USER, UserRole.DEVELOPER],
      };

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext as ExecutionContext))
        .toThrow(new ForbiddenException('Insufficient permissions. Required roles: admin, super_admin'));
    });

    it('should handle user with null roles array', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.ADMIN]);
      mockRequest.user = {
        id: 'user-1',
        roles: null,
      };

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext as ExecutionContext))
        .toThrow(new ForbiddenException('Insufficient permissions. Required roles: admin'));
    });

    it('should handle user with undefined roles array', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.ADMIN]);
      mockRequest.user = {
        id: 'user-1',
        // roles is undefined
      };

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext as ExecutionContext))
        .toThrow(new ForbiddenException('Insufficient permissions. Required roles: admin'));
    });

    it('should handle user with empty roles array', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.ADMIN]);
      mockRequest.user = {
        id: 'user-1',
        roles: [],
      };

      // Act & Assert
      expect(() => guard.canActivate(mockExecutionContext as ExecutionContext))
        .toThrow(new ForbiddenException('Insufficient permissions. Required roles: admin'));
    });

    it('should work with SUPER_ADMIN role', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.SUPER_ADMIN]);
      mockRequest.user = {
        id: 'user-1',
        roles: [UserRole.SUPER_ADMIN],
      };

      // Act
      const result = guard.canActivate(mockExecutionContext as ExecutionContext);

      // Assert
      expect(result).toBe(true);
    });

    it('should work with DEVELOPER role', () => {
      // Arrange
      jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue([UserRole.DEVELOPER]);
      mockRequest.user = {
        id: 'user-1',
        roles: [UserRole.USER, UserRole.DEVELOPER],
      };

      // Act
      const result = guard.canActivate(mockExecutionContext as ExecutionContext);

      // Assert
      expect(result).toBe(true);
    });
  });
});
