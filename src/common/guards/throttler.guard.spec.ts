import { ThrottlerBehindProxyGuard } from './throttler.guard';

describe('ThrottlerBehindProxyGuard', () => {
  let guard: ThrottlerBehindProxyGuard;

  beforeEach(() => {
    // Create a minimal instance just to test the getTracker method
    guard = Object.create(ThrottlerBehindProxyGuard.prototype);
  });

  describe('getTracker', () => {
    it('should return the first IP from ips array when available', async () => {
      const req = {
        ips: ['***********', '********'],
        ip: '127.0.0.1',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('***********');
    });

    it('should return the ip when ips array is empty', async () => {
      const req = {
        ips: [],
        ip: '127.0.0.1',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('127.0.0.1');
    });

    it('should return the ip when ips is not present', async () => {
      const req = {
        ip: '***********00',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('***********00');
    });

    it('should handle undefined ips gracefully', async () => {
      const req = {
        ips: undefined,
        ip: '********',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('********');
    });

    it('should handle null ips gracefully', async () => {
      const req = {
        ips: null,
        ip: '**********',
      };

      const tracker = await guard['getTracker'](req);
      expect(tracker).toBe('**********');
    });
  });

  describe('guard instantiation', () => {
    it('should be defined', () => {
      expect(guard).toBeDefined();
    });

    it('should extend ThrottlerGuard', () => {
      expect(guard).toBeInstanceOf(ThrottlerBehindProxyGuard);
    });
  });
});
