import { Reflector } from '@nestjs/core';
import { UserRole } from '../enums/user-role.enum';
import { Roles, ROLES_KEY } from './roles.decorator';

describe('Roles Decorator', () => {
  let reflector: Reflector;

  beforeEach(() => {
    reflector = new Reflector();
  });

  it('should be defined', () => {
    expect(Roles).toBeDefined();
    expect(ROLES_KEY).toBeDefined();
  });

  it('should have correct ROLES_KEY', () => {
    expect(ROLES_KEY).toBe('roles');
  });

  it('should set metadata for single role', () => {
    @Roles(UserRole.ADMIN)
    class TestClass {}

    const roles = reflector.get<UserRole[]>(ROLES_KEY, TestClass);
    expect(roles).toEqual([UserRole.ADMIN]);
  });

  it('should set metadata for multiple roles', () => {
    @Roles(UserRole.ADMIN, UserRole.USER)
    class TestClass {}

    const roles = reflector.get<UserRole[]>(ROLES_KEY, TestClass);
    expect(roles).toEqual([UserRole.ADMIN, UserRole.USER]);
  });

  it('should work with method decorators', () => {
    class TestClass {
      @Roles(UserRole.ADMIN)
      testMethod() {}
    }

    const roles = reflector.get<UserRole[]>(ROLES_KEY, TestClass.prototype.testMethod);
    expect(roles).toEqual([UserRole.ADMIN]);
  });

  it('should work with empty roles array', () => {
    @Roles()
    class TestClass {}

    const roles = reflector.get<UserRole[]>(ROLES_KEY, TestClass);
    expect(roles).toEqual([]);
  });

  it('should work with all user roles', () => {
    @Roles(UserRole.ADMIN, UserRole.USER, UserRole.DEVELOPER)
    class TestClass {}

    const roles = reflector.get<UserRole[]>(ROLES_KEY, TestClass);
    expect(roles).toContain(UserRole.ADMIN);
    expect(roles).toContain(UserRole.USER);
    expect(roles).toContain(UserRole.DEVELOPER);
    expect(roles).toHaveLength(3);
  });
});
