/**
 * Secure Logger Utility
 * Provides logging functions that automatically sanitize sensitive data
 */

import { Logger } from '@nestjs/common';

export class SecureLogger extends Logger {
  private static readonly SENSITIVE_FIELDS = [
    'password',
    'token',
    'secret',
    'key',
    'authorization',
    'auth',
    'credential',
    'private',
    'session',
    'cookie',
    'jwt',
    'refresh_token',
    'access_token',
    'api_key',
    'client_secret',
    'client_id',
  ];

  private static readonly DATABASE_ERROR_PATTERNS = [
    /Failed query:/,
    /params:/,
    /PostgresError:/,
    /QueryFailedError:/,
    /duplicate key value/,
    /violates foreign key constraint/,
    /invalid input syntax/,
    /relation ".*" does not exist/,
    /column ".*" does not exist/,
  ];

  /**
   * Sanitize an object by removing or masking sensitive fields
   */
  private sanitizeObject(obj: any): any {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }

    const sanitized = { ...obj };

    for (const [key, value] of Object.entries(sanitized)) {
      const lowerKey = key.toLowerCase();

      // Check if this is a sensitive field
      if (SecureLogger.SENSITIVE_FIELDS.some(field => lowerKey.includes(field))) {
        sanitized[key] = '[REDACTED]';
      } else if (value && typeof value === 'object') {
        sanitized[key] = this.sanitizeObject(value);
      }
    }

    return sanitized;
  }

  /**
   * Sanitize error messages to remove database query details
   */
  private sanitizeErrorMessage(message: string): string {
    if (process.env.NODE_ENV === 'production') {
      // In production, check for database error patterns
      for (const pattern of SecureLogger.DATABASE_ERROR_PATTERNS) {
        if (pattern.test(message)) {
          return 'Database operation failed';
        }
      }
    }

    return message;
  }

  /**
   * Log with automatic sanitization
   */
  log(message: any, context?: string): void {
    const sanitizedMessage = typeof message === 'string'
      ? this.sanitizeErrorMessage(message)
      : this.sanitizeObject(message);

    super.log(sanitizedMessage, context);
  }

  /**
   * Error log with automatic sanitization
   */
  error(message: any, trace?: string, context?: string): void {
    const sanitizedMessage = typeof message === 'string'
      ? this.sanitizeErrorMessage(message)
      : this.sanitizeObject(message);

    // In production, don't log stack traces for database errors
    let sanitizedTrace = trace;
    if (process.env.NODE_ENV === 'production' && trace) {
      for (const pattern of SecureLogger.DATABASE_ERROR_PATTERNS) {
        if (pattern.test(trace)) {
          sanitizedTrace = undefined;
          break;
        }
      }
    }

    super.error(sanitizedMessage, sanitizedTrace, context);
  }

  /**
   * Warn log with automatic sanitization
   */
  warn(message: any, context?: string): void {
    const sanitizedMessage = typeof message === 'string'
      ? this.sanitizeErrorMessage(message)
      : this.sanitizeObject(message);

    super.warn(sanitizedMessage, context);
  }

  /**
   * Debug log with automatic sanitization (only in development)
   */
  debug(message: any, context?: string): void {
    if (process.env.NODE_ENV === 'development') {
      const sanitizedMessage = typeof message === 'string'
        ? message // Allow full debug info in development
        : this.sanitizeObject(message);

      super.debug(sanitizedMessage, context);
    }
  }

  /**
   * Verbose log with automatic sanitization (only in development)
   */
  verbose(message: any, context?: string): void {
    if (process.env.NODE_ENV === 'development') {
      const sanitizedMessage = typeof message === 'string'
        ? message // Allow full verbose info in development
        : this.sanitizeObject(message);

      super.verbose(sanitizedMessage, context);
    }
  }

  /**
   * Log database operations safely
   */
  logDatabaseOperation(operation: string, table: string, success: boolean, error?: any): void {
    const logData = {
      operation,
      table,
      success,
      timestamp: new Date().toISOString(),
    };

    if (success) {
      this.log(`Database ${operation} on ${table} completed successfully`);
    } else {
      if (process.env.NODE_ENV === 'production') {
        this.error(`Database ${operation} on ${table} failed`);
      } else {
        this.error(`Database ${operation} on ${table} failed`, error?.message);
      }
    }
  }

  /**
   * Log authentication events safely
   */
  logAuthEvent(event: string, userId?: string, success: boolean = true, details?: any): void {
    const logData = {
      event,
      userId: userId ? `user-${userId.substring(0, 8)}***` : 'anonymous', // Partial user ID
      success,
      timestamp: new Date().toISOString(),
      details: this.sanitizeObject(details),
    };

    if (success) {
      this.log(`Auth event: ${event}`, JSON.stringify(logData));
    } else {
      this.warn(`Auth event failed: ${event}`, JSON.stringify(logData));
    }
  }

  /**
   * Log API requests safely
   */
  logApiRequest(method: string, url: string, statusCode: number, userId?: string, duration?: number): void {
    const logData = {
      method,
      url: this.sanitizeUrl(url),
      statusCode,
      userId: userId ? `user-${userId.substring(0, 8)}***` : 'anonymous',
      duration: duration ? `${duration}ms` : undefined,
      timestamp: new Date().toISOString(),
    };

    this.log(`API ${method} ${url} - ${statusCode}`, JSON.stringify(logData));
  }

  /**
   * Sanitize URLs to remove sensitive query parameters
   */
  private sanitizeUrl(url: string): string {
    try {
      const urlObj = new URL(url, 'http://localhost');

      // Remove sensitive query parameters
      const sensitiveParams = ['token', 'key', 'secret', 'password', 'auth'];
      for (const param of sensitiveParams) {
        if (urlObj.searchParams.has(param)) {
          urlObj.searchParams.set(param, '[REDACTED]');
        }
      }

      return urlObj.pathname + urlObj.search;
    } catch {
      // If URL parsing fails, just return the original
      return url;
    }
  }
}
