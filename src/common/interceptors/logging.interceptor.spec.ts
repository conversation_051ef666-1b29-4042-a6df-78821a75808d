import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext, CallHandler, Logger } from '@nestjs/common';
import { of } from 'rxjs';
import { LoggingInterceptor } from './logging.interceptor';

describe('LoggingInterceptor', () => {
  let interceptor: LoggingInterceptor;
  let mockLogger: jest.Mocked<Logger>;

  beforeEach(async () => {
    // Mock the Logger
    mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
      verbose: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [LoggingInterceptor],
    }).compile();

    interceptor = module.get<LoggingInterceptor>(LoggingInterceptor);
    
    // Replace the logger instance with our mock
    (interceptor as any).logger = mockLogger;
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
  });

  describe('intercept', () => {
    let mockExecutionContext: Partial<ExecutionContext>;
    let mockCallHandler: Partial<CallHandler>;
    let mockRequest: any;
    let mockResponse: any;

    beforeEach(() => {
      mockRequest = {
        method: 'GET',
        url: '/api/users',
        ip: '***********',
        get: jest.fn(),
      };

      mockResponse = {
        statusCode: 200,
        get: jest.fn(),
      };

      mockExecutionContext = {
        switchToHttp: jest.fn().mockReturnValue({
          getRequest: () => mockRequest,
          getResponse: () => mockResponse,
        }),
      };

      mockCallHandler = {
        handle: jest.fn().mockReturnValue(of({ data: 'test' })),
      };
    });

    it('should log the incoming request', (done) => {
      // Arrange
      mockRequest.get.mockReturnValue('Mozilla/5.0');

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        expect(mockLogger.log).toHaveBeenCalledWith(
          'GET /api/users - *********** - Mozilla/5.0'
        );
        done();
      });
    });

    it('should log the response with timing', (done) => {
      // Arrange
      mockRequest.get.mockReturnValue('Mozilla/5.0');
      mockResponse.get.mockReturnValue('1024');

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        expect(mockLogger.log).toHaveBeenCalledTimes(2);
        
        // Check the second log call (response log)
        const secondCall = mockLogger.log.mock.calls[1][0];
        expect(secondCall).toMatch(/GET \/api\/users 200 1024 - \d+ms - 192\.168\.1\.1 - Mozilla\/5\.0/);
        done();
      });
    });

    it('should handle missing User-Agent header', (done) => {
      // Arrange
      mockRequest.get.mockReturnValue(undefined);

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        expect(mockLogger.log).toHaveBeenCalledWith(
          'GET /api/users - *********** - '
        );
        done();
      });
    });

    it('should handle null User-Agent header', (done) => {
      // Arrange
      mockRequest.get.mockReturnValue(null);

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        expect(mockLogger.log).toHaveBeenCalledWith(
          'GET /api/users - *********** - '
        );
        done();
      });
    });

    it('should handle different HTTP methods', (done) => {
      // Arrange
      mockRequest.method = 'POST';
      mockRequest.url = '/api/users';
      mockRequest.get.mockReturnValue('PostmanRuntime/7.28.4');

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        expect(mockLogger.log).toHaveBeenCalledWith(
          'POST /api/users - *********** - PostmanRuntime/7.28.4'
        );
        done();
      });
    });

    it('should handle different status codes', (done) => {
      // Arrange
      mockRequest.get.mockReturnValue('curl/7.68.0');
      mockResponse.statusCode = 404;
      mockResponse.get.mockReturnValue('512');

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        const secondCall = mockLogger.log.mock.calls[1][0];
        expect(secondCall).toMatch(/GET \/api\/users 404 512 - \d+ms - 192\.168\.1\.1 - curl\/7\.68\.0/);
        done();
      });
    });

    it('should handle missing content-length header', (done) => {
      // Arrange
      mockRequest.get.mockReturnValue('Mozilla/5.0');
      mockResponse.get.mockReturnValue(undefined);

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        const secondCall = mockLogger.log.mock.calls[1][0];
        expect(secondCall).toMatch(/GET \/api\/users 200 undefined - \d+ms - 192\.168\.1\.1 - Mozilla\/5\.0/);
        done();
      });
    });

    it('should measure timing correctly', (done) => {
      // Arrange
      mockRequest.get.mockReturnValue('Mozilla/5.0');
      mockResponse.get.mockReturnValue('2048');

      // Mock Date.now to control timing
      const originalDateNow = Date.now;
      let callCount = 0;
      Date.now = jest.fn(() => {
        callCount++;
        if (callCount === 1) return 1000; // Start time
        return 1050; // End time (50ms later)
      });

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        const secondCall = mockLogger.log.mock.calls[1][0];
        expect(secondCall).toContain('50ms');
        
        // Restore Date.now
        Date.now = originalDateNow;
        done();
      });
    });

    it('should handle IPv6 addresses', (done) => {
      // Arrange
      mockRequest.ip = '2001:db8::1';
      mockRequest.get.mockReturnValue('Mozilla/5.0');

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        expect(mockLogger.log).toHaveBeenCalledWith(
          'GET /api/users - 2001:db8::1 - Mozilla/5.0'
        );
        done();
      });
    });

    it('should handle complex URLs with query parameters', (done) => {
      // Arrange
      mockRequest.url = '/api/users?page=1&limit=10&sort=name';
      mockRequest.get.mockReturnValue('Mozilla/5.0');

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe(() => {
        expect(mockLogger.log).toHaveBeenCalledWith(
          'GET /api/users?page=1&limit=10&sort=name - *********** - Mozilla/5.0'
        );
        done();
      });
    });

    it('should pass through the original observable', (done) => {
      // Arrange
      const testData = { id: 1, name: 'test' };
      mockCallHandler.handle = jest.fn().mockReturnValue(of(testData));
      mockRequest.get.mockReturnValue('Mozilla/5.0');

      // Act
      const result = interceptor.intercept(
        mockExecutionContext as ExecutionContext,
        mockCallHandler as CallHandler,
      );

      // Assert
      result.subscribe((data) => {
        expect(data).toEqual(testData);
        done();
      });
    });
  });
});
