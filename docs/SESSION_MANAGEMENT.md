# RSGlider Session & Device Management

## 🎯 Overview
Comprehensive session management system that distinguishes between web app and desktop app access, with proper licensing controls and bot session management.

## 🖥️ **Desktop App (Tauri) Management**

### Device Registration & Limits
- **Device fingerprinting**: Hardware-based unique identification
- **Subscription limits**: Based on user's subscription tier
- **Transfer capability**: Move license between devices
- **Bot session control**: Manage multiple bot instances per device

### Desktop Session Flow
```
1. User installs desktop app
2. App generates device fingerprint
3. POST /users/me/desktop/register
4. Check subscription limits
5. Register device or show existing devices
6. User can transfer from old device if needed
```

### Bot Session Management
- **Subscription-based limits**: Different tiers allow different bot counts
- **Device-specific**: Bots tied to specific desktop installations
- **Exclusive sessions**: One bot session can't run across multiple devices
- **Transfer capability**: Move running bots between registered devices

## 🌐 **Web App Management**

### Device Verification
- **New device detection**: Browser fingerprinting + IP analysis
- **Email verification**: Required for new devices/browsers
- **Trusted devices**: Option to trust devices for future logins
- **Session limits**: Independent from desktop app limits

### Web Session Flow
```
1. User logs in from new browser/device
2. System detects new device
3. Send email verification code
4. POST /users/me/web/verify-device
5. User can choose to trust device
6. Session established with appropriate limits
```

## 📊 **Session Limits by Subscription Tier**

### Free Tier
```yaml
webSessions:
  max: 3
  trustedDevices: 1
desktopSessions:
  max: 1
  transfersPerMonth: 1
botSessions:
  maxConcurrent: 1
  perDevice: 1
```

### Pro Tier
```yaml
webSessions:
  max: 3
  trustedDevices: 2
desktopSessions:
  max: 2
  transfersPerMonth: 3
botSessions:
  maxConcurrent: 3
  perDevice: 2
```

### Addon System
```yaml
# Additional sessions available for purchase
desktopAddon:
  pricePerDevice: "$10/month"
  maxAddons: 10
  transfersIncluded: 2
botAddon:
  pricePerBot: "$5/month"
  maxAddons: 25
  perDeviceLimit: 5
webSessions:
  fixed: 3  # Always 3, no addons available
```

## 🔐 **Security Features**

### Desktop App Security
- **Hardware fingerprinting**: Prevents VM/container abuse
- **App version tracking**: Ensure users run supported versions
- **Device transfer verification**: Email confirmation required
- **Bot session isolation**: Each bot runs in isolated context

### Web App Security
- **Device verification**: Email codes for new devices
- **Trusted device management**: Users control trusted devices
- **Session monitoring**: Track all active web sessions
- **Geographic anomaly detection**: Flag suspicious locations

## 🛠️ **API Endpoints Summary**

### Session Management
- `GET /users/me/sessions` - View all sessions (web, desktop, bot)
- `DELETE /users/me/sessions/{sessionId}` - Revoke specific session
- `POST /users/me/sessions/revoke-all` - Revoke all except current

### Desktop Management
- `POST /users/me/desktop/register` - Register new desktop installation
- `POST /users/me/desktop/{deviceId}/transfer` - Transfer to new device

### Web Device Management
- `POST /users/me/web/verify-device` - Verify new web device

### Bot Session Management
- `GET /users/me/bots/sessions` - View active bot sessions
- `POST /users/me/bots/sessions` - Start new bot session
- `DELETE /users/me/bots/sessions/{botSessionId}` - Stop bot session
- `POST /users/me/bots/sessions/{botSessionId}/transfer` - Transfer bot

## 🔄 **Session Lifecycle**

### Desktop App Lifecycle
```
Install → Register → Authenticate → Run Bots → Transfer/Revoke
```

### Web App Lifecycle
```
Login → Verify Device → Trust (Optional) → Use → Expire/Revoke
```

### Bot Session Lifecycle
```
Start → Configure → Run → Monitor → Stop/Transfer
```

## 📱 **Multi-Platform Scenarios**

### Scenario 1: Free User with Desktop + Web
- **Desktop**: 1 registered device, 1 active bot
- **Web**: 1 trusted device, 3 active sessions
- **Independent limits**: Web sessions don't affect desktop bot limits

### Scenario 2: Pro User with Multiple Devices
- **Desktop**: 2 registered devices, 3 active bots total
- **Web**: 2 trusted devices, 3 active sessions
- **Bot distribution**: Can run bots on either desktop device

### Scenario 3: Addon Purchase
- **Base Pro**: 2 desktops, 3 bots
- **With addons**: +2 desktop addons, +5 bot addons
- **Total capacity**: 4 desktops, 8 bots, 3 web sessions
- **Immediate effect**: New limits apply instantly

### Scenario 4: Device Transfer
- **Old device**: User's laptop breaks
- **Transfer process**: Email verification → New device registration → Bot sessions migrate
- **Seamless transition**: Minimal downtime for bot operations

## 🚨 **Enforcement Mechanisms**

### Desktop App Enforcement
- **Device limit**: Block new registrations when limit reached
- **Bot limit**: Prevent starting bots beyond subscription limit
- **Transfer limit**: Block transfers when monthly limit exceeded
- **Version enforcement**: Require minimum app version

### Web App Enforcement
- **Session limit**: Force logout of oldest sessions when limit reached
- **Device verification**: Block unverified devices
- **Geographic restrictions**: Optional IP-based restrictions
- **Rate limiting**: Prevent session abuse

This comprehensive session management system ensures proper licensing control while providing flexibility for legitimate use cases across both web and desktop platforms.
