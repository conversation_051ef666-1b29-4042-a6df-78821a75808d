# Integration Tests Quick Reference

## Basic Test Structure

```typescript
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase
} from '../../test/database-setup';

describe('YourService Integration Tests', () => {
  let service: YourService;
  let testDb: TestDatabase;
  let testData: any;

  beforeAll(async () => {
    testDb = await createTestDatabase();
    const module = await Test.createTestingModule({
      providers: [
        YourService,
        { provide: 'DB', useValue: testDb },
        // Mock external services only
      ],
    }).compile();
    service = module.get<YourService>(YourService);
  });

  beforeEach(async () => {
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });
});
```

## Essential Patterns

### ✅ Test Behavior, Not Implementation
```typescript
// Good: Test actual behavior
it('should update user profile successfully', async () => {
  const result = await service.updateProfile(userId, updateData);
  
  expect(result).toMatchObject({ firstName: 'Updated' });
  
  // Verify in database
  const [user] = await testDb.select().from(users).where(eq(users.id, userId));
  expect(user.name).toBe('Updated Name');
});
```

### ✅ Test Error Cases
```typescript
it('should throw NotFoundException for non-existent user', async () => {
  await expect(service.getUser('invalid-id'))
    .rejects.toThrow(NotFoundException);
});
```

### ✅ Verify Database State
```typescript
it('should deactivate user in database', async () => {
  await service.deactivateUser(userId);
  
  const [user] = await testDb.select().from(users).where(eq(users.id, userId));
  expect(user.isActive).toBe(false);
});
```

## What to Mock vs Not Mock

### ✅ Mock These
- External HTTP clients
- Email services
- File storage (S3, etc.)
- Third-party APIs
- Configuration (ConfigService)
- JWT services

### ❌ Don't Mock These
- Database operations
- Drizzle ORM queries
- Database connections
- Schema operations

## Common Test Patterns

### Password Hashing Verification
```typescript
expect(user.password).not.toBe('plaintext');
expect(user.password).toMatch(/^\$2b\$12\$/); // bcrypt format
```

### UUID Validation
```typescript
expect(result.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
```

### Timestamp Checks
```typescript
expect(result.createdAt).toBeTruthy();
expect(new Date(result.createdAt)).toBeInstanceOf(Date);
```

### Array Containment
```typescript
expect(result.roles).toEqual(expect.arrayContaining(['user', 'admin']));
```

### Partial Object Matching
```typescript
expect(result).toMatchObject({
  id: expect.any(String),
  email: '<EMAIL>',
  status: 'active',
});
```

## Device Testing Template

```typescript
const deviceTestCases = [
  {
    name: 'iOS mobile',
    data: {
      deviceName: 'iPhone 14',
      deviceType: 'mobile' as const,
      platform: 'iOS',
      deviceInfo: { model: 'iPhone 14', version: '16.0' },
    },
  },
  {
    name: 'Windows desktop',
    data: {
      deviceName: 'Windows PC',
      deviceType: 'desktop' as const,
      platform: 'Windows',
      deviceInfo: { version: '11', build: '22621' },
    },
  },
  // Add more platforms...
];

deviceTestCases.forEach(({ name, data }) => {
  it(`should register ${name} successfully`, async () => {
    const result = await service.registerDevice(userId, data);
    expect(result.platform).toBe(data.platform);
  });
});
```

## Complex Scenario Testing

### Testing State Changes
```typescript
it('should mark previous sessions as not current', async () => {
  // Setup: Create existing session
  await testDb.insert(userSessions).values({
    userId,
    isCurrent: true,
    // ... other fields
  });

  // Action: Login (creates new session)
  await service.loginUser(loginData);

  // Verify: Only one current session
  const sessions = await testDb.select().from(userSessions).where(eq(userSessions.userId, userId));
  const currentSessions = sessions.filter(s => s.isCurrent);
  expect(currentSessions).toHaveLength(1);
});
```

### Testing Concurrent Operations
```typescript
it('should handle concurrent operations', async () => {
  const promises = [
    service.assignRole(userId, roleId),
    service.assignRole(userId, roleId), // Duplicate
  ];

  const results = await Promise.allSettled(promises);
  const successes = results.filter(r => r.status === 'fulfilled');
  const failures = results.filter(r => r.status === 'rejected');

  expect(successes).toHaveLength(1);
  expect(failures).toHaveLength(1);
});
```

## Debugging Tips

### Database State Inspection
```typescript
const debugUsers = async () => {
  const users = await testDb.select().from(users);
  console.log('Users:', users.map(u => ({ id: u.id, email: u.email })));
};
```

### Enable SQL Logging
```bash
DEBUG_SQL=true pnpm run test
```

### Use Snapshots for Complex Objects
```typescript
expect(result).toMatchSnapshot({
  id: expect.any(String),
  createdAt: expect.any(String),
});
```

## Running Tests

```bash
# Single test file
pnpm run test src/users/users.service.integration.spec.ts

# All integration tests
pnpm run test --testNamePattern="Integration Tests"

# With coverage
pnpm run test:cov

# Watch mode
pnpm run test:watch
```

## Common Gotchas

### 1. Always Clean Database
```typescript
beforeEach(async () => {
  await cleanTestDatabase(testDb); // Always clean first
  testData = await seedTestDatabase(testDb);
});
```

### 2. Use Valid UUIDs in Tests
```typescript
const validUuid = 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a55';
// Don't use: 'invalid-id'
```

### 3. Handle Async Operations
```typescript
// Wait for all promises
await Promise.all(promises);

// Or use Promise.allSettled for mixed results
const results = await Promise.allSettled(promises);
```

### 4. Reset Mocks Between Tests
```typescript
beforeEach(async () => {
  jest.clearAllMocks(); // Reset mock call counts
});
```

## Performance Considerations

### Bulk Operations
```typescript
it('should handle bulk operations efficiently', async () => {
  const startTime = Date.now();
  
  await Promise.all(Array.from({ length: 100 }, (_, i) => 
    service.createUser({ email: `user${i}@example.com` })
  ));
  
  const duration = Date.now() - startTime;
  expect(duration).toBeLessThan(5000); // 5 seconds
});
```

### Database Queries
- Use `limit(1)` for single record queries
- Use `select()` with specific fields when possible
- Test query performance with realistic data volumes

This quick reference covers the most common patterns you'll use when writing integration tests with Drizzle ORM and real PostgreSQL databases.
