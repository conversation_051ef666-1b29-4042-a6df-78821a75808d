# Integration Testing Guide with Drizzle ORM

This guide outlines best practices for writing integration tests using Drizzle ORM with real PostgreSQL test databases.

## Overview

Integration tests verify that your services work correctly with real database operations, providing confidence that your application behaves correctly in production. Unlike unit tests with mocks, integration tests use actual database connections and operations.

## Why Integration Tests Over Complex Mocking?

### Problems with Heavy Mocking
- **Brittle tests**: Complex mock chains break easily when implementation changes
- **False confidence**: Mocks may not reflect real database behavior
- **Maintenance overhead**: Keeping mocks in sync with actual APIs
- **TypeScript complexity**: Fighting type system with mock assertions

### Benefits of Integration Tests
- **Real behavior**: Tests actual database operations and constraints
- **Reliable**: Catches real issues like SQL syntax errors, constraint violations
- **Maintainable**: Simple, readable test code
- **Production confidence**: Tests the actual code path used in production

## Test Database Setup

Our test setup uses isolated PostgreSQL databases for each test run:

```typescript
// test/database-setup.ts provides:
- createTestDatabase(): Creates isolated test DB with migrations
- cleanTestDatabase(): Cleans all data between tests
- seedTestDatabase(): Seeds with standard test data
- closeTestDatabase(): Cleanup after tests
```

### Key Features
- **Isolated databases**: Each test run gets a unique database
- **Real migrations**: Uses actual Drizzle migrations
- **Automatic cleanup**: Handles database lifecycle
- **Seeded data**: Consistent test data across tests

## Basic Integration Test Structure

```typescript
import { Test, TestingModule } from '@nestjs/testing';
import {
  cleanTestDatabase,
  closeTestDatabase,
  createTestDatabase,
  seedTestDatabase,
  TestDatabase
} from '../../test/database-setup';
import { YourService } from './your.service';

describe('YourService Integration Tests', () => {
  let service: YourService;
  let testDb: TestDatabase;
  let testData: any;

  beforeAll(async () => {
    // Create test database and NestJS module
    testDb = await createTestDatabase();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        YourService,
        {
          provide: 'DB',
          useValue: testDb,
        },
        // Mock only external services, not database
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockImplementation((key, defaultValue) => {
              const config = {
                'JWT_SECRET': 'test-secret',
                // ... other config
              };
              return config[key] || defaultValue;
            }),
          },
        },
      ],
    }).compile();

    service = module.get<YourService>(YourService);
  });

  beforeEach(async () => {
    // Clean database and seed fresh data for each test
    await cleanTestDatabase(testDb);
    testData = await seedTestDatabase(testDb);
  });

  afterAll(async () => {
    // Cleanup database connection
    await closeTestDatabase();
  });

  // Your tests here...
});
```

## Writing Effective Integration Tests

### 1. Test Real Behavior, Not Implementation

❌ **Bad**: Testing implementation details
```typescript
it('should call database.update with correct parameters', async () => {
  const spy = jest.spyOn(database, 'update');
  await service.updateUser(userId, data);
  expect(spy).toHaveBeenCalledWith(/* specific parameters */);
});
```

✅ **Good**: Testing actual behavior
```typescript
it('should update user profile successfully', async () => {
  const updateData = { name: 'Updated Name', bio: 'New bio' };

  const result = await service.updateProfile(testData.testUser.id, updateData);

  expect(result).toMatchObject({
    id: testData.testUser.id,
    firstName: 'Updated',
    lastName: 'Name',
    bio: 'New bio',
  });

  // Verify in database
  const [updatedUser] = await testDb
    .select()
    .from(users)
    .where(eq(users.id, testData.testUser.id))
    .limit(1);

  expect(updatedUser.name).toBe('Updated Name');
  expect(updatedUser.bio).toBe('New bio');
});
```

### 2. Test Both Success and Error Cases

```typescript
describe('updateProfile', () => {
  it('should update user profile successfully', async () => {
    // Test successful case
  });

  it('should throw NotFoundException for non-existent user', async () => {
    const nonExistentId = 'f0eebc99-9c0b-4ef8-bb6d-6bb9bd380a66';
    const updateData = { name: 'New Name' };

    await expect(service.updateProfile(nonExistentId, updateData))
      .rejects.toThrow(NotFoundException);
  });

  it('should throw ConflictException for duplicate email', async () => {
    const updateData = { email: testData.adminUser.email };

    await expect(service.updateProfile(testData.testUser.id, updateData))
      .rejects.toThrow(ConflictException);
    await expect(service.updateProfile(testData.testUser.id, updateData))
      .rejects.toThrow('Email already exists');
  });
});
```

### 3. Verify Database State Changes

Always verify that the database state matches expectations:

```typescript
it('should deactivate user successfully', async () => {
  const result = await service.deactivateUser(testData.testUser.id);

  expect(result).toEqual({ message: 'User deactivated successfully' });

  // Verify user was deactivated in database
  const [deactivatedUser] = await testDb
    .select()
    .from(users)
    .where(eq(users.id, testData.testUser.id))
    .limit(1);

  expect(deactivatedUser.isActive).toBe(false);
});
```

### 4. Test Complex Scenarios

```typescript
it('should set previous sessions to not current when logging in', async () => {
  // Create an existing session
  const existingSessionId = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11';
  await testDb.insert(userSessions).values({
    id: existingSessionId,
    userId: testData.testUser.id,
    platform: 'web',
    isActive: true,
    isCurrent: true,
    // ... other required fields
  });

  // Login user (should create new session and mark old as not current)
  await service.loginUser({
    email: testData.testUser.email,
    password: 'password123',
  });

  // Verify old session is no longer current
  const [existingSession] = await testDb
    .select()
    .from(userSessions)
    .where(eq(userSessions.id, existingSessionId))
    .limit(1);

  expect(existingSession.isCurrent).toBe(false);

  // Verify exactly one current session exists
  const currentSessions = await testDb
    .select()
    .from(userSessions)
    .where(eq(userSessions.userId, testData.testUser.id));

  const currentSessionsCount = currentSessions.filter(s => s.isCurrent).length;
  expect(currentSessionsCount).toBe(1);
});
```

## Best Practices

### 1. Use Descriptive Test Names
```typescript
// ❌ Bad
it('should work', async () => {});

// ✅ Good
it('should throw ConflictException when assigning already assigned role', async () => {});
```

### 2. Use toMatchObject for Partial Matching
```typescript
// ✅ Good - focuses on important fields
expect(result).toMatchObject({
  id: testData.testUser.id,
  email: testData.testUser.email,
  firstName: 'Test',
  lastName: 'User',
});

// ❌ Avoid exact equality for complex objects
expect(result).toEqual(expectedCompleteObject);
```

### 3. Test Data Setup
```typescript
describe('assignRole', () => {
  it('should assign role to user successfully', async () => {
    const result = await service.assignRole(testData.testUser.id, testData.adminRole.id);

    expect(result).toEqual({ message: 'Role assigned successfully' });

    // Verify role was assigned in database
    const userRoleRecords = await testDb
      .select()
      .from(userRoles)
      .where(eq(userRoles.userId, testData.testUser.id));

    const roleIds = userRoleRecords.map(ur => ur.roleId);
    expect(roleIds).toContain(testData.adminRole.id);
  });
});
```

### 4. Setup and Teardown for Complex Tests
```typescript
describe('removeRole', () => {
  beforeEach(async () => {
    // Setup: Assign admin role to test user for removal tests
    await service.assignRole(testData.testUser.id, testData.adminRole.id);
  });

  it('should remove role from user successfully', async () => {
    const result = await service.removeRole(testData.testUser.id, testData.adminRole.id);
    // ... test implementation
  });
});
```

## What to Mock vs What Not to Mock

### ✅ Mock External Services
- HTTP clients
- Email services
- File storage services
- Third-party APIs
- Configuration (ConfigService)

### ❌ Don't Mock Database Operations
- Drizzle ORM queries
- Database connections
- Schema operations
- Transactions

### Example: Proper Mocking
```typescript
const module: TestingModule = await Test.createTestingModule({
  providers: [
    AuthService,
    {
      provide: 'DB',
      useValue: testDb, // Real database
    },
    {
      provide: JwtService, // Mock external service
      useValue: {
        signAsync: jest.fn().mockImplementation((payload) =>
          Promise.resolve(`mock.jwt.token.${payload.sub}`)
        ),
      },
    },
    {
      provide: ConfigService, // Mock configuration
      useValue: {
        get: jest.fn().mockImplementation((key, defaultValue) => {
          const config = { 'JWT_SECRET': 'test-secret' };
          return config[key] || defaultValue;
        }),
      },
    },
  ],
}).compile();
```

## Running Integration Tests

```bash
# Run specific integration test file
pnpm run test src/users/users.service.integration.spec.ts

# Run all integration tests
pnpm run test --testNamePattern="Integration Tests"

# Run with coverage
pnpm run test:cov
```

## Common Patterns

### Testing Password Hashing
```typescript
it('should hash the password properly', async () => {
  const registerData = {
    email: '<EMAIL>',
    password: 'PlainTextPassword123!',
    name: 'Test User',
  };

  await service.registerUser(registerData);

  const [createdUser] = await testDb
    .select()
    .from(users)
    .where(eq(users.email, '<EMAIL>'))
    .limit(1);

  expect(createdUser.password).not.toBe('PlainTextPassword123!');
  expect(createdUser.password).toMatch(/^\$2b\$12\$/); // bcrypt hash format
});
```

### Testing Timestamps and UUIDs
```typescript
it('should create user with proper timestamps', async () => {
  const result = await service.createUser(userData);

  expect(result.createdAt).toBeTruthy();
  expect(result.updatedAt).toBeTruthy();
  expect(result.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/);
});
```

## Advanced Testing Patterns

### Testing Transactions
```typescript
it('should rollback transaction on error', async () => {
  // This tests that database transactions work correctly
  const invalidData = { email: 'invalid-email-format' };

  await expect(service.createUserWithProfile(invalidData))
    .rejects.toThrow();

  // Verify no partial data was saved
  const users = await testDb.select().from(users);
  expect(users).toHaveLength(2); // Only seeded users
});
```

### Testing Concurrent Operations
```typescript
it('should handle concurrent role assignments correctly', async () => {
  const promises = [
    service.assignRole(testData.testUser.id, testData.adminRole.id),
    service.assignRole(testData.testUser.id, testData.adminRole.id),
  ];

  // One should succeed, one should fail with ConflictException
  const results = await Promise.allSettled(promises);

  const successes = results.filter(r => r.status === 'fulfilled');
  const failures = results.filter(r => r.status === 'rejected');

  expect(successes).toHaveLength(1);
  expect(failures).toHaveLength(1);
});
```

### Testing Complex Queries
```typescript
it('should filter users by multiple criteria', async () => {
  // Create additional test data
  await testDb.insert(users).values([
    { email: '<EMAIL>', isActive: true, roles: ['user'] },
    { email: '<EMAIL>', isActive: false, roles: ['user'] },
    { email: '<EMAIL>', isActive: true, roles: ['admin'] },
  ]);

  const result = await service.adminListUsers({
    status: 'active',
    role: 'user',
    search: 'active',
  });

  expect(result.users).toHaveLength(1);
  expect(result.users[0].email).toBe('<EMAIL>');
});
```

### Testing Soft Deletes
```typescript
it('should soft delete user and exclude from queries', async () => {
  await service.deleteUser(testData.testUser.id);

  // Verify user is marked as deleted but still in database
  const [deletedUser] = await testDb
    .select()
    .from(users)
    .where(eq(users.id, testData.testUser.id))
    .limit(1);

  expect(deletedUser.deletedAt).toBeTruthy();

  // Verify user is excluded from normal queries
  const activeUsers = await service.getActiveUsers();
  const userIds = activeUsers.map(u => u.id);
  expect(userIds).not.toContain(testData.testUser.id);
});
```

## Testing Device Management (Multi-Platform)

```typescript
describe('Device Management Integration', () => {
  const deviceTestCases = [
    {
      name: 'iOS mobile device',
      deviceData: {
        deviceName: 'iPhone 14 Pro',
        deviceType: 'mobile' as const,
        platform: 'iOS',
        deviceInfo: { model: 'iPhone 14 Pro', version: '16.0', osVersion: 'iOS 16.0' },
      },
    },
    {
      name: 'Android mobile device',
      deviceData: {
        deviceName: 'Samsung Galaxy S23',
        deviceType: 'mobile' as const,
        platform: 'Android',
        deviceInfo: { model: 'SM-S911B', version: '13', manufacturer: 'Samsung' },
      },
    },
    {
      name: 'Windows desktop',
      deviceData: {
        deviceName: 'Windows Desktop',
        deviceType: 'desktop' as const,
        platform: 'Windows',
        deviceInfo: { version: '11', build: '22621', architecture: 'x64' },
      },
    },
    {
      name: 'macOS desktop',
      deviceData: {
        deviceName: 'MacBook Pro',
        deviceType: 'desktop' as const,
        platform: 'macOS',
        deviceInfo: { model: 'MacBook Pro 16-inch', version: '13.0', chip: 'M2 Pro' },
      },
    },
    {
      name: 'Linux desktop',
      deviceData: {
        deviceName: 'Ubuntu Workstation',
        deviceType: 'desktop' as const,
        platform: 'Linux',
        deviceInfo: { distribution: 'Ubuntu', version: '22.04 LTS', kernel: '5.15.0' },
      },
    },
    {
      name: 'Web browser',
      deviceData: {
        deviceName: 'Chrome Browser',
        deviceType: 'web' as const,
        platform: 'Web',
        deviceInfo: {
          browser: 'Chrome',
          version: '*********',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          os: 'Windows'
        },
      },
    },
  ];

  deviceTestCases.forEach(({ name, deviceData }) => {
    it(`should register ${name} successfully`, async () => {
      const result = await service.registerDevice(testData.testUser.id, deviceData);

      expect(result).toMatchObject({
        deviceName: deviceData.deviceName,
        deviceType: deviceData.deviceType,
        platform: deviceData.platform,
        isTrusted: false,
      });

      // Verify in database
      const [device] = await testDb
        .select()
        .from(devices)
        .where(eq(devices.id, result.id))
        .limit(1);

      expect(device.deviceInfo).toEqual(deviceData.deviceInfo);
    });
  });
});
```

## Performance Testing

```typescript
describe('Performance Tests', () => {
  it('should handle bulk user operations efficiently', async () => {
    const startTime = Date.now();

    // Create 100 users
    const userPromises = Array.from({ length: 100 }, (_, i) =>
      service.createUser({
        email: `user${i}@example.com`,
        name: `User ${i}`,
        password: 'password123',
      })
    );

    await Promise.all(userPromises);

    const endTime = Date.now();
    const duration = endTime - startTime;

    // Should complete within reasonable time (adjust based on your requirements)
    expect(duration).toBeLessThan(5000); // 5 seconds

    // Verify all users were created
    const allUsers = await testDb.select().from(users);
    expect(allUsers).toHaveLength(102); // 100 + 2 seeded users
  });
});
```

## Debugging Integration Tests

### 1. Database State Inspection
```typescript
it('should debug database state', async () => {
  // Add debugging helpers
  const debugUsers = async () => {
    const users = await testDb.select().from(users);
    console.log('Current users:', users.map(u => ({ id: u.id, email: u.email })));
  };

  await debugUsers(); // Before operation
  await service.someOperation();
  await debugUsers(); // After operation
});
```

### 2. SQL Query Logging
Enable query logging in test database setup:
```typescript
// In test/database-setup.ts
testDbInstance = drizzle(testClient, {
  schema,
  logger: process.env.NODE_ENV === 'test' && process.env.DEBUG_SQL === 'true'
});
```

### 3. Test Data Snapshots
```typescript
it('should match expected data snapshot', async () => {
  const result = await service.getUserProfile(testData.testUser.id);

  // Use Jest snapshots for complex objects
  expect(result).toMatchSnapshot({
    id: expect.any(String),
    createdAt: expect.any(String),
    updatedAt: expect.any(String),
  });
});
```

## Troubleshooting Common Issues

### 1. Test Database Connection Issues
```bash
# Check if PostgreSQL is running
pg_isready -h localhost -p 5432

# Check database permissions
psql -h localhost -U rsglider -d postgres -c "SELECT 1"
```

### 2. Migration Issues
```typescript
// In test setup, handle migration errors gracefully
try {
  await migrate(testDbInstance, { migrationsFolder: './src/database/migrations' });
} catch (error) {
  console.warn('Migration error (might be expected):', error.message);
  // Continue with manual schema setup if needed
}
```

### 3. Test Data Conflicts
```typescript
beforeEach(async () => {
  // Always clean before seeding to avoid conflicts
  await cleanTestDatabase(testDb);
  testData = await seedTestDatabase(testDb);

  // Reset any global state
  jest.clearAllMocks();
});
```

This comprehensive approach provides reliable, maintainable tests that give you confidence in your application's behavior with real database operations.
