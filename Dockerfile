# Development Dockerfile for RSGlider API
FROM node:22-alpine

# Install system dependencies including build tools for native modules
RUN apk add --no-cache \
    curl \
    python3 \
    make \
    g++ \
    linux-headers \
    git \
    bash

# Install pnpm globally
RUN corepack enable && corepack prepare pnpm@latest --activate

# Node user already exists in the base image, just ensure proper permissions

# Create app directory and set ownership
WORKDIR /app
RUN chown -R node:node /app

# Switch to node user
USER node

# Copy package files first for better caching
COPY --chown=node:node package.json pnpm-lock.yaml ./

# Install dependencies (this will be cached if package.json doesn't change)
RUN pnpm install --shamefully-hoist

# Copy source code (excluding node_modules via .dockerignore)
COPY --chown=node:node . .

# Create uploads directory
RUN mkdir -p uploads

# Expose port and debug port
EXPOSE 3000 9229

# Default command for development
CMD ["pnpm", "run", "start:dev"]
